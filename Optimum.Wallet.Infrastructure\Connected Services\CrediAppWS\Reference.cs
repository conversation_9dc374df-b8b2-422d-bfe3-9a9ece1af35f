﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Optimum.Wallet.Infrastructure.CrediAppWS
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap")]
    public interface CrediAppServiceSoap
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F4_GetCardList", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListResponse> F4_GetCardListAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F5_GetBalance", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceResponse> F5_GetBalanceAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F6_GetStatementList", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListResponse> F6_GetStatementListAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F8_GetCurrentStatementTransaction", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionResponse> F8_GetCurrentStatementTransactionAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F10_StopCard_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawResponse> F10_StopCard_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F13_PayMyCardPayment_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawResponse> F13_PayMyCardPayment_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/External_Transfer_Funds_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawResponse> External_Transfer_Funds_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F16A_ActivatEstatement_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawResponse> F16A_ActivatEstatement_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F18_UpdateMobileNumber_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawResponse> F18_UpdateMobileNumber_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F19_UpdateEmail_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawResponse> F19_UpdateEmail_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F25_GetPersonalData_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawResponse> F25_GetPersonalData_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F27_CardHolderVerification_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawResponse> F27_CardHolderVerification_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F29_GetWalletsBalances", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesResponse> F29_GetWalletsBalancesAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F30_VerifyTransferAmount", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountResponse> F30_VerifyTransferAmountAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F31_ConfirmTransferAmount", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountResponse> F31_ConfirmTransferAmountAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetWalletStatementTransaction", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionResponse> GetWalletStatementTransactionAsync(Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F23_LoyFppProg", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgResponse> F23_LoyFppProgAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F24_LoyThameen", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenResponse> F24_LoyThameenAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F26_GetSmsData", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataResponse> F26_GetSmsDataAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F3_Login_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawResponse> F3_Login_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F7_GetPreviousStatementTransaction_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawResponse> F7_GetPreviousStatementTransaction_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F8_GetCurrentStatementTransaction_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawResponse> F8_GetCurrentStatementTransaction_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/GetStatementTransaction", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionResponse> GetStatementTransactionAsync(Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F9_GetHoldStatementTransaction_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawResponse> F9_GetHoldStatementTransaction_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F16B_DisablEstatement_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawResponse> F16B_DisablEstatement_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F17_SetSuppLimit_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawResponse> F17_SetSuppLimit_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F20_UpdateDOB_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawResponse> F20_UpdateDOB_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F23_LoyFppProg_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawResponse> F23_LoyFppProg_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F24_LoyThameenA_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawResponse> F24_LoyThameenA_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F24_LoyThameenB_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawResponse> F24_LoyThameenB_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/F28_PINBySMS_Raw", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawResponse> F28_PINBySMS_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/RunWebService", ReplyAction="*")]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceResponse> RunWebServiceAsync(Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F4_GetCardListRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F4_GetCardList", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListRequestBody Body;
        
        public F4_GetCardListRequest()
        {
        }
        
        public F4_GetCardListRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F4_GetCardListRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        public F4_GetCardListRequestBody()
        {
        }
        
        public F4_GetCardListRequestBody(string pCpr)
        {
            this.pCpr = pCpr;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F4_GetCardListResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F4_GetCardListResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListResponseBody Body;
        
        public F4_GetCardListResponse()
        {
        }
        
        public F4_GetCardListResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F4_GetCardListResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F4_GetCardListResult;
        
        public F4_GetCardListResponseBody()
        {
        }
        
        public F4_GetCardListResponseBody(string F4_GetCardListResult)
        {
            this.F4_GetCardListResult = F4_GetCardListResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F5_GetBalanceRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F5_GetBalance", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceRequestBody Body;
        
        public F5_GetBalanceRequest()
        {
        }
        
        public F5_GetBalanceRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F5_GetBalanceRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string mainCardBalanceJson;
        
        public F5_GetBalanceRequestBody()
        {
        }
        
        public F5_GetBalanceRequestBody(string pCpr, string pCardNumber, string mainCardBalanceJson)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
            this.mainCardBalanceJson = mainCardBalanceJson;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F5_GetBalanceResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F5_GetBalanceResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceResponseBody Body;
        
        public F5_GetBalanceResponse()
        {
        }
        
        public F5_GetBalanceResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F5_GetBalanceResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F5_GetBalanceResult;
        
        public F5_GetBalanceResponseBody()
        {
        }
        
        public F5_GetBalanceResponseBody(string F5_GetBalanceResult)
        {
            this.F5_GetBalanceResult = F5_GetBalanceResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F6_GetStatementListRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F6_GetStatementList", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListRequestBody Body;
        
        public F6_GetStatementListRequest()
        {
        }
        
        public F6_GetStatementListRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F6_GetStatementListRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        public F6_GetStatementListRequestBody()
        {
        }
        
        public F6_GetStatementListRequestBody(string pCpr, string pCardNumber)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F6_GetStatementListResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F6_GetStatementListResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListResponseBody Body;
        
        public F6_GetStatementListResponse()
        {
        }
        
        public F6_GetStatementListResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F6_GetStatementListResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F6_GetStatementListResult;
        
        public F6_GetStatementListResponseBody()
        {
        }
        
        public F6_GetStatementListResponseBody(string F6_GetStatementListResult)
        {
            this.F6_GetStatementListResult = F6_GetStatementListResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F8_GetCurrentStatementTransactionRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F8_GetCurrentStatementTransaction", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionRequestBody Body;
        
        public F8_GetCurrentStatementTransactionRequest()
        {
        }
        
        public F8_GetCurrentStatementTransactionRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F8_GetCurrentStatementTransactionRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        public F8_GetCurrentStatementTransactionRequestBody()
        {
        }
        
        public F8_GetCurrentStatementTransactionRequestBody(string pCpr, string pCardNumber)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F8_GetCurrentStatementTransactionResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F8_GetCurrentStatementTransactionResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionResponseBody Body;
        
        public F8_GetCurrentStatementTransactionResponse()
        {
        }
        
        public F8_GetCurrentStatementTransactionResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F8_GetCurrentStatementTransactionResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F8_GetCurrentStatementTransactionResult;
        
        public F8_GetCurrentStatementTransactionResponseBody()
        {
        }
        
        public F8_GetCurrentStatementTransactionResponseBody(string F8_GetCurrentStatementTransactionResult)
        {
            this.F8_GetCurrentStatementTransactionResult = F8_GetCurrentStatementTransactionResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F10_StopCard_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F10_StopCard_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawRequestBody Body;
        
        public F10_StopCard_RawRequest()
        {
        }
        
        public F10_StopCard_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F10_StopCard_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        public F10_StopCard_RawRequestBody()
        {
        }
        
        public F10_StopCard_RawRequestBody(string pCpr, string pCardNumber)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F10_StopCard_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F10_StopCard_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawResponseBody Body;
        
        public F10_StopCard_RawResponse()
        {
        }
        
        public F10_StopCard_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F10_StopCard_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F10_StopCard_RawResult;
        
        public F10_StopCard_RawResponseBody()
        {
        }
        
        public F10_StopCard_RawResponseBody(string F10_StopCard_RawResult)
        {
            this.F10_StopCard_RawResult = F10_StopCard_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F13_PayMyCardPayment_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F13_PayMyCardPayment_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawRequestBody Body;
        
        public F13_PayMyCardPayment_RawRequest()
        {
        }
        
        public F13_PayMyCardPayment_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F13_PayMyCardPayment_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pTrxAmount;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string pReferenceNbr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string fromCAID;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string transaction_source;
        
        public F13_PayMyCardPayment_RawRequestBody()
        {
        }
        
        public F13_PayMyCardPayment_RawRequestBody(string pCardNumber, string pTrxAmount, string pCpr, string pReferenceNbr, string fromCAID, string transaction_source)
        {
            this.pCardNumber = pCardNumber;
            this.pTrxAmount = pTrxAmount;
            this.pCpr = pCpr;
            this.pReferenceNbr = pReferenceNbr;
            this.fromCAID = fromCAID;
            this.transaction_source = transaction_source;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F13_PayMyCardPayment_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F13_PayMyCardPayment_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawResponseBody Body;
        
        public F13_PayMyCardPayment_RawResponse()
        {
        }
        
        public F13_PayMyCardPayment_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F13_PayMyCardPayment_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F13_PayMyCardPayment_RawResult;
        
        public F13_PayMyCardPayment_RawResponseBody()
        {
        }
        
        public F13_PayMyCardPayment_RawResponseBody(string F13_PayMyCardPayment_RawResult)
        {
            this.F13_PayMyCardPayment_RawResult = F13_PayMyCardPayment_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class External_Transfer_Funds_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="External_Transfer_Funds_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawRequestBody Body;
        
        public External_Transfer_Funds_RawRequest()
        {
        }
        
        public External_Transfer_Funds_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class External_Transfer_Funds_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string fromCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pTrxAmount;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string fromCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string toCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string toAccount;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string targetDetination;
        
        public External_Transfer_Funds_RawRequestBody()
        {
        }
        
        public External_Transfer_Funds_RawRequestBody(string fromCardNumber, string pTrxAmount, string fromCpr, string toCpr, string toAccount, string targetDetination)
        {
            this.fromCardNumber = fromCardNumber;
            this.pTrxAmount = pTrxAmount;
            this.fromCpr = fromCpr;
            this.toCpr = toCpr;
            this.toAccount = toAccount;
            this.targetDetination = targetDetination;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class External_Transfer_Funds_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="External_Transfer_Funds_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawResponseBody Body;
        
        public External_Transfer_Funds_RawResponse()
        {
        }
        
        public External_Transfer_Funds_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class External_Transfer_Funds_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string External_Transfer_Funds_RawResult;
        
        public External_Transfer_Funds_RawResponseBody()
        {
        }
        
        public External_Transfer_Funds_RawResponseBody(string External_Transfer_Funds_RawResult)
        {
            this.External_Transfer_Funds_RawResult = External_Transfer_Funds_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F16A_ActivatEstatement_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F16A_ActivatEstatement_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawRequestBody Body;
        
        public F16A_ActivatEstatement_RawRequest()
        {
        }
        
        public F16A_ActivatEstatement_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F16A_ActivatEstatement_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pEmailAddress;
        
        public F16A_ActivatEstatement_RawRequestBody()
        {
        }
        
        public F16A_ActivatEstatement_RawRequestBody(string pCpr, string pEmailAddress)
        {
            this.pCpr = pCpr;
            this.pEmailAddress = pEmailAddress;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F16A_ActivatEstatement_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F16A_ActivatEstatement_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawResponseBody Body;
        
        public F16A_ActivatEstatement_RawResponse()
        {
        }
        
        public F16A_ActivatEstatement_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F16A_ActivatEstatement_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F16A_ActivatEstatement_RawResult;
        
        public F16A_ActivatEstatement_RawResponseBody()
        {
        }
        
        public F16A_ActivatEstatement_RawResponseBody(string F16A_ActivatEstatement_RawResult)
        {
            this.F16A_ActivatEstatement_RawResult = F16A_ActivatEstatement_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F18_UpdateMobileNumber_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F18_UpdateMobileNumber_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawRequestBody Body;
        
        public F18_UpdateMobileNumber_RawRequest()
        {
        }
        
        public F18_UpdateMobileNumber_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F18_UpdateMobileNumber_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pMobile;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string pHome;
        
        public F18_UpdateMobileNumber_RawRequestBody()
        {
        }
        
        public F18_UpdateMobileNumber_RawRequestBody(string pCpr, string pMobile, string pHome)
        {
            this.pCpr = pCpr;
            this.pMobile = pMobile;
            this.pHome = pHome;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F18_UpdateMobileNumber_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F18_UpdateMobileNumber_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawResponseBody Body;
        
        public F18_UpdateMobileNumber_RawResponse()
        {
        }
        
        public F18_UpdateMobileNumber_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F18_UpdateMobileNumber_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F18_UpdateMobileNumber_RawResult;
        
        public F18_UpdateMobileNumber_RawResponseBody()
        {
        }
        
        public F18_UpdateMobileNumber_RawResponseBody(string F18_UpdateMobileNumber_RawResult)
        {
            this.F18_UpdateMobileNumber_RawResult = F18_UpdateMobileNumber_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F19_UpdateEmail_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F19_UpdateEmail_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawRequestBody Body;
        
        public F19_UpdateEmail_RawRequest()
        {
        }
        
        public F19_UpdateEmail_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F19_UpdateEmail_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pEmail;
        
        public F19_UpdateEmail_RawRequestBody()
        {
        }
        
        public F19_UpdateEmail_RawRequestBody(string pCpr, string pEmail)
        {
            this.pCpr = pCpr;
            this.pEmail = pEmail;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F19_UpdateEmail_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F19_UpdateEmail_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawResponseBody Body;
        
        public F19_UpdateEmail_RawResponse()
        {
        }
        
        public F19_UpdateEmail_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F19_UpdateEmail_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F19_UpdateEmail_RawResult;
        
        public F19_UpdateEmail_RawResponseBody()
        {
        }
        
        public F19_UpdateEmail_RawResponseBody(string F19_UpdateEmail_RawResult)
        {
            this.F19_UpdateEmail_RawResult = F19_UpdateEmail_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F25_GetPersonalData_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F25_GetPersonalData_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawRequestBody Body;
        
        public F25_GetPersonalData_RawRequest()
        {
        }
        
        public F25_GetPersonalData_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F25_GetPersonalData_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        public F25_GetPersonalData_RawRequestBody()
        {
        }
        
        public F25_GetPersonalData_RawRequestBody(string pCpr)
        {
            this.pCpr = pCpr;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F25_GetPersonalData_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F25_GetPersonalData_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawResponseBody Body;
        
        public F25_GetPersonalData_RawResponse()
        {
        }
        
        public F25_GetPersonalData_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F25_GetPersonalData_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F25_GetPersonalData_RawResult;
        
        public F25_GetPersonalData_RawResponseBody()
        {
        }
        
        public F25_GetPersonalData_RawResponseBody(string F25_GetPersonalData_RawResult)
        {
            this.F25_GetPersonalData_RawResult = F25_GetPersonalData_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F27_CardHolderVerification_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F27_CardHolderVerification_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawRequestBody Body;
        
        public F27_CardHolderVerification_RawRequest()
        {
        }
        
        public F27_CardHolderVerification_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F27_CardHolderVerification_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string _sCardEmbossedName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string _sCardLast4Digit;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string pMobile;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string valdationFlag;
        
        public F27_CardHolderVerification_RawRequestBody()
        {
        }
        
        public F27_CardHolderVerification_RawRequestBody(string _sCardEmbossedName, string _sCardLast4Digit, string pCpr, string pMobile, string valdationFlag)
        {
            this._sCardEmbossedName = _sCardEmbossedName;
            this._sCardLast4Digit = _sCardLast4Digit;
            this.pCpr = pCpr;
            this.pMobile = pMobile;
            this.valdationFlag = valdationFlag;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F27_CardHolderVerification_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F27_CardHolderVerification_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawResponseBody Body;
        
        public F27_CardHolderVerification_RawResponse()
        {
        }
        
        public F27_CardHolderVerification_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F27_CardHolderVerification_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F27_CardHolderVerification_RawResult;
        
        public F27_CardHolderVerification_RawResponseBody()
        {
        }
        
        public F27_CardHolderVerification_RawResponseBody(string F27_CardHolderVerification_RawResult)
        {
            this.F27_CardHolderVerification_RawResult = F27_CardHolderVerification_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F29_GetWalletsBalancesRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F29_GetWalletsBalances", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesRequestBody Body;
        
        public F29_GetWalletsBalancesRequest()
        {
        }
        
        public F29_GetWalletsBalancesRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F29_GetWalletsBalancesRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string mainCardBalanceJson;
        
        public F29_GetWalletsBalancesRequestBody()
        {
        }
        
        public F29_GetWalletsBalancesRequestBody(string pCpr, string pCardNumber, string mainCardBalanceJson)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
            this.mainCardBalanceJson = mainCardBalanceJson;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F29_GetWalletsBalancesResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F29_GetWalletsBalancesResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesResponseBody Body;
        
        public F29_GetWalletsBalancesResponse()
        {
        }
        
        public F29_GetWalletsBalancesResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F29_GetWalletsBalancesResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F29_GetWalletsBalancesResult;
        
        public F29_GetWalletsBalancesResponseBody()
        {
        }
        
        public F29_GetWalletsBalancesResponseBody(string F29_GetWalletsBalancesResult)
        {
            this.F29_GetWalletsBalancesResult = F29_GetWalletsBalancesResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F30_VerifyTransferAmountRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F30_VerifyTransferAmount", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountRequestBody Body;
        
        public F30_VerifyTransferAmountRequest()
        {
        }
        
        public F30_VerifyTransferAmountRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F30_VerifyTransferAmountRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string sourceWallet;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string destinationWallet;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public decimal transferAmount;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string transferCurrency;
        
        public F30_VerifyTransferAmountRequestBody()
        {
        }
        
        public F30_VerifyTransferAmountRequestBody(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
            this.sourceWallet = sourceWallet;
            this.destinationWallet = destinationWallet;
            this.transferAmount = transferAmount;
            this.transferCurrency = transferCurrency;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F30_VerifyTransferAmountResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F30_VerifyTransferAmountResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountResponseBody Body;
        
        public F30_VerifyTransferAmountResponse()
        {
        }
        
        public F30_VerifyTransferAmountResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F30_VerifyTransferAmountResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F30_VerifyTransferAmountResult;
        
        public F30_VerifyTransferAmountResponseBody()
        {
        }
        
        public F30_VerifyTransferAmountResponseBody(string F30_VerifyTransferAmountResult)
        {
            this.F30_VerifyTransferAmountResult = F30_VerifyTransferAmountResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F31_ConfirmTransferAmountRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F31_ConfirmTransferAmount", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountRequestBody Body;
        
        public F31_ConfirmTransferAmountRequest()
        {
        }
        
        public F31_ConfirmTransferAmountRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F31_ConfirmTransferAmountRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string sourceWallet;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string destinationWallet;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public decimal transferAmount;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string transferCurrency;
        
        public F31_ConfirmTransferAmountRequestBody()
        {
        }
        
        public F31_ConfirmTransferAmountRequestBody(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
            this.sourceWallet = sourceWallet;
            this.destinationWallet = destinationWallet;
            this.transferAmount = transferAmount;
            this.transferCurrency = transferCurrency;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F31_ConfirmTransferAmountResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F31_ConfirmTransferAmountResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountResponseBody Body;
        
        public F31_ConfirmTransferAmountResponse()
        {
        }
        
        public F31_ConfirmTransferAmountResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F31_ConfirmTransferAmountResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F31_ConfirmTransferAmountResult;
        
        public F31_ConfirmTransferAmountResponseBody()
        {
        }
        
        public F31_ConfirmTransferAmountResponseBody(string F31_ConfirmTransferAmountResult)
        {
            this.F31_ConfirmTransferAmountResult = F31_ConfirmTransferAmountResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetWalletStatementTransactionRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetWalletStatementTransaction", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionRequestBody Body;
        
        public GetWalletStatementTransactionRequest()
        {
        }
        
        public GetWalletStatementTransactionRequest(Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetWalletStatementTransactionRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string type;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string statementDate;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string Currency;
        
        public GetWalletStatementTransactionRequestBody()
        {
        }
        
        public GetWalletStatementTransactionRequestBody(string pCpr, string pCardNumber, string type, string statementDate, string Currency)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
            this.type = type;
            this.statementDate = statementDate;
            this.Currency = Currency;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetWalletStatementTransactionResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetWalletStatementTransactionResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionResponseBody Body;
        
        public GetWalletStatementTransactionResponse()
        {
        }
        
        public GetWalletStatementTransactionResponse(Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetWalletStatementTransactionResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string GetWalletStatementTransactionResult;
        
        public GetWalletStatementTransactionResponseBody()
        {
        }
        
        public GetWalletStatementTransactionResponseBody(string GetWalletStatementTransactionResult)
        {
            this.GetWalletStatementTransactionResult = GetWalletStatementTransactionResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F23_LoyFppProgRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F23_LoyFppProg", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgRequestBody Body;
        
        public F23_LoyFppProgRequest()
        {
        }
        
        public F23_LoyFppProgRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F23_LoyFppProgRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        public F23_LoyFppProgRequestBody()
        {
        }
        
        public F23_LoyFppProgRequestBody(string pCpr)
        {
            this.pCpr = pCpr;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F23_LoyFppProgResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F23_LoyFppProgResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgResponseBody Body;
        
        public F23_LoyFppProgResponse()
        {
        }
        
        public F23_LoyFppProgResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F23_LoyFppProgResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F23_LoyFppProgResult;
        
        public F23_LoyFppProgResponseBody()
        {
        }
        
        public F23_LoyFppProgResponseBody(string F23_LoyFppProgResult)
        {
            this.F23_LoyFppProgResult = F23_LoyFppProgResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F24_LoyThameenRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F24_LoyThameen", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenRequestBody Body;
        
        public F24_LoyThameenRequest()
        {
        }
        
        public F24_LoyThameenRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F24_LoyThameenRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        public F24_LoyThameenRequestBody()
        {
        }
        
        public F24_LoyThameenRequestBody(string pCpr)
        {
            this.pCpr = pCpr;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F24_LoyThameenResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F24_LoyThameenResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenResponseBody Body;
        
        public F24_LoyThameenResponse()
        {
        }
        
        public F24_LoyThameenResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F24_LoyThameenResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F24_LoyThameenResult;
        
        public F24_LoyThameenResponseBody()
        {
        }
        
        public F24_LoyThameenResponseBody(string F24_LoyThameenResult)
        {
            this.F24_LoyThameenResult = F24_LoyThameenResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F26_GetSmsDataRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F26_GetSmsData", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataRequestBody Body;
        
        public F26_GetSmsDataRequest()
        {
        }
        
        public F26_GetSmsDataRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute()]
    public partial class F26_GetSmsDataRequestBody
    {
        
        public F26_GetSmsDataRequestBody()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F26_GetSmsDataResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F26_GetSmsDataResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataResponseBody Body;
        
        public F26_GetSmsDataResponse()
        {
        }
        
        public F26_GetSmsDataResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F26_GetSmsDataResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F26_GetSmsDataResult;
        
        public F26_GetSmsDataResponseBody()
        {
        }
        
        public F26_GetSmsDataResponseBody(string F26_GetSmsDataResult)
        {
            this.F26_GetSmsDataResult = F26_GetSmsDataResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F3_Login_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F3_Login_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawRequestBody Body;
        
        public F3_Login_RawRequest()
        {
        }
        
        public F3_Login_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F3_Login_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        public F3_Login_RawRequestBody()
        {
        }
        
        public F3_Login_RawRequestBody(string pCpr)
        {
            this.pCpr = pCpr;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F3_Login_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F3_Login_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawResponseBody Body;
        
        public F3_Login_RawResponse()
        {
        }
        
        public F3_Login_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F3_Login_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F3_Login_RawResult;
        
        public F3_Login_RawResponseBody()
        {
        }
        
        public F3_Login_RawResponseBody(string F3_Login_RawResult)
        {
            this.F3_Login_RawResult = F3_Login_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F7_GetPreviousStatementTransaction_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F7_GetPreviousStatementTransaction_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawRequestBody Body;
        
        public F7_GetPreviousStatementTransaction_RawRequest()
        {
        }
        
        public F7_GetPreviousStatementTransaction_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F7_GetPreviousStatementTransaction_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string pMonth;
        
        public F7_GetPreviousStatementTransaction_RawRequestBody()
        {
        }
        
        public F7_GetPreviousStatementTransaction_RawRequestBody(string pCpr, string pCardNumber, string pMonth)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
            this.pMonth = pMonth;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F7_GetPreviousStatementTransaction_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F7_GetPreviousStatementTransaction_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawResponseBody Body;
        
        public F7_GetPreviousStatementTransaction_RawResponse()
        {
        }
        
        public F7_GetPreviousStatementTransaction_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F7_GetPreviousStatementTransaction_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F7_GetPreviousStatementTransaction_RawResult;
        
        public F7_GetPreviousStatementTransaction_RawResponseBody()
        {
        }
        
        public F7_GetPreviousStatementTransaction_RawResponseBody(string F7_GetPreviousStatementTransaction_RawResult)
        {
            this.F7_GetPreviousStatementTransaction_RawResult = F7_GetPreviousStatementTransaction_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F8_GetCurrentStatementTransaction_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F8_GetCurrentStatementTransaction_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawRequestBody Body;
        
        public F8_GetCurrentStatementTransaction_RawRequest()
        {
        }
        
        public F8_GetCurrentStatementTransaction_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F8_GetCurrentStatementTransaction_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        public F8_GetCurrentStatementTransaction_RawRequestBody()
        {
        }
        
        public F8_GetCurrentStatementTransaction_RawRequestBody(string pCpr, string pCardNumber)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F8_GetCurrentStatementTransaction_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F8_GetCurrentStatementTransaction_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawResponseBody Body;
        
        public F8_GetCurrentStatementTransaction_RawResponse()
        {
        }
        
        public F8_GetCurrentStatementTransaction_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F8_GetCurrentStatementTransaction_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F8_GetCurrentStatementTransaction_RawResult;
        
        public F8_GetCurrentStatementTransaction_RawResponseBody()
        {
        }
        
        public F8_GetCurrentStatementTransaction_RawResponseBody(string F8_GetCurrentStatementTransaction_RawResult)
        {
            this.F8_GetCurrentStatementTransaction_RawResult = F8_GetCurrentStatementTransaction_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetStatementTransactionRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetStatementTransaction", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionRequestBody Body;
        
        public GetStatementTransactionRequest()
        {
        }
        
        public GetStatementTransactionRequest(Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetStatementTransactionRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string type;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string statementDate;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string suppCardsJson;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string onlySupp;
        
        public GetStatementTransactionRequestBody()
        {
        }
        
        public GetStatementTransactionRequestBody(string pCpr, string pCardNumber, string type, string statementDate, string suppCardsJson, string onlySupp)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
            this.type = type;
            this.statementDate = statementDate;
            this.suppCardsJson = suppCardsJson;
            this.onlySupp = onlySupp;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class GetStatementTransactionResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="GetStatementTransactionResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionResponseBody Body;
        
        public GetStatementTransactionResponse()
        {
        }
        
        public GetStatementTransactionResponse(Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class GetStatementTransactionResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string GetStatementTransactionResult;
        
        public GetStatementTransactionResponseBody()
        {
        }
        
        public GetStatementTransactionResponseBody(string GetStatementTransactionResult)
        {
            this.GetStatementTransactionResult = GetStatementTransactionResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F9_GetHoldStatementTransaction_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F9_GetHoldStatementTransaction_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawRequestBody Body;
        
        public F9_GetHoldStatementTransaction_RawRequest()
        {
        }
        
        public F9_GetHoldStatementTransaction_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F9_GetHoldStatementTransaction_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string pMonth;
        
        public F9_GetHoldStatementTransaction_RawRequestBody()
        {
        }
        
        public F9_GetHoldStatementTransaction_RawRequestBody(string pCpr, string pCardNumber, string pMonth)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
            this.pMonth = pMonth;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F9_GetHoldStatementTransaction_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F9_GetHoldStatementTransaction_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawResponseBody Body;
        
        public F9_GetHoldStatementTransaction_RawResponse()
        {
        }
        
        public F9_GetHoldStatementTransaction_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F9_GetHoldStatementTransaction_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F9_GetHoldStatementTransaction_RawResult;
        
        public F9_GetHoldStatementTransaction_RawResponseBody()
        {
        }
        
        public F9_GetHoldStatementTransaction_RawResponseBody(string F9_GetHoldStatementTransaction_RawResult)
        {
            this.F9_GetHoldStatementTransaction_RawResult = F9_GetHoldStatementTransaction_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F16B_DisablEstatement_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F16B_DisablEstatement_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawRequestBody Body;
        
        public F16B_DisablEstatement_RawRequest()
        {
        }
        
        public F16B_DisablEstatement_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F16B_DisablEstatement_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pEmail;
        
        public F16B_DisablEstatement_RawRequestBody()
        {
        }
        
        public F16B_DisablEstatement_RawRequestBody(string pCpr, string pEmail)
        {
            this.pCpr = pCpr;
            this.pEmail = pEmail;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F16B_DisablEstatement_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F16B_DisablEstatement_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawResponseBody Body;
        
        public F16B_DisablEstatement_RawResponse()
        {
        }
        
        public F16B_DisablEstatement_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F16B_DisablEstatement_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F16B_DisablEstatement_RawResult;
        
        public F16B_DisablEstatement_RawResponseBody()
        {
        }
        
        public F16B_DisablEstatement_RawResponseBody(string F16B_DisablEstatement_RawResult)
        {
            this.F16B_DisablEstatement_RawResult = F16B_DisablEstatement_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F17_SetSuppLimit_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F17_SetSuppLimit_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawRequestBody Body;
        
        public F17_SetSuppLimit_RawRequest()
        {
        }
        
        public F17_SetSuppLimit_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F17_SetSuppLimit_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string pLimit;
        
        public F17_SetSuppLimit_RawRequestBody()
        {
        }
        
        public F17_SetSuppLimit_RawRequestBody(string pCpr, string pCardNumber, string pLimit)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
            this.pLimit = pLimit;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F17_SetSuppLimit_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F17_SetSuppLimit_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawResponseBody Body;
        
        public F17_SetSuppLimit_RawResponse()
        {
        }
        
        public F17_SetSuppLimit_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F17_SetSuppLimit_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F17_SetSuppLimit_RawResult;
        
        public F17_SetSuppLimit_RawResponseBody()
        {
        }
        
        public F17_SetSuppLimit_RawResponseBody(string F17_SetSuppLimit_RawResult)
        {
            this.F17_SetSuppLimit_RawResult = F17_SetSuppLimit_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F20_UpdateDOB_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F20_UpdateDOB_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawRequestBody Body;
        
        public F20_UpdateDOB_RawRequest()
        {
        }
        
        public F20_UpdateDOB_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F20_UpdateDOB_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pDob;
        
        public F20_UpdateDOB_RawRequestBody()
        {
        }
        
        public F20_UpdateDOB_RawRequestBody(string pCpr, string pDob)
        {
            this.pCpr = pCpr;
            this.pDob = pDob;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F20_UpdateDOB_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F20_UpdateDOB_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawResponseBody Body;
        
        public F20_UpdateDOB_RawResponse()
        {
        }
        
        public F20_UpdateDOB_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F20_UpdateDOB_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F20_UpdateDOB_RawResult;
        
        public F20_UpdateDOB_RawResponseBody()
        {
        }
        
        public F20_UpdateDOB_RawResponseBody(string F20_UpdateDOB_RawResult)
        {
            this.F20_UpdateDOB_RawResult = F20_UpdateDOB_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F23_LoyFppProg_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F23_LoyFppProg_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawRequestBody Body;
        
        public F23_LoyFppProg_RawRequest()
        {
        }
        
        public F23_LoyFppProg_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F23_LoyFppProg_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        public F23_LoyFppProg_RawRequestBody()
        {
        }
        
        public F23_LoyFppProg_RawRequestBody(string pCpr)
        {
            this.pCpr = pCpr;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F23_LoyFppProg_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F23_LoyFppProg_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawResponseBody Body;
        
        public F23_LoyFppProg_RawResponse()
        {
        }
        
        public F23_LoyFppProg_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F23_LoyFppProg_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F23_LoyFppProg_RawResult;
        
        public F23_LoyFppProg_RawResponseBody()
        {
        }
        
        public F23_LoyFppProg_RawResponseBody(string F23_LoyFppProg_RawResult)
        {
            this.F23_LoyFppProg_RawResult = F23_LoyFppProg_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F24_LoyThameenA_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F24_LoyThameenA_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawRequestBody Body;
        
        public F24_LoyThameenA_RawRequest()
        {
        }
        
        public F24_LoyThameenA_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F24_LoyThameenA_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        public F24_LoyThameenA_RawRequestBody()
        {
        }
        
        public F24_LoyThameenA_RawRequestBody(string pCpr)
        {
            this.pCpr = pCpr;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F24_LoyThameenA_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F24_LoyThameenA_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawResponseBody Body;
        
        public F24_LoyThameenA_RawResponse()
        {
        }
        
        public F24_LoyThameenA_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F24_LoyThameenA_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F24_LoyThameenA_RawResult;
        
        public F24_LoyThameenA_RawResponseBody()
        {
        }
        
        public F24_LoyThameenA_RawResponseBody(string F24_LoyThameenA_RawResult)
        {
            this.F24_LoyThameenA_RawResult = F24_LoyThameenA_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F24_LoyThameenB_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F24_LoyThameenB_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawRequestBody Body;
        
        public F24_LoyThameenB_RawRequest()
        {
        }
        
        public F24_LoyThameenB_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F24_LoyThameenB_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        public F24_LoyThameenB_RawRequestBody()
        {
        }
        
        public F24_LoyThameenB_RawRequestBody(string pCpr)
        {
            this.pCpr = pCpr;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F24_LoyThameenB_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F24_LoyThameenB_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawResponseBody Body;
        
        public F24_LoyThameenB_RawResponse()
        {
        }
        
        public F24_LoyThameenB_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F24_LoyThameenB_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F24_LoyThameenB_RawResult;
        
        public F24_LoyThameenB_RawResponseBody()
        {
        }
        
        public F24_LoyThameenB_RawResponseBody(string F24_LoyThameenB_RawResult)
        {
            this.F24_LoyThameenB_RawResult = F24_LoyThameenB_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F28_PINBySMS_RawRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F28_PINBySMS_Raw", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawRequestBody Body;
        
        public F28_PINBySMS_RawRequest()
        {
        }
        
        public F28_PINBySMS_RawRequest(Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F28_PINBySMS_RawRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string pCpr;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string pCardNumber;
        
        public F28_PINBySMS_RawRequestBody()
        {
        }
        
        public F28_PINBySMS_RawRequestBody(string pCpr, string pCardNumber)
        {
            this.pCpr = pCpr;
            this.pCardNumber = pCardNumber;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class F28_PINBySMS_RawResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="F28_PINBySMS_RawResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawResponseBody Body;
        
        public F28_PINBySMS_RawResponse()
        {
        }
        
        public F28_PINBySMS_RawResponse(Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class F28_PINBySMS_RawResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string F28_PINBySMS_RawResult;
        
        public F28_PINBySMS_RawResponseBody()
        {
        }
        
        public F28_PINBySMS_RawResponseBody(string F28_PINBySMS_RawResult)
        {
            this.F28_PINBySMS_RawResult = F28_PINBySMS_RawResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class RunWebServiceRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="RunWebService", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceRequestBody Body;
        
        public RunWebServiceRequest()
        {
        }
        
        public RunWebServiceRequest(Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class RunWebServiceRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string functionName;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string parameters;
        
        public RunWebServiceRequestBody()
        {
        }
        
        public RunWebServiceRequestBody(string functionName, string parameters)
        {
            this.functionName = functionName;
            this.parameters = parameters;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class RunWebServiceResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="RunWebServiceResponse", Namespace="http://tempuri.org/", Order=0)]
        public Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceResponseBody Body;
        
        public RunWebServiceResponse()
        {
        }
        
        public RunWebServiceResponse(Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class RunWebServiceResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string RunWebServiceResult;
        
        public RunWebServiceResponseBody()
        {
        }
        
        public RunWebServiceResponseBody(string RunWebServiceResult)
        {
            this.RunWebServiceResult = RunWebServiceResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface CrediAppServiceSoapChannel : Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class CrediAppServiceSoapClient : System.ServiceModel.ClientBase<Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap>, Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public CrediAppServiceSoapClient(EndpointConfiguration endpointConfiguration) : 
                base(CrediAppServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), CrediAppServiceSoapClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public CrediAppServiceSoapClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(CrediAppServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public CrediAppServiceSoapClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(CrediAppServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public CrediAppServiceSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F4_GetCardListAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListRequest request)
        {
            return base.Channel.F4_GetCardListAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListResponse> F4_GetCardListAsync(string pCpr)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F4_GetCardListRequestBody();
            inValue.Body.pCpr = pCpr;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F4_GetCardListAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F5_GetBalanceAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceRequest request)
        {
            return base.Channel.F5_GetBalanceAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceResponse> F5_GetBalanceAsync(string pCpr, string pCardNumber, string mainCardBalanceJson)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F5_GetBalanceRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            inValue.Body.mainCardBalanceJson = mainCardBalanceJson;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F5_GetBalanceAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F6_GetStatementListAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListRequest request)
        {
            return base.Channel.F6_GetStatementListAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListResponse> F6_GetStatementListAsync(string pCpr, string pCardNumber)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F6_GetStatementListRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F6_GetStatementListAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F8_GetCurrentStatementTransactionAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionRequest request)
        {
            return base.Channel.F8_GetCurrentStatementTransactionAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionResponse> F8_GetCurrentStatementTransactionAsync(string pCpr, string pCardNumber)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransactionRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F8_GetCurrentStatementTransactionAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F10_StopCard_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawRequest request)
        {
            return base.Channel.F10_StopCard_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawResponse> F10_StopCard_RawAsync(string pCpr, string pCardNumber)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F10_StopCard_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F10_StopCard_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F13_PayMyCardPayment_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawRequest request)
        {
            return base.Channel.F13_PayMyCardPayment_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawResponse> F13_PayMyCardPayment_RawAsync(string pCardNumber, string pTrxAmount, string pCpr, string pReferenceNbr, string fromCAID, string transaction_source)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F13_PayMyCardPayment_RawRequestBody();
            inValue.Body.pCardNumber = pCardNumber;
            inValue.Body.pTrxAmount = pTrxAmount;
            inValue.Body.pCpr = pCpr;
            inValue.Body.pReferenceNbr = pReferenceNbr;
            inValue.Body.fromCAID = fromCAID;
            inValue.Body.transaction_source = transaction_source;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F13_PayMyCardPayment_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.External_Transfer_Funds_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawRequest request)
        {
            return base.Channel.External_Transfer_Funds_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawResponse> External_Transfer_Funds_RawAsync(string fromCardNumber, string pTrxAmount, string fromCpr, string toCpr, string toAccount, string targetDetination)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.External_Transfer_Funds_RawRequestBody();
            inValue.Body.fromCardNumber = fromCardNumber;
            inValue.Body.pTrxAmount = pTrxAmount;
            inValue.Body.fromCpr = fromCpr;
            inValue.Body.toCpr = toCpr;
            inValue.Body.toAccount = toAccount;
            inValue.Body.targetDetination = targetDetination;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).External_Transfer_Funds_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F16A_ActivatEstatement_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawRequest request)
        {
            return base.Channel.F16A_ActivatEstatement_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawResponse> F16A_ActivatEstatement_RawAsync(string pCpr, string pEmailAddress)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F16A_ActivatEstatement_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pEmailAddress = pEmailAddress;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F16A_ActivatEstatement_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F18_UpdateMobileNumber_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawRequest request)
        {
            return base.Channel.F18_UpdateMobileNumber_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawResponse> F18_UpdateMobileNumber_RawAsync(string pCpr, string pMobile, string pHome)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F18_UpdateMobileNumber_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pMobile = pMobile;
            inValue.Body.pHome = pHome;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F18_UpdateMobileNumber_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F19_UpdateEmail_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawRequest request)
        {
            return base.Channel.F19_UpdateEmail_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawResponse> F19_UpdateEmail_RawAsync(string pCpr, string pEmail)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F19_UpdateEmail_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pEmail = pEmail;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F19_UpdateEmail_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F25_GetPersonalData_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawRequest request)
        {
            return base.Channel.F25_GetPersonalData_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawResponse> F25_GetPersonalData_RawAsync(string pCpr)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F25_GetPersonalData_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F25_GetPersonalData_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F27_CardHolderVerification_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawRequest request)
        {
            return base.Channel.F27_CardHolderVerification_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawResponse> F27_CardHolderVerification_RawAsync(string _sCardEmbossedName, string _sCardLast4Digit, string pCpr, string pMobile, string valdationFlag)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F27_CardHolderVerification_RawRequestBody();
            inValue.Body._sCardEmbossedName = _sCardEmbossedName;
            inValue.Body._sCardLast4Digit = _sCardLast4Digit;
            inValue.Body.pCpr = pCpr;
            inValue.Body.pMobile = pMobile;
            inValue.Body.valdationFlag = valdationFlag;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F27_CardHolderVerification_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F29_GetWalletsBalancesAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesRequest request)
        {
            return base.Channel.F29_GetWalletsBalancesAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesResponse> F29_GetWalletsBalancesAsync(string pCpr, string pCardNumber, string mainCardBalanceJson)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F29_GetWalletsBalancesRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            inValue.Body.mainCardBalanceJson = mainCardBalanceJson;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F29_GetWalletsBalancesAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F30_VerifyTransferAmountAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountRequest request)
        {
            return base.Channel.F30_VerifyTransferAmountAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountResponse> F30_VerifyTransferAmountAsync(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F30_VerifyTransferAmountRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            inValue.Body.sourceWallet = sourceWallet;
            inValue.Body.destinationWallet = destinationWallet;
            inValue.Body.transferAmount = transferAmount;
            inValue.Body.transferCurrency = transferCurrency;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F30_VerifyTransferAmountAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F31_ConfirmTransferAmountAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountRequest request)
        {
            return base.Channel.F31_ConfirmTransferAmountAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountResponse> F31_ConfirmTransferAmountAsync(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F31_ConfirmTransferAmountRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            inValue.Body.sourceWallet = sourceWallet;
            inValue.Body.destinationWallet = destinationWallet;
            inValue.Body.transferAmount = transferAmount;
            inValue.Body.transferCurrency = transferCurrency;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F31_ConfirmTransferAmountAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.GetWalletStatementTransactionAsync(Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionRequest request)
        {
            return base.Channel.GetWalletStatementTransactionAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionResponse> GetWalletStatementTransactionAsync(string pCpr, string pCardNumber, string type, string statementDate, string Currency)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.GetWalletStatementTransactionRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            inValue.Body.type = type;
            inValue.Body.statementDate = statementDate;
            inValue.Body.Currency = Currency;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).GetWalletStatementTransactionAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F23_LoyFppProgAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgRequest request)
        {
            return base.Channel.F23_LoyFppProgAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgResponse> F23_LoyFppProgAsync(string pCpr)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProgRequestBody();
            inValue.Body.pCpr = pCpr;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F23_LoyFppProgAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F24_LoyThameenAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenRequest request)
        {
            return base.Channel.F24_LoyThameenAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenResponse> F24_LoyThameenAsync(string pCpr)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenRequestBody();
            inValue.Body.pCpr = pCpr;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F24_LoyThameenAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F26_GetSmsDataAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataRequest request)
        {
            return base.Channel.F26_GetSmsDataAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataResponse> F26_GetSmsDataAsync()
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F26_GetSmsDataRequestBody();
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F26_GetSmsDataAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F3_Login_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawRequest request)
        {
            return base.Channel.F3_Login_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawResponse> F3_Login_RawAsync(string pCpr)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F3_Login_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F3_Login_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F7_GetPreviousStatementTransaction_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawRequest request)
        {
            return base.Channel.F7_GetPreviousStatementTransaction_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawResponse> F7_GetPreviousStatementTransaction_RawAsync(string pCpr, string pCardNumber, string pMonth)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F7_GetPreviousStatementTransaction_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            inValue.Body.pMonth = pMonth;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F7_GetPreviousStatementTransaction_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F8_GetCurrentStatementTransaction_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawRequest request)
        {
            return base.Channel.F8_GetCurrentStatementTransaction_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawResponse> F8_GetCurrentStatementTransaction_RawAsync(string pCpr, string pCardNumber)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F8_GetCurrentStatementTransaction_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F8_GetCurrentStatementTransaction_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.GetStatementTransactionAsync(Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionRequest request)
        {
            return base.Channel.GetStatementTransactionAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionResponse> GetStatementTransactionAsync(string pCpr, string pCardNumber, string type, string statementDate, string suppCardsJson, string onlySupp)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.GetStatementTransactionRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            inValue.Body.type = type;
            inValue.Body.statementDate = statementDate;
            inValue.Body.suppCardsJson = suppCardsJson;
            inValue.Body.onlySupp = onlySupp;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).GetStatementTransactionAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F9_GetHoldStatementTransaction_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawRequest request)
        {
            return base.Channel.F9_GetHoldStatementTransaction_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawResponse> F9_GetHoldStatementTransaction_RawAsync(string pCpr, string pCardNumber, string pMonth)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F9_GetHoldStatementTransaction_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            inValue.Body.pMonth = pMonth;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F9_GetHoldStatementTransaction_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F16B_DisablEstatement_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawRequest request)
        {
            return base.Channel.F16B_DisablEstatement_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawResponse> F16B_DisablEstatement_RawAsync(string pCpr, string pEmail)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F16B_DisablEstatement_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pEmail = pEmail;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F16B_DisablEstatement_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F17_SetSuppLimit_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawRequest request)
        {
            return base.Channel.F17_SetSuppLimit_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawResponse> F17_SetSuppLimit_RawAsync(string pCpr, string pCardNumber, string pLimit)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F17_SetSuppLimit_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            inValue.Body.pLimit = pLimit;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F17_SetSuppLimit_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F20_UpdateDOB_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawRequest request)
        {
            return base.Channel.F20_UpdateDOB_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawResponse> F20_UpdateDOB_RawAsync(string pCpr, string pDob)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F20_UpdateDOB_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pDob = pDob;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F20_UpdateDOB_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F23_LoyFppProg_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawRequest request)
        {
            return base.Channel.F23_LoyFppProg_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawResponse> F23_LoyFppProg_RawAsync(string pCpr)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F23_LoyFppProg_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F23_LoyFppProg_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F24_LoyThameenA_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawRequest request)
        {
            return base.Channel.F24_LoyThameenA_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawResponse> F24_LoyThameenA_RawAsync(string pCpr)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenA_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F24_LoyThameenA_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F24_LoyThameenB_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawRequest request)
        {
            return base.Channel.F24_LoyThameenB_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawResponse> F24_LoyThameenB_RawAsync(string pCpr)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F24_LoyThameenB_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F24_LoyThameenB_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.F28_PINBySMS_RawAsync(Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawRequest request)
        {
            return base.Channel.F28_PINBySMS_RawAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawResponse> F28_PINBySMS_RawAsync(string pCpr, string pCardNumber)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.F28_PINBySMS_RawRequestBody();
            inValue.Body.pCpr = pCpr;
            inValue.Body.pCardNumber = pCardNumber;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).F28_PINBySMS_RawAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceResponse> Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap.RunWebServiceAsync(Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceRequest request)
        {
            return base.Channel.RunWebServiceAsync(request);
        }
        
        public System.Threading.Tasks.Task<Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceResponse> RunWebServiceAsync(string functionName, string parameters)
        {
            Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceRequest inValue = new Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceRequest();
            inValue.Body = new Optimum.Wallet.Infrastructure.CrediAppWS.RunWebServiceRequestBody();
            inValue.Body.functionName = functionName;
            inValue.Body.parameters = parameters;
            return ((Optimum.Wallet.Infrastructure.CrediAppWS.CrediAppServiceSoap)(this)).RunWebServiceAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.CrediAppServiceSoap))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            if ((endpointConfiguration == EndpointConfiguration.CrediAppServiceSoap12))
            {
                System.ServiceModel.Channels.CustomBinding result = new System.ServiceModel.Channels.CustomBinding();
                System.ServiceModel.Channels.TextMessageEncodingBindingElement textBindingElement = new System.ServiceModel.Channels.TextMessageEncodingBindingElement();
                textBindingElement.MessageVersion = System.ServiceModel.Channels.MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap12, System.ServiceModel.Channels.AddressingVersion.None);
                result.Elements.Add(textBindingElement);
                System.ServiceModel.Channels.HttpTransportBindingElement httpBindingElement = new System.ServiceModel.Channels.HttpTransportBindingElement();
                httpBindingElement.AllowCookies = true;
                httpBindingElement.MaxBufferSize = int.MaxValue;
                httpBindingElement.MaxReceivedMessageSize = int.MaxValue;
                result.Elements.Add(httpBindingElement);
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.CrediAppServiceSoap))
            {
                return new System.ServiceModel.EndpointAddress("http://localhost:53699/CrediAppService.asmx");
            }
            if ((endpointConfiguration == EndpointConfiguration.CrediAppServiceSoap12))
            {
                return new System.ServiceModel.EndpointAddress("http://localhost:53699/CrediAppService.asmx");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        public enum EndpointConfiguration
        {
            
            CrediAppServiceSoap,
            
            CrediAppServiceSoap12,
        }
    }
}
