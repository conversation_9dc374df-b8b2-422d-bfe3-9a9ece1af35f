using Optimum.Wallet.Application.Common.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Optimum.Wallet.Application.Interfaces
{
    public interface IWebServices
    {
        Task<string> External_Transfer_Funds_Raw(string fromCardNumber, string pTrxAmount, string fromCpr, string toCpr, string toAccount, string targetDetination);
        Task<string> F10_StopCard_Raw(string pCpr, string pCardNumber);
        Task<string> F13_PayMyCardPayment_Raw(string pCardNumber, string pTrxAmount, string pCpr, string pReferenceNbr, string fromCAID, string transaction_source);
        Task<string> F16A_ActivatEstatement_Raw(string pCpr, string pEmailAddress);
        Task<string> F18_UpdateMobileNumber_Raw(string pCpr, string pMobile, string pHome);
        Task<string> F19_UpdateEmail_Raw(string pCpr, string pEmail);
        Task<Dictionary<int, string>> F23_LoyFppProg(string pCpr);
        Task<List<Dictionary<int, string>>> F24_LoyThameen(string pCpr);
        Task<PersonalData> F25_GetPersonalData_Raw(string pCpr);
        Task<VerifyUser> F27_CardHolderVerification_Raw(string _sCardEmbossedName, string _sCardLast4Digit, string pCpr, string pMobile, string valdationFlag = "N");
        Task<List<Common.Models.Wallet>> F29_GetWalletsBalances(string pCpr, string pCardNumber, CardBalance mainCardBalance = null);
        Task<WalletTransferResult> F30_VerifyTransferAmount(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency);
        Task<WalletTransferResult> F31_ConfirmTransferAmount(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency);
        Task<List<CardDetailsViewModel>> F4_GetCardList(string pCpr);
        Task<CardBalance> F5_GetBalance(string pCpr, string pCardNumber, CardBalance mainCardBalance = null);
        Task<Dictionary<string, string>> F6_GetStatementList(string pCpr, string pCardNumber);
        Task<List<CardStatementModelView>> F8_GetCurrentStatementTransaction(string pCpr, string pCardNumber);
        string GetCardNumber(string source);
        Task<List<CardStatementModelView>> GetStatementTransaction(string pCpr, string pCardNumber, string type = "C", string statementDate = "", Dictionary<string, string> suppCards = null, bool onlySupp = false);
        Task<List<CardStatementModelView>> GetWalletStatementTransaction(string pCpr, string pCardNumber, string type = "C", string statementDate = "", string Currency = "");
        Task<Tuple<bool, string>> RunWebService(string functionName, string parameters);
    }
}