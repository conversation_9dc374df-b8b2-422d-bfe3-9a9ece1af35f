{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug"
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Username} {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "../logs/wallet-api-.log",
          "rollingInterval": "Day",
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {CorrelationId} {Level:u3}] {Username} {Message:lj}{NewLine}{Exception}"
        }
      }
    ]
  },
  "JWTSettings": {
    "Issuer": "WalletWebApi",
    "SecretKey": "ASDLJAS&D^*&A^WEASHDASDJALKSDWE&*hjsd0as87e00S&^Q&Q$&^%A*&(090HNDLSD))A*&D&*A^SD*HIKSJADp;l,()*)#$&*&",
    "ValidateLifetime": true,
    "ExpirationTimeMinutes": 360
  },
  "DBName": "Mallats",
  "ConnectionStrings": {

    "Utilities": "Server=IMPServer01,4017;Database=MallatsUtilities;User Id=Optimum;password=************;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "Ledger": "Server=IMPServer01,4017;Database=MallatsLedger;User Id=Optimum;password=************;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=True;"

    /*
    "Utilities": "Server=dbserver02.uaenorth.cloudapp.azure.com,49188;Database=MallatsUtilities;User Id=Optimum;password=********$^#@#S;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "Ledger": "Server=dbserver02.uaenorth.cloudapp.azure.com,49188;Database=MallatsLedger;User Id=Optimum;password=********$^#@#S;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "Payroll": "Server=dbserver02.uaenorth.cloudapp.azure.com,49188;Database=MallatsPayroll;User Id=Optimum;password=********$^#@#S;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "Service": "Server=dbserver02.uaenorth.cloudapp.azure.com,49188;Database=MallatsService;User Id=Optimum;password=********$^#@#S;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "Time": "Server=dbserver02.uaenorth.cloudapp.azure.com,49188;Database=MallatsTime;User Id=Optimum;password=********$^#@#S;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "Documents": "Server=dbserver02.uaenorth.cloudapp.azure.com,49188;Database=MallatsDocuments;User Id=Optimum;password=********$^#@#S;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=True;"
    */
  },
  "DefaultSmsSenderName": "Mallats",
  "SupportedCulture": [ "en", "ar" ],
  "AllowLoginBy": "CPR,mobile",
  "MobileAppFolder": "D:\\OnlineGitHub\\Innopact\\Mallats\\LatestMallats\\Mallats-Wallet\\AppFiles",
  "AppFolder": "D:\\OnlineGitHub\\Innopact\\Mallats\\LatestMallats\\Mallats-Wallet\\AppFiles",
  "FormsFolder": "Letter\\",
  "AccountPhotoFolder": "AccountPhotoFolder\\",
  "ContactPhotoFolder": "ContactPhotoFolder\\",
  "CommentPhotoFolder": "CommentPhotoFolder\\",
  "DummyData": "false",
  "BranchId": "82",
  "DataProtectionKey": "MallatsWallet",
  "RateLimit": {
    "PermitLimit": 10,
    "SlidingPermitLimit": 25,
    "Window": 1,
    "ReplenishmentPeriod": 2,
    "QueueLimit": 5,
    "SegmentsPerWindow": 8,
    "TokenLimit": 10,
    "TokenLimit2": 20,
    "TokensPerPeriod": 4,
    "AutoReplenishment": true
  },
  "Swagger": {
    "EnableSwagger": true,
    "User": "Swagger",
    "Password": "Mallats@2023"
  },

  "OneSignalAppID": "************************************",
  "OneSignalAppTitle": "B2C Wolke Wallet",
  "OneSignalRestKey": "************************************************",
  "OneSignalMaxTries": "3",
  "OneSignalTimeWindowInMins": "180",
  "OneSignalMaxToProcess": "2000",
  "MaxToProcess": "500",
  "OneLaunchURL": "https://localhost:7067/en/account/SingleNotification/{id}",


  "BenefitPaymentGatewayPassword": "*********",
  "BenefitPaymentGatewayResourceKey": "02523413510502523413510502523413",
  "BenefitPaymentGatewayTranportalID": "*********"
}
