﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Primitives;
using System.Reflection.Metadata.Ecma335;
using System.Security.Claims;
using Optimum.Wallet.Core.Extensions;

namespace Optimum.Wallet.Api
{
    public abstract class BaseController<T> : ControllerBase where T : BaseController<T>
    {
        private ILogger<T> _logger;
        protected ILogger<T> Logger => _logger ?? (_logger = HttpContext.RequestServices.GetService<ILogger<T>>());
        protected bool isMobile => HttpContext.Request.Headers.TryGetValue("isMobile", out _);
        protected bool RefreshCache => HttpContext.Request.Headers.TryGetValue("RefreshCache", out _);
        protected AppUser CurrentUser => new AppUser(User);
        protected string IPAddress => HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString();
    }
}
