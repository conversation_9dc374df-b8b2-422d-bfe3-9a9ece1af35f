using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Net.Http.Json;
using System.Security.Claims;
using System.Text.Json.Serialization;
using Optimum.Wallet.Domain.Entities;

namespace Optimum.Wallet.Application.Common.Models
{
    public class LoginRequest
    {
        [Required]
        [MaxLength(100)]
        public string Username { get; set; }

        [MaxLength(15)]
        public string Mobile { get; set; }

        [Required]
        [DataType(DataType.Password)]
        public string Password { get; set; }

        public bool RememberMe { get; set; }

        public string FingerprintHash { get; set; }

        public string FingerprintEnable { get; set; }
        public string isPinVerification { get; set; }

        public bool? isHomeFromHomePage { get; set; }
        public bool isMallatsLogin { get; set; }
    }

    public class LoginResponse
    {
        public string Username { get; set; }
        public string JWTToken { get; set; }
        public string RefreshToken { get; set; }
        public int TokenExpiry { get; set; } = 24;
        public bool IsRetailCustomer { get; set; }
        public bool KYCInProgress { get; set; }

    }

    public class CreatePasswordRequest
    {
        public bool isResetPwd { get; set; }
        public bool isCreateAccount { get; set; }
        public bool isUpdateAccount { get; set; }
        public bool isKYCAccount { get; set; }
        [Required]        
        public int pin { get; set; }
        [System.Text.Json.Serialization.JsonIgnore]
        public string actionName => isResetPwd ? "ForgotPassword" : (isCreateAccount ? "CreateAccount" : "Register");
        [Required]
        public RegisterRequest registerRequest { get; set; }
        [Required]
        public RegisterResponse  registerResponse { get; set; }
    }

    public class RegisterKycProcess
    {
        public string Username { get; set; }
        public bool verified { get; set; }
        public string ExternalCode { get; set; }
        public string ExternalUrl { get; set; }
        public bool IsRetailCustomer { get; set; } = true;
    }

    public class ContactRegisterData : ContactTbl
    {
        public int CAID { get; set; }
        public int CRID { get; set; }
        public int CTRID { get; set; }
    }

    public class ContactLoginData : ContactTbl
    {
        public string salt { get; set; }
        public string iteration_count { get; set; }
        public string BankCode { get; set; }
        public int CAID { get; set; }
        public int CRID { get; set; }
        public int CTRID { get; set; }
        public bool IsChild { get; set; }
        public int CustAcctType { get; set; }
    }

    public class RegisterRequest
    {
        [Required]
        public string Username { get; set; }

        public string Name { get; set; }

        [Required]
        public string MobileNumber { get; set; }

        public bool AcceptTc { get; set; }
        public string CardNumber { get; set; }
        [Required]
        public int Stage { get; set; } = 0;
    }

    public class RegisterResponse
    {
        public string Username { get; set; }
        public int? ContactID { get; set; }
        public string ContactName { get; set; }
        public bool KYCInProgress { get; set; }
        public int Stage { get; set; }
    }

    public class CreateAccountData
    {
        public CreateAccountUser User;
    }

    public class CreateAccountUser
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Password { get; set; }
        public string Salt { get; set; }
        public string HashIterations { get; set; }
    }

    public class NotificationCount
    {
        public string Status { get; set; }
        public int Count { get; set; }
    }

    public class NotificationGroup
    {
        public int Id { get; set; }
        public string Title { get; set; }
    }

    public class Notification
    {
        public int ID { get; set; }
        public DateTime Date { get; set; }
        public string Content { get; set; }
        public string Type { get; set; }
        public bool IsRead { get; set; }
    }

    public class transactionCountAndAmount
    {
        public int TotalCount { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class ScriptParamsResponse
    {
        public int notifications { get; set; }
        public ScriptParamProfile profile { get; set; }
        public string deviceId { get; set; }
        public bool addHomeHistory { get; set; } = true;
    }

    public class ScriptParamProfile
    {
        public string username { get; set; }
        public string name { get; set; }
        public string avatarId { get; set; }
    }

    public class NotificationRequest
    {
        [Required]
        public List<string> ids { get; set; }
        [Required]
        public string type { get; set; }
    }

    public class VerifyCodeRequest
    {
        [Required]
        public string Username { get; set; }
        [Required]
        public string MobileNumber { get; set; }
        [Required]
        public string code { get; set; }
        public bool isLogin { get; set; }
        public bool isResetPwd { get; set; } = false;
        public bool isCreateAccount { get; set; }
        public bool isUpdateAccount { get; set; }
        public bool isKYCAccount { get; set; }
    }

    public class VerifyCodeResponse : RegisterResponse
    {
        public string ShuftiproUrl { get; set; }
        public string ShuftiproRef { get; set; }
        public bool ShuftiPending { get; set; }
    }

    public class PersonalViewModel
    {
        public string Name { get; set; }

        [DataType(DataType.PhoneNumber)]
        public string Mobile { get; set; }

        [DataType(DataType.PhoneNumber)]
        public string HomeTel { get; set; }
        public string Email { get; set; }
        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? BirthDate { get; set; }
        public string Address6 { get; set; }
        public string CPR { get; set; }

        public string Avatar { get; set; }

        public bool IsMain { get; set; }
        public bool SymbolsMoneyControl { get; set; }
        public decimal SymbolsMoney { get; set; }
        public string econtactid { get; set; }
    }

    public class ProfileViewModel
    {
        [Required]
        public string tabKey { get; set; }
        public int id { get; set; }
        public PersonalViewModel PersonalDetails { get; set; }
        public IEnumerable<Form> PrivacySettings { get; set; }
        public FingerprintViewModel FingerprintSettings { get; set; }
        public ChangePasswordViewModel ChangePassword { get; set; }
    }

    public class ChangePasswordViewModel
    {
        [Required]
        [DataType(DataType.Password)]
        public string OldPassword { get; set; }
        [Required]
        [DataType(DataType.Password)]
        public string Password { get; set; }
        [Required]
        [DataType(DataType.Password)]
        public string ConfirmPassword { get; set; }
    }

    public class FingerprintViewModel
    {
        public bool FingerprintUnlock { get; set; }
    }

    public class RefreshTokenRequest
    {
        [Required]
        public string JWTToken { get; set; }
        [Required]
        public string RefreshToken { get; set; }
    }

    public class MerchantData
    {
        public string BankName { get; set; }
        public string City { get; set; }
        public int ISO18245 { get; set; }
        public string MobileNumber { get; set; }
        public decimal? Amount { get; set; }
        public string CPR { get; set; }
        //START: For ATM Withdrawal
        public string Date { get; set; }
        public string Time { get; set; }
        public string TransactionAmount { get; set; }
        public string BillNumber { get; set; }
        public string StoreLabel { get; set; }
        public string CustomerLabel { get; set; }
        public string TerminalLabel { get; set; }
        public string TransactionType { get; set; }
        public string Currency { get; set; }
        //END: For ATM Withdrawal
    }
    public class Result
    {
        public string status { get; set; }
        public string version { get; set; }
    }

    public class RegisterDeviceRequest
    {
        [Required]
        public string username { get; set; }
        [Required]
        public string userId { get; set; }
        [Required]
        public string regId { get; set; }
        [Required]
        public string deviceType { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class AppUserResponse
    {
        public string BankCode { get; set; }
        public int ContactID { get; set; }
        public int CTRID { get; set; }
        public int CRID { get; set; }
        public int CAID { get; set; }
        public int CustomerID { get; set; }
        public string Email { get; set; }
        public string Username { get; set; }
        public string Name { get; set; }
        public string Mobile { get; set; }
        public string HomePhone { get; set; }
        public bool IsChild { get; set; }
        public string ContactPhoto { get; set; }
        public bool IsMerchant { get; set; }
        public string BillAddress { get; set; }
    }
}
