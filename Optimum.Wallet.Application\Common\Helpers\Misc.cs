﻿using Microsoft.AspNetCore.DataProtection;
using Microsoft.Extensions.Localization;
using System.Globalization;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using Optimum.Wallet.Core.Constants;

namespace Optimum.Wallet.Application.Common.Helpers
{
    public static class Misc
    {
        public static IDataProtectionProvider _rootProvider;

        public static string Decrypt(this string source, string purpose)
        {
            var protectedString = WebUtility.UrlDecode(source);

            IDataProtector protector = _rootProvider.CreateProtector(purpose);

            var unprotectedString = protector.Unprotect(protectedString);
            return unprotectedString;
        }

        public static string Encrypt(this string source, string purpose)
        {
            IDataProtector protector = _rootProvider.CreateProtector(purpose);

            var protectedString = protector.Protect(source);
            return WebUtility.UrlEncode(protectedString);
        }



        public static string HashMD5(this string source)
        {
            try
            {
                MD5 md5 = MD5.Create();
                var encoder = new UTF8Encoding();
                var originalBytes = encoder.GetBytes(source);
                var encodedBytes = md5.ComputeHash(originalBytes);
                source = BitConverter.ToString(encodedBytes).Replace("-", "");
            }
            catch (Exception ex)
            {

            }

            return source.ToLower();
        }

        public static string FormatCardNumber(this string source, bool showSpacing = true, bool showMasked = true, string format = "{0:0000 00'XX XXXX' 0000}", int startIndex = 6, int count = 6)
        {
            // Make sure the the given text is not empty
            if (string.IsNullOrWhiteSpace(source) || source.Replace(" ", "").Length != 16)
            {
                return source;
            }

            // Keep track of the text to be formatted
            var text = source.Replace(" ", "");

            // Should we remove the mask?
            if (!showMasked)
            {
                format = format.Replace("X", "0");
            }
            else
            {
                // If we're using the mask, get rid of the middle portion
                text = text.Remove(startIndex, count);
            }


            // Should we remove the spacing?
            if (!showSpacing) format = format.Replace(" ", "");

            var number = 0L;
            if (!long.TryParse(text, out number))
            {
                return source;
            }

            // Return the formatted string
            return string.Format(format, number);
        }

        public static string FormatAmount(this string source, int decimals = -1, string currency = "default")
        {
            var format = "";
            var number = 0m;
            return !decimal.TryParse(source, NumberStyles.Any, CultureInfo.InvariantCulture, out number) ? source : number.FormatAmount(decimals, currency);
        }

        public static string FormatAmount(this decimal source, int decimals = -1, string currency = "default", bool showCreditSign = false, string currencySeparator = " ", IStringLocalizer globalLocalizer = null)
        {
            currency = currency + "";
            //TODO:REMOVE AND FIX IT
            if ((currency + "").ToLower() == "default" && globalLocalizer != null)
            {
                WalletApplication.Currency = DapperHelper.Query<string>($"SELECT {globalLocalizer["Col_CurrencyFile_SwiftCode"]} FROM [Currency File] WHERE BranchID = @BranchId AND Active = 1", new { BranchId = 0 }, "Ledger").FirstOrDefault();
            }

            decimals = decimals == -1 ? WalletApplication.NoOfDecimals : decimals;
            if (Config.GetBooleanValue("IsMallatsApp"))
            {
                currency = (currency + "").ToLower() == "default" ? "BHD" : currency;
            }
            else
            {
                currency = (currency + "").ToLower() == "default" ? WalletApplication.Currency : currency;
            }


            var format = "";
            switch (decimals)
            {
                case 10:
                    format = "{0:#,##0.0000000000}";
                    break;
                case 9:
                    format = "{0:#,##0.000000000}";
                    break;
                case 8:
                    format = "{0:#,##0.00000000}";
                    break;
                case 7:
                    format = "{0:#,##0.0000000}";
                    break;
                case 6:
                    format = "{0:#,##0.000000}";
                    break;
                case 5:
                    format = "{0:#,##0.00000}";
                    break;
                case 4:
                    format = "{0:#,##0.0000}";
                    break;
                case 3:
                    format = "{0:#,##0.000}";
                    break;
                case 2:
                    format = "{0:#,##0.00}";
                    break;
                case 1:
                    format = "{0:#,##0.0}";
                    break;
                case 0:
                    format = "{0:#,##0}";
                    break;
            }

            if (!string.IsNullOrWhiteSpace(currency))
            {
                if (currency.ToLower() != "default")
                {
                    if (LanguageHelper.GetCurrentCultureRTL && !IsEnglish(currency))
                    {
                        format = format + currencySeparator + currency;
                    }
                    else
                    {
                        format = currency + currencySeparator + format;
                    }


                }
            }

            if (showCreditSign)
            {
                var creditSign = "<span style='visibility:hidden'>DB</span>";

                if (source < 0)
                {
                    creditSign = "CR";
                    source *= -1;
                }

                if (LanguageHelper.GetCurrentCultureRTL)
                {
                    format = creditSign + " " + format;
                }
                else
                {
                    format = format + " " + creditSign;
                }

            }

            return string.Format(CultureInfo.InvariantCulture, format, source);
        }
        public static bool IsEnglish(string input)
        {
            const string Lookup = " 0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ.,-=+";
            for (int i = 0; i < input.Length; i++)
            {
                if (!Lookup.Contains(input[i].ToString()))
                {
                    return false;
                }
            }
            return true;
        }

        public static string GetStringPortion(this string source, int startingChars, int endingChars)
        {
            var result = source;
            var length = source.Length;

            // If the starting digits is valid, get the specified portion
            if (startingChars > 0 && startingChars <= length)
            {
                result = source.Substring(0, startingChars);
            }

            // If the ending digits is not valid, we're done
            if (endingChars <= 0 || endingChars > length) return result;

            // Clear the results if the starting digits is not valid
            if (startingChars <= 0) result = "";

            // Otherwise, concatenate it with the value set above
            result += source.Substring(length - endingChars);

            // Finally, return the portion
            return result;
        }

        public static string ContactAvatarUrl(this string Photo)
        {
            var folderName = Config.GetStringValue("ContactPhotoFolder") ?? "ContactPhotoFolder";
            if (!string.IsNullOrWhiteSpace(Photo))
            {
                var mobileAppFolder = WalletApplication.MobileAppFolder;
                Photo = File.Exists(Path.Combine(mobileAppFolder, folderName, Photo)) ? Photo : "default-avatar.png";
            }
            else
            {
                Photo = "default-avatar.png";
            }


            var path = WalletApplication.PhotosPath + folderName.Replace("\\", "") + "//";
            return path + Photo;
        }

        public static string DecryptUrl(this string source)
        {
            return source.Decrypt(General.UrlEncryptPurpose);
        }
    }
}
