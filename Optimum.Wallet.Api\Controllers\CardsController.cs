using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Optimum.Wallet.Infrastructure.Repository;
using Optimum.Wallet.Application.Common;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Application.Interfaces;
using Optimum.Wallet.Application.Services;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Extensions;
using Optimum.Wallet.Application.Interfaces.Repositories;

namespace Optimum.Wallet.Api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    public class CardsController : BaseController<CardsController>
    {
        private readonly IStringLocalizer<AccountController> _loc;
        private readonly ICardRepository _cardRepository;
        private readonly IWalletRepository _walletRepository;
        private readonly IFormRepository _formRepository;
        private readonly IWebServices _webServices;
        private readonly CacheHelper _cacheHelper;

        public CardsController(IStringLocalizer<AccountController> stringLocalizer,
            ICardRepository cardRepository, CacheHelper cacheHelper, IWalletRepository walletRepository,
            IWebServices webServices, IFormRepository formRepository)
        {
            _loc = stringLocalizer;
            _cardRepository = cardRepository;
            Misc._rootProvider = DataProtectionProvider.Create(new DirectoryInfo(@"C:\\WalletKeysExample"));
            _cacheHelper = cacheHelper;
            _walletRepository = walletRepository;
            _webServices = webServices;
            _formRepository = formRepository;
        }

        [HttpGet("GetMenus")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<List<MenuViewModel>>))]
        public async Task<IActionResult> GetMenus()
        {
            var key = string.Format(CacheKeys.USER_CARD_MENU, CurrentUser.Username);
            var data = await _cacheHelper.GetCacheAsync<IEnumerable<MenuViewModel>>(key);
            if (data == null || RefreshCache)
            {
                data =
                   await DapperHelper.QueryAsync<MenuViewModel>(
                       $" SELECT ProductNameE AS 'Title' ,'#' AS 'Url' ,ProductId , IconClass as 'Icon' " +
                       " FROM  dbo.ProductServiceTbl " +
                       " WHERE ParentId = 26  and active = 1" +
                       " ORDER BY ProcessCode;"
                   );
                await _cacheHelper.SetCacheAsync(key, data, new TimeSpan(1, 0, 0, 0));
            }
            data.AsList().ForEach(a => a.PID = a.ProductId.ToString().Encrypt("PID"));
            return Ok(BaseResponse<List<MenuViewModel>>.Success(data.AsList()));
        }

        [HttpGet]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<List<CardDetailsViewModel>>))]
        public async Task<IActionResult> GetCards(string SelectedCAIDHash = "")
        {
            IEnumerable<CardDetailsViewModel> cardsList = null;
            var key = string.Format(CacheKeys.CARD_DETAILS, CurrentUser.Username) + SelectedCAIDHash;
            cardsList = await _cacheHelper.GetCacheAsync<IEnumerable<CardDetailsViewModel>>(key);

            if (cardsList == null || RefreshCache)
            {
                if (!WalletApplication.IsDummyData)
                {
                    if (!string.IsNullOrWhiteSpace(SelectedCAIDHash))
                    {
                        IEnumerable<CardDetailsViewModel> cardsList2 = await _cardRepository.GetCards<CardDetailsViewModel>(CurrentUser.ContactID, CurrentUser.CustomerID, -1, false, "", true, true, CurrentUser.Username);
                        var selectd = cardsList2.Where(r => r.HashedCAID == SelectedCAIDHash).FirstOrDefault();
                        List<CardDetailsViewModel> NewcardList = new List<CardDetailsViewModel>() { selectd };
                        NewcardList.AddRange(cardsList2.Where(r => r.HashedCAID != SelectedCAIDHash));
                        cardsList = NewcardList;
                    }
                    else
                    {
                        // Fetch the cards from our repository and pass them to the view
                        cardsList = await _cardRepository.GetCards<CardDetailsViewModel>(CurrentUser.ContactID, CurrentUser.CustomerID, -1, false, "", true, true, CurrentUser.Username);

                    }
                }
                else
                {
                    cardsList = new List<CardDetailsViewModel>() {
                    new CardDetailsViewModel() { CardHolderName = CurrentUser.Name, CardNumber = "1234 56XX XXXX 0001", CardExpiry = "02/24", CardId = 1, CardHolderCpr = CurrentUser.Username, CardTypeId = 1, ParentCardId = 1, Status = "1", CardType = "1", Balance = new CardBalance() { AvailableBalance = 1000 } },
                    new CardDetailsViewModel() { CardHolderName = CurrentUser.Name, CardNumber = "1234 56XX XXXX 0002", CardExpiry = "02/24", CardId = 1, CardHolderCpr = CurrentUser.Username, CardTypeId = 1, ParentCardId = 1, Status = "1", CardType = "1", Balance = new CardBalance() { AvailableBalance = 2500 } }
                };

                }
                await _cacheHelper.SetCacheAsync(key, cardsList,TimeSpan.FromMinutes(1));
            }
            return Ok(BaseResponse<List<CardDetailsViewModel>>.Success(cardsList.AsList()));
        }

        [HttpGet("GetCardCurrencies")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<List<WalletAccount>>))]
        public async Task<IActionResult> GetCardCurrencies(int CARDID)
        {
            var data = await _cardRepository.GetCurrencyWallets(CurrentUser.ContactID,CARDID);
            return Ok(BaseResponse<List<WalletAccount>>.Success(data));
        }

        [HttpGet("GetCardsById")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<List<CardDetailsViewModel>>))]
        public async Task<IActionResult> GetCardsById(int id)
        {
            CardDetailsViewModel card = null;
            if (WalletApplication.IsDummyData)
            {
                card = new CardDetailsViewModel() { };
            }
            else
            {
                // Get the main card details
                card = await _cardRepository.GetCardById<CardDetailsViewModel>(CurrentUser.ContactID, CurrentUser.CustomerID, id, false, "", true, true, CurrentUser.Username);
                if (card == null)
                {
                    return NotFound();
                }
                // Get the supplementary cards
                //card.SupplementaryCards = _cardRepository.GetCards<SuppCardDetailsViewModel>(CurrentUser.CustomerID, -1, true, card.CardNumber, true).ToList();
                card.SupplementaryCards = new List<SuppCardDetailsViewModel>()
                    {
                        new SuppCardDetailsViewModel {CardType="mastercard",SuppCardNumber ="511494XXXXXX5421",CardHolderName="Test" }
                    ,
                    new SuppCardDetailsViewModel {CardType="visa" ,SuppCardNumber="511494XXXXXX5422",CardHolderName="Test 2" }
                    };

            }
            // Update the main card balances
            // TODO: Save the updated balances to the database
            card.Balance = await _webServices.F5_GetBalance(CurrentUser.Username, card.CardNumber);

            //Pass the model to the view and render it
            return Ok(BaseResponse<CardDetailsViewModel>.Success(card));
        }

       

        [HttpGet("GetCardBalance")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<List<CardBalanceResponse>>))]
        public async Task<IActionResult> GetCardBalance()
        {
            var cardBalancesQuery = await _cardRepository.GetCards<CardDetailsViewModel>(CurrentUser.ContactID, CurrentUser.CustomerID, -1, false, "", true, true, CurrentUser.Username);
            //var cardBalances = cardBalancesQuery.ToDictionary(r => r.HashedCAID, r => r.Balance.AvailableBalance.FormatAmount(r.NoOfDecimals, r.SwiftCode));
            var data = cardBalancesQuery
                .Select(a => new CardBalanceResponse
                {
                    HashedCAID = a.HashedCAID,
                    AvailableBalance = a.Balance.AvailableBalance.FormatAmount(a.NoOfDecimals, a.SwiftCode)
                });
            return Ok(BaseResponse<List<CardBalanceResponse>>.Success(data.ToList()));
        }


        [HttpGet("GetCardAccountType")]
        public async Task<IActionResult> GetCardAccountType()
        {
            var data = await _cardRepository.GetCardAccountTypesAsync('C');
            return Ok(BaseResponse<List<WalletAccountType>>.Success(data.ToList()));
        }

        [HttpGet("GetCardCurrency")]
        public async Task<IActionResult> GetCardCurrency()
        {
            var data = await _cardRepository.GetCardCurrenciesAsync(CurrentUser.ContactID);
            return Ok(BaseResponse<List<WalletCurrency>>.Success(data.ToList()));
        }

        [HttpPost("StatementResult")]
        public async Task<IActionResult> StatementResult([FromBody] WalletStatementRequest walletStatementRequest)
        {
            var type = walletStatementRequest.type;
            var card = walletStatementRequest.card;
            var date = walletStatementRequest.date;
            var suppCards = walletStatementRequest.suppCards;
            var onlySupp = walletStatementRequest.onlySupp;
            var page = walletStatementRequest.page;
            var IsWalletStatement = walletStatementRequest.IsWalletStatement;
            var sCAID = walletStatementRequest.sCAID;
            var StmtType = walletStatementRequest.StmtType;

            var suppDict = new Dictionary<string, string>();
            try
            {
                suppDict = JsonConvert.DeserializeObject<Dictionary<string, string>>(suppCards);
            }
            catch (Exception ex)
            {

            }

            int CAID = CurrentUser.CAID;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (!int.TryParse(sCAID.Decrypt("CAID"), out CAID))
                {
                    CAID = CurrentUser.CAID;
                }
            }

            List<CardStatementModelView> model = null;
            if (IsWalletStatement)
            {
                model = await _formRepository.GetWalletStatementTransaction(CAID, date, StmtType);
            }
            else
            {
                model = await _webServices.GetStatementTransaction(
                    CurrentUser.Username,
                    card,
                    type, date, suppDict, onlySupp);
            }

            return Ok(BaseResponse<List<CardStatementModelView>>.Success(model));
        }

        [HttpGet("GetCardStatement")]
        public async Task<IActionResult> GetCardStatement(string CAID = "", string Type = "")
        {
            // Get the statement headers
            var resultQuery = await DapperHelper.QueryAsync<dynamic>("SET DATEFORMAT DMY;" +
                " SELECT -1 'Value' ,FORMAT(DATEADD(m, -1, getdate()),'MMMM') + ' ' +CONVERT(NVARCHAR, DATEPART(yyyy, DATEADD(m, -1, getdate()))) 'Text' " +
                "UNION " +
                "SELECT -2 'Value' ,FORMAT(DATEADD(m, -2, getdate()),'MMMM') + ' ' +CONVERT(NVARCHAR, DATEPART(yyyy, DATEADD(m, -2, getdate()))) 'Text' " +
                "UNION " +
                "SELECT -3 'Value' ,FORMAT(DATEADD(m, -3, getdate()),'MMMM') + ' ' +CONVERT(NVARCHAR, DATEPART(yyyy, DATEADD(m, -3, getdate()))) 'Text' " +
                "ORDER BY 1 DESC"
                            );
            var result = resultQuery.ToDictionary(row => (int)row.Value, row => (string)row.Text);
            return Ok(BaseResponse<Dictionary<int, string>>.Success(result));
        }

        [HttpPost("AddWalletCard")]
        public async Task<IActionResult> AddWalletCard([FromBody] AddWalletCardRequest addWalletCardRequest)
        {
            var Response = new ResponseViewModel() { };

            var id = addWalletCardRequest.id; 
            var type = addWalletCardRequest.type;
            var name = addWalletCardRequest.name;
            var brand = addWalletCardRequest.brand;

            // Validate the submitted data
            if (id <= 0 || type <= 0 || string.IsNullOrWhiteSpace(name) || !(new List<string> { "mastercard", "visa", "cup" }.Contains(brand.ToLower())))
            {
                Response.Message = "ErrorDataSubmitted";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            var currency = DapperHelper.Query<WalletCurrency>($@"
                SELECT CF.RecID AS Id, CF.CcyName AS [Name], CF.SwiftCode AS [Code], CF.Scan AS Image, CF.Active AS Active 
                FROM [Currency File] CF 
                WHERE CF.RecID = @Id AND CF.BranchID = @BranchId
                AND CF.Active = 1 AND CF.Cancel = 0
            ", new
            {
                Id = id,
                BranchId = WalletApplication.BranchId
            }, "Ledger").FirstOrDefault();

            if (currency == null)
            {
                Response.Message = "ErrorDataSubmitted";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            string bin = "522018";
            string branchCode = "01";
            var caid = await DapperHelper.ExecuteScalarAsync<int>($@"
                DECLARE @CAID INT;
                INSERT INTO dbo.CustomerAccountTbl (
				CRID, CustActId, CustAcCode, CustAcNameE, CustAcNameA, CRDays, CRLimit, StatusID, EffectiveDate, UserModified, DateModified, DateInput, 
				CACRExpDate, CABillingDate, CADiscountCus, CAStateDate, CAACOfficer, CAAccountNo, CAGoodsReceivedAct, CAAdvAccount, AutoInvoice, StatementBalance, 
				StatementRetention, RetentionFrequency, SubProfitSeg, AnalysisID, Photo, CATaxAccount, ADStatementBalance, GRStatementBalance, VATStatus, 
				VATCatID, TDSClassID, TransactionCount, PanNo, Tax_No, PayType, RefContactID, TotalDueBalance, PaySubType)
                SELECT TOP 1 '{CurrentUser.CRID}',CAS.CustActId,'{string.Format("{0}{1}", bin, branchCode)}' + FORMAT(CAS.SeqNo + 1,'D6') + FORMAT(CAS.CustAcctType,'D2') , @Name, @Name,CAS.DefCRDays,CAS.DefCRLimit,1,GETDATE(),'amthal',GETDATE(),GETDATE(),
                DATEADD(YEAR, 2, GETDATE()),NULL,0,GETDATE(),CSF.ACOfficer,CAS.ARAccountNo CAAccountNo,CAS.ARGoodsReceivedAct CAGoodsReceivedAct,CAS.ARAdvAccount CAAdvAccount,1,0,
                3,0,NULL,NULL,NULL,CAS.ARTaxAccount CATaxAccount,0,0,CAS.VATStatus,
                CAS.VATCatID,0,0,@CardBrand,NULL,2,@ContactID,0, @PaySubType
                FROM dbo.CustAcctSetupTbl CAS 
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON CAS.ARAccountNo = AF.AccountNo AND CAS.BranchID = '{WalletApplication.BranchId}' AND CAS.Active = 1 AND CAS.CustAcctType = @PaySubType
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy AND CF.SwiftCode = '{currency.Code}' AND CF.BranchID = CAS.BranchID
                INNER JOIN dbo.CustomerFile CSF ON CSF.RecID = '{CurrentUser.CustomerID}' 

                SET @CAID = SCOPE_IDENTITY();

                UPDATE CAS
                SET CAS.SeqNo = CAS.SeqNo + 1
                FROM dbo.CustAcctSetupTbl CAS
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON CAS.ARAccountNo = AF.AccountNo AND CAS.BranchID = '{WalletApplication.BranchId}' AND CAS.Active = 1 AND CAS.CustAcctType = @PaySubType
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy AND CF.SwiftCode = '{currency.Code}' AND CF.BranchID = CAS.BranchID
                INNER JOIN dbo.CustomerFile CSF ON CSF.RecID = '{CurrentUser.CustomerID}'

                SELECT @CAID
            ", new
            {
                ContactID = CurrentUser.ContactID,
                Name = name,
                PaySubType = type,
                CardBrand = brand.ToUpper()
            });

            Response.Message = caid > 0 ? "Card has been created successfully." : "Failed to create your card.";
            Response.Status = caid > 0 ? NotificationType.SUCCESS : NotificationType.ERROR;
            return Ok(BaseResponse<ResponseViewModel>.Success(Response));
        }

        [HttpGet("linkUnLinkAccount")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<List<WalletAccount>>))]
        public async Task<IActionResult> linkUnLinkAccount(string sId, string type, string sCard = "-1", string sCAID = "", string sRequestId = "", bool isMobile = false, bool isMerchantWallet = false)
        {
            int CAID = CurrentUser.CAID;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (!int.TryParse(sCAID.Decrypt("CAID"), out CAID))
                {
                    return Ok(BaseResponse<List<string>>.Failure());
                }
            }
            var walletAccounts = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, PaySubType: 1);
            var CurrencyWalletsQuery = await _walletRepository.GetCurrencyWallets(CurrentUser.ContactID, CAID);
            var CurrencyWallets = CurrencyWalletsQuery.Select(r => r.CAID).ToList();
            walletAccounts.Where(r => CurrencyWallets.Contains(r.CAID))
                .ToList()
                .ForEach(cc => cc.isLinked = true);

            return Ok(BaseResponse<List<WalletAccount>>.Success(walletAccounts.ToList()));
        }

        [HttpPost("linkUnLinkAccountPost")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> linkUnLinkAccountPost(Dictionary<string, bool> linkUnlinkIDs, string sCAID, string HashedCAID)
        {
            int CAID = -1;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (int.TryParse(sCAID.Decrypt("CAID"), out CAID))
                {
                    var walletAccounts = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, PaySubType: 1);
                    var sql = "";
                    if (walletAccounts.Any())
                    {
                        foreach (var item in walletAccounts)
                        {
                            if (linkUnlinkIDs[item.HashedCAID] == true)
                            {
                                sql += $"INSERT INTO dbo.CustomerAccountRelationshipTbl(MainCAID,RefCAID,DateModified,UserModified)VALUES({CAID},{item.CAID},GETDATE(),N'{CurrentUser.Username}');";
                            }
                        }
                    }

                    await DapperHelper.ExceuteAsync($"SET DATEFORMAT DMY; DELETE FROM CustomerAccountRelationshipTbl WHERE MainCAID={CAID}; {sql}");
                }
                else
                {
                    return Ok(BaseResponse<List<string>>.Failure());
                }
            }
            return Ok(BaseResponse<List<string>>.Success());
        }
    }
}
