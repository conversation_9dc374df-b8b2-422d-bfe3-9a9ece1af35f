using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Domain.Entities;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Application.Interfaces;
using Optimum.Wallet.Core.Extensions;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Core.Interfaces;
namespace Optimum.Wallet.Api.Controllers
{

    [Route("api/[controller]")]
    public class PaymentController : BaseController<PaymentController>
    {
        private readonly IGenericRepositoryAsync<PAYMENT_GATEWAY_TRAN_RESPONSE> _paymentResponseRepository;
        private readonly IPaymentService _paymentService;

        public PaymentController(IGenericRepositoryAsync<PAYMENT_GATEWAY_TRAN_RESPONSE> paymentResponseRepository, IPaymentService paymentService) 
        { 
            _paymentResponseRepository = paymentResponseRepository;
            _paymentService = paymentService;
        }

        [Authorize]
        [HttpPost("Submit")]
        public async Task<IActionResult> Submit([FromBody]PaymentSubmitModel paymentSubmitModel)
        {
            Logger.LogDebug($"[Payment/Submit-GET/{CurrentUser.Username}] Entered");

            ResponseViewModel Response = new ResponseViewModel()
            {
                Status = NotificationType.ERROR,
                Message = "ErrorOccurred"
            };

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12;

            var tranType = paymentSubmitModel.PayTranType;
            var cardNumber = "";
            decimal amount = -1; decimal targetAmount = -1; int formId = -1; int formTableId = -1; int caid = -1;

            if (!decimal.TryParse(paymentSubmitModel.TranAmount + "", out amount) || amount <= 0
               || !decimal.TryParse(paymentSubmitModel.TranTargetAmount + "", out targetAmount) || targetAmount <= 0
               || !int.TryParse(paymentSubmitModel.TranFormId + "", out formId)
               || !int.TryParse(paymentSubmitModel.TranCAID + "", out caid)
               || !int.TryParse(paymentSubmitModel.TranFormTableId + "", out formTableId)
               || string.IsNullOrWhiteSpace(tranType))
            {
                Response.Status = "InternalError";
                Response.Message = "ErrorPayRetrievePaymentDetails";
                Logger.LogError($"[Payment/Submit-GET/{CurrentUser.Username}] Amount: {amount} | CAID: {caid} | formId: {formId} | formTableId: {formTableId} | tranType: {tranType} | cardNumber: {cardNumber}");
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            iPayBenefitPipe gateway = new();

            var transactionId = -1;

            Logger.LogDebug($"[Payment/Submit-GET/{CurrentUser.Username}] Adding data to PAYMENT_GATEWAY_TRANSACTIONS");

            transactionId = DapperHelper.ExecuteScalar<int>(
                    " INSERT INTO dbo.PAYMENT_GATEWAY_TRANSACTIONS (PG_TYPE, PG_Amount, PG_User) VALUES" +
                    " (@PG_TYPE, @PG_Amount, @PG_User);" +
                    " SELECT CONVERT(INT, SCOPE_IDENTITY());",
                    new
                    {
                        PG_TYPE = PaymentGatewayTypes.DEBIT_CARD,
                        PG_Amount = amount,
                        PG_User = CurrentUser.ContactID + ""
                    },"Service");

            Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}] Added data to PAYMENT_GATEWAY_TRANSACTIONS. TransactionId: {transactionId}");

            Logger.LogDebug($"[Payment/Submit-GET/{CurrentUser.Username}] Adding data to PAYMENT_GATEWAY_TN_DETAILS");
            var paymentDetailsId = DapperHelper.ExecuteScalar<int>(
                " INSERT INTO dbo.PAYMENT_GATEWAY_TN_DETAILS (TID, BankCode, AmountToPay) VALUES" +
                " (@TID, @BankCode, @AmountToPay);" +
                " SELECT CONVERT(INT, SCOPE_IDENTITY());",
                new
                {
                    TID = transactionId,
                    BankCode = CurrentUser.BankCode,
                    AmountToPay = amount
                },"Service");
            Logger.LogDebug($"[Payment/Submit-GET/{CurrentUser.Username}] Added data to PAYMENT_GATEWAY_TN_DETAILS. DetailsID: {paymentDetailsId}");

            if (transactionId == -1)
            {
                Response.Status = "InternalError";
                Response.Message = "ErrorPayRetrievePaymentDetails";
                Logger.LogError($"[Payment/Submit-GET/{CurrentUser.Username}] Amount: {amount} | CAID: {caid} | formId: {formId} | formTableId: {formTableId} | tranType: {tranType} | cardNumber: {cardNumber}");
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            var gatewayParamsQuery = await DapperHelper.QueryAsync<PAYMENT_GATEWAY_PARAMS>(
                    " SELECT * FROM dbo.PAYMENT_GATEWAY_PARAMS WHERE HID = @HID",
                    new
                    {
                        HID = PaymentGatewayTypes.DEBIT_CARD_NEW_IPAY
                    }, "Service"
                );
            var gatewayParams = gatewayParamsQuery.ToList();

            Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}/{transactionId}] Initializing PG class.");
            gateway = new iPayBenefitPipe();
            gateway.setCardType("D");
            gateway.setAction("1");
            gateway.setCurrencyCode("048");

            gateway.setPassword(Config.GetStringValue("BenefitPaymentGatewayPassword"));
            gateway.setTranportalID(Config.GetStringValue("BenefitPaymentGatewayTranportalID"));
            gateway.setResourceKey(Config.GetStringValue("BenefitPaymentGatewayResourceKey"));

            gateway.setTrackId(CurrentUser.ContactID + "00" + transactionId);
            gateway.setAmt(amount + "");

            var formDetails = formTableId + "|" + transactionId + "|" + formId + "|" + tranType + "|" + cardNumber + "|" + caid + "|" + targetAmount;

            gateway.setUdf2(cardNumber);
            gateway.setUdf3(formDetails.Encrypt("PG_TRAN_VAL"));
            gateway.setUdf4(CurrentUser.Username);

            var errorUrl = Url.Action("GatewayError", null, new { PGType = (PaymentGatewayTypes.DEBIT_CARD_NEW_IPAY + "|" + PaymentGatewayTypes.DEBIT_CARD_NEW_IPAY+"|"+ transactionId).Encrypt("PGType") }, HttpContext.Request.Scheme);
            var responseUrl = Url.Action("GatewayResponse", null, new { PGType = (PaymentGatewayTypes.DEBIT_CARD_NEW_IPAY + "|" + PaymentGatewayTypes.DEBIT_CARD_NEW_IPAY + "|" + transactionId).Encrypt("PGType") }, HttpContext.Request.Scheme);

            gateway.setErrorURL(errorUrl);
            gateway.setResponseURL(responseUrl);


            Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}/{transactionId}] ErrorUrl: {errorUrl}.");
            Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}/{transactionId}] ResponseUrl: {responseUrl}.");

            // Inititate the gateway transaction
            Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}/{transactionId}] Initiating transaction.");
            var paymentResponse = gateway.PerformTransaction();
            Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}/{transactionId}] Initiated transaction: {paymentResponse.status}");


            Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}/{transactionId}] PaymentPage: {paymentResponse.result}.");
            if (paymentResponse.status == "1")
            {
                var eWalletFlag = gateway.GetType();
                var paymentGatewayLink = paymentResponse.result;
                var paymentId = gateway.getPaymentID();
                Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}/{transactionId}] PaymentPageLink: {paymentGatewayLink}.");

                Uri resultUri;
                if (Uri.TryCreate(paymentGatewayLink, UriKind.RelativeOrAbsolute, out resultUri))
                {
                    // Update the payment transaction details
                    Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}/{transactionId}] Updating transaction details.");
                    await DapperHelper.ExceuteAsync(
                        " UPDATE dbo.PAYMENT_GATEWAY_TRANSACTIONS" +
                        " SET PG_TRCODE = @PG_TRCODE," +
                        "     PG_PAYMENTID = @PG_PAYMENTID," +
                        "     PG_REQ_QUERYSTRING = @PG_REQ_QUERYSTRING," +
                        "     PG_TRNO = @PG_TRNO," +
                        "     PG_RAW_RESPONSE = @PG_RAW_RESPONSE" +
                        " WHERE ID = @ID",
                        new
                        {
                            PG_TRCODE = formDetails,
                            PG_PAYMENTID = paymentId,
                            PG_REQ_QUERYSTRING = paymentGatewayLink,
                            PG_TRNO = gateway.getTrackId(),
                            PG_RAW_RESPONSE = "" +
                        " | Error: " + gateway.getErrorText() + ": " + gateway.getErrorCode() +
                        " | Result: " + "",
                            ID = transactionId
                        }, "Service");
                    Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}/{transactionId}] Updated transaction details, redirecting...");
                    Response.Message = "Redirecting...";
                    Response.Status = NotificationType.SUCCESS;
                    Response.PaymentData = new PaymentSubmitModel
                    {
                        PaymentID = paymentId,
                        PaymentLink = paymentGatewayLink
                    };

                    // gateway = null;
                    HttpContext.Session.SetString("PGTypeID", "6");
                    await HttpContext.SignOutAsync();
                    HttpContext.Response.Redirect(paymentGatewayLink);
                }
            }
            Logger.LogInformation($"[Payment/SubmitNew-GET/{CurrentUser.Username}/{transactionId}] Error: {gateway.getErrorText() + ": " + gateway.getErrorCode()}.");

            Response.Status = "InternalError";
            Response.Message = "ErrorPaySubmitPaymentDetails";
            return Ok(BaseResponse<ResponseViewModel>.Failure());
        }

        [HttpPost("GatewayResponse")]
        [AllowAnonymous]
        public async Task<IActionResult> GatewayResponse([FromBody] IFormCollection fc,string PGType,string paymentId= "")
        {
            var transactionId = -1;

            if(fc.Count <= 0)
            {
                return NotFound();
            }

            Logger.LogDebug($"[Payment/GatewayResponseNew-POST/{transactionId}] Entered");
            var APGType = PGType?.Decrypt("PGType")?.Split('|');
            Logger.LogDebug($"[Payment/GatewayResponseNew-POST/{transactionId}] PGType: {PGType}");
            var PGTypeID = APGType.FirstOrDefault() ?? "";
            Logger.LogDebug($"[Payment/GatewayResponseNew-POST/{transactionId}] PGTypeID: {PGTypeID}");

            if (PGTypeID == PaymentGatewayTypes.DEBIT_CARD_NEW_IPAY + "")
            {
                Dictionary<string, string> decrypteddata = new Dictionary<string, string>();

                var PGID = APGType[1] ?? "";
                Logger.LogDebug($"[Payment/GatewayResponseNew-POST/{transactionId}] PGID: {PGID}");

                var gateway = new iPayBenefitPipe();
                Logger.LogDebug($"[Payment/GatewayResponseNew-POST/{transactionId}] Getting gateway parameters...");

                var gatewayParams = DapperHelper.Query<PAYMENT_GATEWAY_PARAMS>(
                       " SELECT * FROM dbo.PAYMENT_GATEWAY_PARAMS WHERE HID = @HID",
                       new
                       {
                           HID = PGTypeID
                       }, "Service"
                   ).ToList();
                Logger.LogDebug($"[Payment/GatewayResponseNew-POST/{transactionId}] Got gateway parameters: {gatewayParams.Count}");

                gateway.setPassword(Config.GetStringValue("BenefitPaymentGatewayPassword"));
                gateway.setTranportalID(Config.GetStringValue("BenefitPaymentGatewayTranportalID"));
                gateway.setResourceKey(Config.GetStringValue("BenefitPaymentGatewayResourceKey"));

                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] {JsonConvert.SerializeObject(fc)}");

                var tranData = fc["trandata"] + "";

                var result = -1;
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] tranData: {tranData}");
                if (!string.IsNullOrWhiteSpace(tranData))
                {
                    Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] Parsing gateway response...");
                    result = gateway.ParseResponse(tranData);
                    Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] Parsed gateway response: {result}");
                }

                paymentId = !string.IsNullOrWhiteSpace(gateway.getPaymentID()) ? gateway.getPaymentID() : paymentId;
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - paymentid: {paymentId}");

                var trackid = !string.IsNullOrWhiteSpace(gateway.getTrackId()) ? gateway.getTrackId() : fc["trackid"] + "";
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - trackid: {trackid}");

                var udf2 = (!string.IsNullOrWhiteSpace(gateway.getUdf2()) ? gateway.getUdf2() : fc["udf2"] + "");
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - udf2: {udf2}");

                var udf3 = (!string.IsNullOrWhiteSpace(gateway.getUdf3()) ? gateway.getUdf3() : fc["udf3"] + "");
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - udf3: {udf3}");

                var udf4 = (!string.IsNullOrWhiteSpace(gateway.getUdf4()) ? gateway.getUdf4() : fc["udf4"] + "");
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - udf4: {udf4}");

                var udf5 = (!string.IsNullOrWhiteSpace(gateway.getUdf5()) ? gateway.getUdf5() : fc["udf5"] + "");
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - udf5: {udf5}");

                var transId = (!string.IsNullOrWhiteSpace(gateway.getTransactionID()) ? gateway.getTransactionID() : fc["tranid"] + "");
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - transId: {transId}");

                var refNumber = (!string.IsNullOrWhiteSpace(gateway.getReferenceID()) ? gateway.getReferenceID() : fc["ref"] + "");
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - refNumber: {refNumber}");

                var gatewayResult = (!string.IsNullOrWhiteSpace(gateway.getResult()) ? gateway.getResult() : fc["result"] + "");
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - gatewayResult: {gatewayResult}");

                var responseCode = (!string.IsNullOrWhiteSpace(gateway.getAuthRespCode()) ? gateway.getAuthRespCode() : (gatewayResult == "CANCELED" ? "9111" : "911"));
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - responseCode: {responseCode}");

                var gatewayError = (!string.IsNullOrWhiteSpace(gateway.getErrorCode()) ? gateway.getErrorCode() : fc["Error"].ToString());
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - gatewayError: {gatewayError}");

                var gatewayErrorText = (!string.IsNullOrWhiteSpace(gateway.getErrorCode()) ? gateway.getErrorText() : fc["Error"].ToString());
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result - gatewayErrorText: {gatewayErrorText}");


                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] The request has returned a result: {paymentId}");

                // Lookup the form values: 1) Using the Payment ID, 2) Using the received UDF3
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] Fetching form values: {udf3}");
                var formDataQuery = await DapperHelper.QueryAsync<string>(
                    "SELECT PG_TRCODE FROM dbo.PAYMENT_GATEWAY_TRANSACTIONS WHERE PG_TRNO = @PG_TRNO",
                    new
                    {
                        PG_TRNO = trackid
                    }, "Service"
                );
                var formData = formDataQuery.FirstOrDefault();
                formData = string.IsNullOrWhiteSpace(formData) ? udf3.Decrypt("PG_TRAN_VAL") : formData;

                var formValues = formData.Split('|');
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] Form values: {JsonConvert.SerializeObject(formValues)}");


                if (formValues.Length < 3)
                {
                    Logger.LogError($"[Payment/GatewayResponseNew-POST/{transactionId}] Mismatch in form values.");
                    return Ok(BaseResponse<string>.Failure("<h1>Communication Error</h1><p>The payment gateway returned an invalid response. Please contact us to enquire about your transaction.</p>"));
                }

                int.TryParse(formValues[1], out transactionId);
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] Transaction Id found.");

                // Keep track of the data url
                var dataUrl = HttpContext.Request?.GetDisplayUrl();
                var dataUrlSeparator = "?";
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] dataUrl is: {dataUrl}.");

                var requestdetails = new List<PAYMENT_GATEWAY_TRAN_RESPONSE>();
                foreach (var item in fc)
                {
                    if (item.Key.ToUpper() == "UDF3")
                    {
                        requestdetails.Add(new PAYMENT_GATEWAY_TRAN_RESPONSE() { HID = transactionId, Key = item.Key, Value = formData });
                    }
                    else
                    {
                        requestdetails.Add(new PAYMENT_GATEWAY_TRAN_RESPONSE() { HID = transactionId, Key = item.Key, Value = item.Value });
                    }
                }

                decrypteddata.Add("paymentID", gateway.getPaymentID());
                decrypteddata.Add("result", gateway.getResult());
                decrypteddata.Add("transactionID", gateway.getTransactionID());
                decrypteddata.Add("referenceID", gateway.getReferenceID());
                decrypteddata.Add("trackID", gateway.getTrackId());
                decrypteddata.Add("amount", gateway.getAmt());
                decrypteddata.Add("Udf3", gateway.getUdf1());
                decrypteddata.Add("Udf2", gateway.getUdf2());
                decrypteddata.Add("Udf3", gateway.getUdf3());
                decrypteddata.Add("Udf4", gateway.getUdf4());
                decrypteddata.Add("Udf5", gateway.getUdf5());
                decrypteddata.Add("authCode", gateway.getAuthCode());
                decrypteddata.Add("postDate", gateway.getTranDate());

                foreach (var a in decrypteddata)
                {
                    requestdetails.Add(new PAYMENT_GATEWAY_TRAN_RESPONSE() { HID = transactionId, Key = a.Key, Value = a.Value });
                }


                await _paymentResponseRepository.AddRangeAsync(requestdetails);

                foreach (var a in HttpContext.Request.Headers)
                {
                    Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}/Decrypted : {a.Key} : {a.Value}");
                }


                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] Updating the transaction record.");
                DapperHelper.Exceute(
                        " UPDATE dbo.PAYMENT_GATEWAY_TRANSACTIONS" +
                    " SET QueryString = @QueryString" +
                    " WHERE ID = @ID",
                    new
                    {
                        QueryString = HttpContext.Connection.RemoteIpAddress + ",  UrlAndData: " + dataUrl,
                        ID = transactionId
                    }, "Service"
                );
                Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] Transaction saved.");

                int formId, formTableId, caid;
                int.TryParse(formValues[0], out formTableId);
                int.TryParse(formValues[2], out formId);
                var tranType = formValues[3];
                var cardNumber = formValues[4];
                int.TryParse(formValues[5], out caid);


                if (decrypteddata["result"].ToString().ToUpper() == "CAPTURED")
                {
                    var paidAmount = 0m;
                    decimal.TryParse(decrypteddata["amount"], out paidAmount);

                    await _paymentService.ProcessPayment(transactionId, decrypteddata["result"],
                        paidAmount, PaymentGatewayStatuses.APPROVED, "", 
                        formId, formTableId, true, 
                        paymentId, transId, refNumber, tranType, cardNumber, caid, paidAmount);

                    var redirectUrl = Url.Action("BenefitApproved", null, decrypteddata, Request.Scheme);
                    Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] Redirect to: {redirectUrl}");
                    return Ok("REDIRECT=" + redirectUrl);
                }
                else if (responseCode != "")
                {
                    await _paymentService.ProcessPayment(transactionId, responseCode, 0m, PaymentGatewayStatuses.DECLINED, "", formId, formTableId, 
                        true, paymentId, transId, refNumber, tranType, "", caid, 0m);

                    var redirectUrl = Url.Action("BenefitFailure", null, null, Request.Scheme);
                    Logger.LogInformation($"[Payment/GatewayResponseNew-POST/{transactionId}] Redirect to: {redirectUrl}");
                    return Ok("REDIRECT=" + redirectUrl);
                }                
            }

            return NotFound();
        }

        [HttpGet("GatewayError")]
        [AllowAnonymous]
        public IActionResult GatewayError(string PGType)
        {
            Logger.LogError($"[Payment/GatewayErrorNew-GET] {JsonConvert.SerializeObject(HttpContext.Request.RouteValues)}");
            return Ok("Error");
        }

        [HttpGet("BenefitApproved")]
        [AllowAnonymous]
        public IActionResult BenefitApproved(string responsecode,string transactionid)
        {
            return Ok();
        }

        [HttpGet("BenefitFailure")]
        [AllowAnonymous]
        public IActionResult BenefitFailure()
        {
            return Ok();
        }
    }
}
