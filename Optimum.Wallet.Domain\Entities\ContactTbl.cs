using System;
using System.Collections.Generic;

namespace Optimum.Wallet.Domain.Entities
{
    public class ContactTbl
    {
        public int ContactID { get; set; }
        public int CustomerID { get; set; }
        public string ContactEng { get; set; }
        public string ContactArb { get; set; }
        public int? TitleId { get; set; }
        public string ContactTel { get; set; }
        public string ContactFax { get; set; }
        public string ContactMobile { get; set; }
        public string ContactEmail { get; set; }
        public bool Active { get; set; }
        public string ContactExtn { get; set; }
        public string Password { get; set; }
        public DateTime? LastLogin { get; set; }
        public string RemoteHost { get; set; }
        public bool Login { get; set; }
        public int? CustomerTypeID { get; set; }
        public string Title { get; set; }
        public string LoginSessionId { get; set; }
        public bool ChangePassword { get; set; }
        public bool ItemPricingAccess { get; set; }
        public bool SamePriceforGroup { get; set; }
        public bool DisplayContractedItems { get; set; }
        public bool Acceptedtandc { get; set; }
        public int? ContactSalutation { get; set; }
        public int CsortCode { get; set; }
        public string UserModified { get; set; }
        public DateTime? DtInput { get; set; }
        public bool? Admin { get; set; }
        public int? TypeId { get; set; }
        public string ContactPhoto { get; set; }
        public string Cont_CPR { get; set; }
        public string BillAddress { get; set; }
        public string BillCountry { get; set; }
        public string BillCity { get; set; }
        public string BillZipCode { get; set; }
        public string BillNotes { get; set; }
        public string ContactOfficeTel { get; set; }
        public DateTime? ContactDOB { get; set; }
        public string EmpCompanyRecId { get; set; }
        public int LoginAttempt { get; set; }
        public bool LoginLock { get; set; }
        public bool Verify { get; set; }
        public string LastOtp { get; set; }
        public string ContactUsername { get; set; }
        public int? Parent { get; set; }
        public bool FingerprintLogin { get; set; }
        public bool FingerprintLock { get; set; }
        public string RedirectLogin { get; set; }
        public string RedirectMessage { get; set; }
        public string EntertainerAccess { get; set; }
        public DateTime? EntertainerStart { get; set; }
        public DateTime? EntertainerEnd { get; set; }
        public string EntertainerCode { get; set; }
        public string NotificationText { get; set; }
        public DateTime? PasswordChanged { get; set; }
        public DateTime? DateModified { get; set; }
        public string EmployeeId { get; set; }
        public string OfficeNo { get; set; }
        public bool? OptimumLogin { get; set; }
        public string MobilePassword { get; set; }
        public bool? MobileLogin { get; set; }
        public bool InProgress { get; set; }
        public string WindowsUser { get; set; }
        public int? DraftContactId { get; set; }
        public int? RegType { get; set; }
        public string TitleArb { get; set; }
        public bool IsSymbolsMoney { get; set; }
        public bool Gpsprocessed { get; set; }
        public int GpsnoOfTrials { get; set; }
        public bool LocationTracking { get; set; }
        public string ContactLatitude { get; set; }
        public string ContactLongitude { get; set; }
        public bool HasCard { get; set; }

        public CustomerFile CustomerFile { get; set; }
    }
}
