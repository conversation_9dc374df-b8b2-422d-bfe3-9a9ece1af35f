using Microsoft.Extensions.Configuration;
using System.Globalization;
using Optimum.Wallet.Core.Settings;

namespace Optimum.Wallet.Application.Common.Helpers
{
    public class Config
    {
        public static IConfiguration _configuration;

        public static List<SupportedCulture> _cultures => SupportedCultures();

        public static void Set(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public static string GetStringValue(string name)
        {
            return _configuration.GetValue<string>(name);
        }

        public static int GetIntValue(string name)
        {
            return _configuration.GetValue<int>(name);
        }

        public static bool GetBooleanValue(string name)
        {
            return _configuration.GetValue<bool>(name);
        }

        public static IConfigurationSection GetSection(string param)
        {
            return _configuration.GetSection(param);
        }

        public static string GetConnectionString(string name)
        {
            return _configuration.GetConnectionString(name);
        }

        public static List<CultureInfo> supportedCultureInfo => new List<CultureInfo>()
        {
                    new CultureInfo("en"),
                    new CultureInfo("ar")
        };

        public static List<SupportedCulture> SupportedCultures()
        {
            return new List<SupportedCulture>
            {
                new SupportedCulture { ID = 1, LanguageName = "English", Name = "en", FlagName = "us_flag.jpg"},
                new SupportedCulture { ID = 2, LanguageName = "عـربــى", Name = "ar", FlagName = "arabic_flag.png"}
            };
        }

        public static List<string> GetConfigCultures()
        {
            return GetSection("SupportedCulture")?.GetChildren()?.Select(a => a.Value).ToList();
        }

        public static int CultureID(string culture)
        {
            return _cultures.Where(a => a.Name == culture).Select(a => a.ID).FirstOrDefault();
        }

        public static string GetDefaultCultureName()
        {
            var config = GetConfigCultures();
            if (config == null || !config.Any())
            {
                return "en";
            }
            return _cultures.Where(a => config.Any(b => b == a.Name)).FirstOrDefault().Name;
        }

        public static List<SupportedCulture> AvailableLangs()
        {
            var config = GetConfigCultures();
            if (config == null || !config.Any())
            {
                return _cultures.Where(a => a.ID == 1).ToList();
            }
            return _cultures.Where(a => config.Any(b => b == a.Name)).ToList();
        }
    }
}
