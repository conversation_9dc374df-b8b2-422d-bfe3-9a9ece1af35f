﻿using System.Collections.Generic;
using System.Linq;

namespace Optimum.Wallet.Core.Constants
{
    public static class General
    {
        public const string InputPrefix = "form_input_id_";
        public const string UrlEncryptPurpose = "SECURE_URL";
        public const string CAIDEncryptPurpose = "CAID";
        public const string BeneficiaryEncryptPurpose = "BENEFICIARY";
    }

    // Move this to the DB !
    public static class WalletActions
    {
        public static Dictionary<int, string> buttonColorStyling = new Dictionary<int, string>
        {
            { 500053, "bg-primary" },
            { 500050, "bg-secondary" },
            { 500051, "bg-success" },
            { 500072, "bg-danger" },
            { 500071, "bg-warning" },
            { 500062, "bg-secondary-alert"},
            { 500064, "bg-info" },
            { 500052, "bg-dark" },
            { -9999, "bg-danger" },
            { -9998, "bg-warning" }
        };

        public static Dictionary<int, string> iconStyling = new Dictionary<int, string>
        {
            { 500053, "cash-outline" },
            { 500050, "arrow-forward-outline" },
            { 500051, "arrow-down-outline" },
            { 500072, "pricetags-outline" },
            { 500071, "receipt-outline" },
            { 500062, "copy-outline"},
            { 500064, "scan-circle-outline" },
            { 500052, "qr-code-outline" },
            { -9999, "copy-outline" },
            { -9998, "copy-outline" }
        };
    }

    public static class Wallets
    {
        private static Dictionary<string, string> codes = new Dictionary<string, string> {
            { "BHD", "048" },
            { "USD", "840" },
            { "GBP", "826" },
            { "EUR", "978" },
            { "SAR", "682" },
            { "AED", "784" }
        };

        private static Dictionary<string, int> decimals = new Dictionary<string, int> {
            { "BHD", 3 },
            { "USD", 2 },
            { "GBP", 2 },
            { "EUR", 2 },
            { "SAR", 2 },
            { "AED", 2 }
        };

        private static Dictionary<string, string> currencies = new Dictionary<string, string>
        {
            { "971", "AFN" },
            { "008", "ALL" },
            { "012", "DZD" },
            { "840", "USD" },
            { "978", "EUR" },
            { "973", "AOA" },
            { "951", "XCD" },
            { "032", "ARS" },
            { "051", "AMD" },
            { "533", "AWG" },
            { "036", "AUD" },
            { "944", "AZN" },
            { "044", "BSD" },
            { "048", "BHD" },
            { "050", "BDT" },
            { "052", "BBD" },
            { "974", "BYR" },
            { "084", "BZD" },
            { "952", "XOF" },
            { "060", "BMD" },
            { "064", "BTN" },
            { "356", "INR" },
            { "068", "BOB" },
            { "984", "BOV" },
            { "977", "BAM" },
            { "072", "BWP" },
            { "578", "NOK" },
            { "986", "BRL" },
            { "096", "BND" },
            { "975", "BGN" },
            { "108", "BIF" },
            { "132", "CVE" },
            { "116", "KHR" },
            { "950", "XAF" },
            { "124", "CAD" },
            { "136", "KYD" },
            { "990", "CLF" },
            { "152", "CLP" },
            { "156", "CNY" },
            { "170", "COP" },
            { "970", "COU" },
            { "174", "KMF" },
            { "976", "CDF" },
            { "554", "NZD" },
            { "188", "CRC" },
            { "191", "HRK" },
            { "931", "CUC" },
            { "192", "CUP" },
            { "532", "ANG" },
            { "203", "CZK" },
            { "208", "DKK" },
            { "262", "DJF" },
            { "214", "DOP" },
            { "818", "EGP" },
            { "222", "SVC" },
            { "232", "ERN" },
            { "230", "ETB" },
            { "238", "FKP" },
            { "242", "FJD" },
            { "953", "XPF" },
            { "270", "GMD" },
            { "981", "GEL" },
            { "936", "GHS" },
            { "292", "GIP" },
            { "320", "GTQ" },
            { "826", "GBP" },
            { "324", "GNF" },
            { "328", "GYD" },
            { "332", "HTG" },
            { "340", "HNL" },
            { "344", "HKD" },
            { "348", "HUF" },
            { "352", "ISK" },
            { "360", "IDR" },
            { "960", "XDR" },
            { "364", "IRR" },
            { "368", "IQD" },
            { "376", "ILS" },
            { "388", "JMD" },
            { "392", "JPY" },
            { "400", "JOD" },
            { "398", "KZT" },
            { "404", "KES" },
            { "408", "KPW" },
            { "410", "KRW" },
            { "414", "KWD" },
            { "417", "KGS" },
            { "418", "LAK" },
            { "422", "LBP" },
            { "426", "LSL" },
            { "710", "ZAR" },
            { "430", "LRD" },
            { "434", "LYD" },
            { "756", "CHF" },
            { "446", "MOP" },
            { "807", "MKD" },
            { "969", "MGA" },
            { "454", "MWK" },
            { "458", "MYR" },
            { "462", "MVR" },
            { "929", "MRU" },
            { "480", "MUR" },
            { "965", "XUA" },
            { "484", "MXN" },
            { "979", "MXV" },
            { "498", "MDL" },
            { "496", "MNT" },
            { "504", "MAD" },
            { "943", "MZN" },
            { "104", "MMK" },
            { "516", "NAD" },
            { "524", "NPR" },
            { "558", "NIO" },
            { "566", "NGN" },
            { "512", "OMR" },
            { "586", "PKR" },
            { "590", "PAB" },
            { "598", "PGK" },
            { "600", "PYG" },
            { "604", "PEN" },
            { "608", "PHP" },
            { "985", "PLN" },
            { "634", "QAR" },
            { "946", "RON" },
            { "643", "RUB" },
            { "646", "RWF" },
            { "654", "SHP" },
            { "882", "WST" },
            { "930", "STN" },
            { "682", "SAR" },
            { "941", "RSD" },
            { "690", "SCR" },
            { "694", "SLL" },
            { "702", "SGD" },
            { "994", "XSU" },
            { "090", "SBD" },
            { "706", "SOS" },
            { "728", "SSP" },
            { "144", "LKR" },
            { "938", "SDG" },
            { "968", "SRD" },
            { "748", "SZL" },
            { "752", "SEK" },
            { "947", "CHE" },
            { "948", "CHW" },
            { "760", "SYP" },
            { "901", "TWD" },
            { "972", "TJS" },
            { "834", "TZS" },
            { "764", "THB" },
            { "776", "TOP" },
            { "780", "TTD" },
            { "788", "TND" },
            { "949", "TRY" },
            { "934", "TMT" },
            { "800", "UGX" },
            { "980", "UAH" },
            { "784", "AED" },
            { "997", "USN" },
            { "940", "UYI" },
            { "858", "UYU" },
            { "860", "UZS" },
            { "548", "VUV" },
            { "937", "VEF" },
            { "704", "VND" },
            { "886", "YER" },
            { "967", "ZMW" },
            { "932", "ZWL" }
        };

        public static bool Exists(string currency)
        {
            return codes.ContainsKey(currency.ToUpper());
        }

        public static string GetWalletCode(string currency)
        {
            // Normalize the currency
            currency = currency.ToUpper();

            // Check if wallet code exists and return it
            if (codes.ContainsKey(currency))
            {
                return codes[currency];
            }

            // Otherwise, code doesn't exist, return the given currency
            return currency;
        }

        public static string GetWalletCurrency(string code)
        {
            // Check if wallet code exists and return its currency
            if (codes.ContainsValue(code))
            {
                return codes.FirstOrDefault(r => r.Value == code).Key;
            }

            // Check if currency code exists and return it
            if (currencies.ContainsKey(code))
            {
                return currencies[code];
            }

            // Otherwise, code doesn't exist, return the given code
            return code;
        }

        public static int GetWalletCurrencyDecimals(string currency)
        {
            // Normalize the currency
            currency = currency?.ToUpper() ?? "";

            // Check if currency decimal field exists and return it
            if (codes.ContainsKey(currency))
            {
                return decimals[currency];
            }

            // Otherwise, decimal field doesn't exist, return the default
            return -1;
        }
    };

    public static class ControlTypes
    {
        public const int ACCOUNT_CLOSE_DATE = 47;
        public const int ACCOUNT_NUMBER = 30;
        public const int ACCOUNT_OPEN_DATE = 46;
        public const int AMOUNT = 43;
        public const int AMOUNT_TO_PAY = 77;
        public const int APPLICANT_NAME = 50;
        public const int APPLICATION_DATE = 53;
        public const int VALUE_DATE = 406;
        public const int APPROVAL_CODE = 45;
        public const int BALANCE_AVAILABLE = 41;
        public const int BALANCE = 75;
        public const int BALANCE_CLOSING = 38;
        public const int BALANCE_HOLD = 40;
        public const int BALANCE_TODAY = 39;
        public const int BALANCE_DUE_DATE = 205;
        public const int CANCELLATION_REASON = 57;
        public const int REASON = 67;
        public const int CARD_EXPIRY = 33;
        public const int CARD_HOLDER_NAME = 32;
        public const int CARD_NUMBER = 31;
        public const int OTHER_CARD_NUMBER = 48;
        public const int MAIN_CARD_NUMBER = 196;
        public const int CATEGORY = 29;
        public const int CPR = 49;
        public const int USER_CPR = 141;
        public const int DIRECT_DEBIT_AMOUNT = 58;
        public const int LIMIT_CASH = 36;
        public const int LIMIT_CASH_USED = 37;
        public const int LIMIT_CREDIT = 34;
        public const int LIMIT_CREDIT_USED = 35;
        public const int LIMIT_REQUIRED = 56;
        public const int LIMIT_REQUIRED_SUPP = 182;
        public const int CURRENT_LIMIT = 64;
        public const int MOBILE_NUMBER = 51;
        public const int EMAIL_ADDRESS = 61;
        public const int HOME_NUMBER = 60;
        public const int USER_MOBILE_NUMBER = 59;
        public const int PRODUCT = 28;
        public const int STATUS = 55;
        public const int SUPP_CARD_HOLDER_NAME = 70;
        public const int SUPP_CARD_NUMBER = 80;
        public const int SUPP_CPR = 69;
        public const int SUPP_LIMIT = 71;
        public const int SUPP_NAME = 68;
        public const int SUPP_RELATION = 72;
        public const int TOTAL_INCOME = 52;
        public const int TRANSACTION_DATE = 44;
        public const int UTILITY_TYPE = 42;
        public const int BALANCE_MINIMUM = 82;
        public const int PAYMENT_REF_NO = 78;
        public const int STATUS_MESSAGE = 208;
        public const int FFP_CREDIT_POINTS = 74;
        public const int FFP_NUMBER = 65;
        public const int EMAIL_BODY_ID = 83;
        public const int EMAIL_RECIPIENT_ID = 84;
        public const int EMAIL_CC_ID = 85;
        public const int EMAIL_ATTACHMENT_ID = 86;
        public const int EMAIL_AUTO_SEND = 87;
        public const int WEB_SERVICE_FUNCTION = 88;
        public const int WEB_SERVICE_PARAMS = 89;
        public const int EMAIL_SUBJECT_ID = 90;
        public const int DELIVERY_METHOD = 92;
        public const int SMS_RECIPIENT = 177;
        public const int SMS_SENDER = 178;
        public const int SMS_BODY = 179;
        public const int PARENT_CARD_ID = 183;
        public const int UPGRADE_TO = 195;

        public const int ENROLLMENT_DATE = 184;
        public const int TOTAL_REWARDED = 185;
        public const int TOTAL_AVAILABLE = 187;
        public const int TOTAL_EXPIRED = 188;
        public const int TOTAL_REDEEMED = 189;
        public const int TOTAL_ADJUSTED = 190;
        public const int VENDOR_NAME = 191;
        public const int POINTS_REDEEMED = 192;

        public const int BIRTH_DATE = 139;
        public const int HOME_ADDRESS = 197;
        public const int ADDRESS_PROOF = 198;

        public const int REQUEST_IP = 127;
        public const int SIGNATURE = 181;

        public const int TRANS_NOTIFICATIONS = 97;
        public const int STAMT_NOTIFICATIONS = 98;
        public const int ANCMT_NOTIFICATIONS = 99;
        public const int MRKTG_NOTIFICATIONS = 206;
        public const int REQUT_NOTIFICATIONS = 481;

        public const int TERMS_POPUP = 200;

        public const int CONTENT = 202;
        public const int TYPE = 203;
        public const int READ = 204;

        public const int REQUEST_FROM = 419;

        public const int SEND_MONEY_SOURCE = 458;

        public const int PERSONAL_ID = 492;

        public const int RECEIPT_ID = 416;

        public const int CAID_FROM = 416;
        public const int CAID_TO = 416;
        public const int FROM = 407;

        public const int PAYMENT_OPTION = 494;

        public const int WALLET_SEND_TO_TYPE = 499;
        public const int WALLET_ACCOUNT = -1;

        public const int SMART_MONEY_TAX_INVOICE_ID = 500;
        public const int MERCHANT_NAME = 497;

        //BEG: WPS EMPLOYEE KYC FIELDS
        public const int WPS_KYC_FULL_NAME = 216;
        public const int WPS_KYC_DOB = 306;
        public const int WPS_KYC_MOBILE = 308;
        public const int WPS_KYC_EMAIL = 309;
        public const int WPS_KYC_ADDRESS = 311;
        public const int WPS_KYC_CPR_EXPIRY = 315;
        public const int WPS_PRODUCT = 312;
        public const int WPS_CARD_EMBOSSING = 320;
        //END: WPS EMPLOYEE KYC FIELDS

        //BEG: ATM_Withdrawal_Money FIELDS
        //Local
        //public const int ATM_Withdrawal_Money_Date = 1528;
        //public const int ATM_Withdrawal_Money_Time = 1529;
        //public const int ATM_Withdrawal_Money_TransactionAmount = 411;
        //public const int ATM_Withdrawal_Money_BillNumber = 1523;
        //public const int ATM_Withdrawal_Money_StoreLabel = 1524;
        //public const int ATM_Withdrawal_Money_CustomerLabel = 1525;
        //public const int ATM_Withdrawal_Money_TerminalLabel = 1526;        
        //public const int ATM_Withdrawal_Money_TransactionType = 1527;
        //BFC UAT
        public const int ATM_Withdrawal_Money_Date = 1534;
        public const int ATM_Withdrawal_Money_Time = 1526;
        public const int ATM_Withdrawal_Money_TransactionAmount = 411;
        public const int ATM_Withdrawal_Money_BillNumber = 1527;
        public const int ATM_Withdrawal_Money_StoreLabel = 1528;
        public const int ATM_Withdrawal_Money_CustomerLabel = 1529;
        public const int ATM_Withdrawal_Money_TerminalLabel = 1530;
        public const int ATM_Withdrawal_Money_TransactionType = 1531;
        //END: ATM_Withdrawal_Money FIELDS

        //BEG: Smart Money Registeration Fields
        public const int SM_REGESTERATION_FullName = 216;
        public const int SM_REGESTERATION_EmployeeDOB = 306;
        public const int SM_REGESTERATION_Mobile = 308;
        public const int SM_REGESTERATION_Email = 309;
        public const int SM_REGESTERATION_EmployeeNationality = 310;
        public const int SM_REGESTERATION_EmployeeFullAddress = 311;
        public const int SM_REGESTERATION_EmployeeCPR = 314;
        public const int SM_REGESTERATION_EmployeeCPRExpiry = 315;
        public const int SM_REGESTERATION_IDType = 453;
        //END: Smart Money Registeration Fields

        //BEG:Customer KYC
        public const int CUST_KYC_FULL_NAME = 216;
        public const int CUST_KYC_CPR = 484;
        public const int CUST_KYC_EXP = 485;
        public const int CUST_KYC_DOB = 482;
        public const int CUST_KYC_GENDER = 217;
        public const int CUST_KYC_NATIONALITY = 483;
        public const int CUST_KYC_PASS_NO = 486;
        public const int CUST_KYC_PASS_EXP = 487;
        public const int CUST_KYC_ADDRESS = 488;
        public const int CUST_KYC_MOBILE = 489;
        public const int CUST_KYC_CORP_SALARY_LETTER = 1574;
        public const int CUST_KYC_WALLET_PURPOSE = 1575;

        public const int CUST_KYC_CPR_PROOF__UPLOAD = 454;
        //UAT
        public const int CUST_KYC_FACE_PROOF_UPLOAD = 1567;
        public const int CUST_KYC_ADDRESS_PROOF_UPLOAD = 1568;
        public const int CUST_KYC_SHUFTOPRO_STATUS = 1570;
        public const int KYC_ADDRESS_PROOF = 1568;
        public const int KYC_VERIFY_ADDRESS = 1576;
        //LOCAL
        //public const int CUST_KYC_FACE_PROOF_UPLOAD = 1530;
        //public const int CUST_KYC_ADDRESS_PROOF_UPLOAD = 1531;
        //public const int CUST_KYC_SHUFTOPRO_STATUS = 1532;
        //END:Customer KYC

        // ShuftiPro KYC
        public const int SHUFTIPRO_KYC_STATUS = 1569;
        public const int SHUFTIPRO_KYC_REFERENCE = 1571;
        public const int SHUFTIPRO_KYC_DECLINE_REASON = 1572;
        public const int SHUFTIPRO_KYC_FACE_CONFIDENCE = 1573;
        public const int SHUFTIPRO_KYC_PLACE_OF_BIRTH = 219;
        public const int SHUFTIPRO_KYC_ID_FRONT = 213;
        public const int SHUFTIPRO_KYC_ID_BACK = 214;
        public const int SHUFTIPRO_PASSPORT_COPY = 337;

        public const string SOURCE_CAID = "SourceCAID";
        public const string TARGET_CAID = "TargetCAID";
        public const string IS_SOURCE_AMOUNT = "IsSourceAmount";
        public const int SOURCE_WALLET = 1539;
        public const int TARGET_WALLET = 1540;
        public const int SOURCE_CARD = 11506;
        public const int TARGET_CARD = 11507;

        //BEG:TASK FORM
        public const int TASK_NAME = 1535;
        public const int TASK_AMOUNT = 1536;
        public const int TASK_FREQUENCY = 1537;
        //END:TASK FORM
        public const int MONTH = 1551;
        public const int CPR_ID = 1559;

        public const int BENEFICIARY_IBAN = 295;
    }

    public static class ControlDataTypes
    {
        public const string EmailAttachment = "emailattach";
        public const string UrlRedirect = "urlredirect";
        public const string UrlSubmit = "urlsubmit";
        public const string WebServiceFunction = "webservfunc";
        public const string WebServiceParams = "webservparam";
        public const string Wallet = "wallet";
        public const string Card = "card";
    }

    public static class PageTypes
    {
        public const int PARENT_PAY_CARDS = 21;
        public const int PAY_MY_CARDS = 22;
        public const int PAY_OTHER_CARDS = 24;

        public const int PARENT_PRIVACY_SETTINGS = 31;
        public const int APP_SETTINGS = 33;
        public const int SMS_SETTINGS = 34;
        public const int EMAL_SETTINGS = 35;

        public const int PARENT_REQUESTS = 8;
        public const int NEW_CARD_REQUEST = 3;
        public const int LIMIT_INC_REQUEST = 4;
        public const int LIMIT_DEC_REQUEST = 5;
        public const int UPGRADE_REQUEST = 6;
        public const int CANCEL_REQUEST = 20;
        public const int DB_REQUEST = 7;
        public const int ACTIVATE_REQUEST = 9;
        public const int DISPUTE_FORM = 10;
        public const int TEMP_LIMIT_DEC_REQUEST = 11;
        public const int FFP_ENROLL_REQUEST = 12;
        public const int REPLACE_REQUEST = 13;
        public const int PIN_REPLACE_REQUEST = 14;
        public const int SUPP_CARD_REQUEST = 15;
        public const int STOP_CARD_REQUEST = 52;
        public const int BLOCK_CARD_REQUEST = 27;
        public const int CHILD_UNBLOCK_CARD_REQUEST = 112;
        public const int CHILD_BLOCK_CARD_REQUEST = 114;

        public const int PARENT_LOYALTY = 16;
        public const int FFP_PROGRAM_PAGE = 17;
        public const int THAMEEN_PROGRAM_PAGE = 55;

        public const int PARENT_UTILITY_PAYMENT = 18;
        public const int UTILITY_PAYMENT = 19;

        public const int PARENT_SERVICES = 26;
        public const int CHILD_SERVICES_PARENT = 106;
        public const int PIN_CHANGE_SERVICE = 50;
        public const int ACTIVATE_E_STATEMENT = 51;
        public const int STOP_CARD_SERVICE = 52;
        public const int MAX_SECURE_SERVICE = 53;
        public const int SET_SUPP_LIMIT_SERVICE = 54;

        public const int PARENT_USER_PROFILE = 28;
        public const int PERSONAL_INFO_PAGE = 29;

        public const int PARENT_SECURITY = 36;
        public const int CHANGE_PASS_PAGE = 30;
        public const int NAV_HISTORY_PAGE = 38;
        public const int USR_REGISTR_PAGE = 61;

        public const int NOTIFICATIONS_PAGE = 60;

        public const int WALLET_ADD_MONEY = 76;
        public const int WALLET_PAY_MERCHANT = 77;
        public const int WALLET_SEND_MONEY = 78;
        public const int WALLET_REQUEST_MONEY = 79;
        public const int WALLET_GENERATE_QR = 80;
        public const int WALLET_SCAN_AND_PAY = 94;
        public const int WALLET_MERCHANT_WITHDRAW_MONEY = -500;

        public const int CARD_ADD_MONEY = 91;
        public const int CARD_SEND_MONEY = 92;

        public const int SERVICE_SMART_MONEY = 87;

        public const int EMPLOYEE_KYC = 66;

        public const int CUSTOMER_KYC = 90;
        public const int SHUFTIPRO_KYC = 10078;
        public const int RETAIL_CUSTOMER_KYC = 10079;

        public const int ATM_WITHDRAWAL_MONEY = 95;

        //PARENT & CHILD FORMS
        public const int PARENT_CHILD_TASK = 99;
        public const int PARENT_CHILD_GOAL = 100;
        public const int PARENT_CHILD_CHILD = 101;
        public const int PARENT_CHILD_BONUS = 115;
        public const int PARENT_CHILD_PAY_FOR_TASK = 116;
        public const int PARENT_CHILD_PAY_FOR_GOAL = 117;
        public const int PARENT_CHILD_PAY_ALLOWANCE = 118;
    }

    public static class ProductTypes
    {
        public const int POWERCARD_MAIN = 2;
        public const int POWERCARD_SUPP = 25;
    }

    public static class ProductValues
    {
        public static int GetValueByName(string name)
        {
            // List of possible values
            var values = new[]
            {
                "CUP",
                "VISA",
                "MASTERCARD",
                "MC",
                "JCB"
            };

            // Test if the given string has one of the possible values
            var result = values.FirstOrDefault(name.ToUpper().Contains);

            switch (result)
            {
                case "VISA":
                    return 88;
                case "MASTERCARD":
                case "MC":
                    return 89;
                case "JCB":
                    return 90;
                case "CUP":
                    return 91;
                default:
                    return -1;
            }
        }
    }

    public static class CategoryValues
    {
        public static int GetValueByName(string name)
        {
            // List of possible values
            var values = new[]
            {
                "CORPORATE WORLD",
                "TAYSEER CLASSIC",
                "TAYSEER CLASIC",
                "TAYSEER CLAS",
                "TAYSEER CL",
                "TAYSEER GOLD",
                "TAYSEER GLD",
                "TAYSEER PLATINUM",
                "TAYSEER PLATNM",
                "TAYSEER PLNM",
                "PRE PAID G20",
                "PRE PAID G30",
                "PRE PAID G50",
                "PRE PAID G100",
                "PLATINUM BLACK",
                "PLATNM BLACK",
                "PL BLACK",
                "WORLD ELITE",
                "CLASSIC",
                "GOLD",
                "PLATINUM",
                "STUDENT",
                "CREDINET",
                "DANAT",
                "CARDY",
                "INFINITE",
                "WORLD",
                "SIGNATURE",
                "CORPORATE",
                "CR",
                "ILEAD",
                "MALLATS"
            };

            // Test if the given string has one of the possible values
            var result = values.FirstOrDefault(name.ToUpper().Contains);

            switch (result)
            {
                case "MALLATS":
                    return 1233;
                case "CR":
                    return 1232;
                case "CORPORATE WORLD":
                    return 418;
                case "TAYSEER CLASSIC":
                case "TAYSEER CLASIC":
                case "TAYSEER CLAS":
                case "TAYSEER CL":
                    return 191;
                case "CLASSIC":
                    return 190;
                case "TAYSEER GOLD":
                case "TAYSEER GLD":
                    return 193;
                case "GOLD":
                    return 192;
                case "TAYSEER PLATINUM":
                case "TAYSEER PLATNM":
                case "TAYSEER PLNM":
                    return 195;
                case "PLATINUM":
                    return 194;
                case "PRE PAID G20":
                    return 196;
                case "PRE PAID G30":
                    return 197;
                case "PRE PAID G50":
                    return 198;
                case "PRE PAID G100":
                    return 199;
                case "STUDENT":
                    return 200;
                case "CREDINET":
                    return 201;
                case "DANAT":
                    return 202;
                case "CARDY":
                    return 203;
                case "INFINITE":
                    return 205;
                case "WORLD ELITE":
                    return 209;
                case "WORLD":
                    return 206;
                case "SIGNATURE":
                    return 207;
                case "PLATINUM BLACK":
                case "PLATNM BLACK":
                case "PL BLACK":
                    return 204;
                case "CORPORATE":
                    return 208;
                case "ILEAD":
                    return 1505;
                default:
                    return -1;
            }
        }
    }
    public static class StatusValues
    {
        public const int ACTIVE = 129;
        public const int EXPIRED = 403;
        public const int INACTIVE = 130;

        public const int APPROVED = 99;
        public const int DECLINED = 100;
        public const int AWAITING = 103;
        public const int PENDING = 226;

        // Define a default value that will be used throughout the system
        public const int DEFAULT = PENDING;

        public const int COMPLETED = 1496;
        public const int FOR_APPROVAL = 1497;
        public const int INCOMPLETED = 1498;
    }

    public static class PickerValues
    {
        public const int YES = 120;
        public const int NO = 121;

        public const int APPROVED = 99;
        public const int DECLINED = 100;

        public const int TRANSACTION = 405;
        public const int STATEMENT = 406;
        public const int ANNOUNCEMENT = 407;
        public const int MARKETING = 416;
        public const int REQUEST = 1235;
        public const int REQUEST_SENT = 10979;
        public const int SPLIT_PAYMENT_REQUEST = 10980;

        public const int SMS = 402;

        public const int PAYMENT_OPTION_WALLET = 1239;
        public const int PAYMENT_OPTION_CARD = 1240;
        public const int PAYMENT_OPTION_CARD_ALT = 1503;
        public const int PAYMENT_OPTION_OTHERS = 1241;
        public const int PAYMENT_OPTION_DEBIT_CARD = 1244;
        public const int PAYMENT_OPTION_WALLET_ACCOUNT = 1492;
        public const int PAYMENT_OPTION_MY_ACCOUNTS = 1499;
        public const int PAYMENT_OPTION_OTHER_CARDS = 1500;
        public const int PAYMENT_OPTION_WITHIN_BH = 1501;
        public const int PAYMENT_OPTION_INTL = 1502;

        //PARENT CHILD SYSTEM
        public const int FAMILY_MEMBER_OPTION_FATHER = 1;
        public const int FAMILY_MEMBER_OPTION_MOTHER = 2;
        public const int RETENTION_DAILY = 1493;
        public const int RETENTION_WEEKLY = 1494;
        public const int RETENTION_MONTHLY = 693;

        public const int MALE = 423;
        public const int FEMALE = 424;

        public const int WPS_PRODUCT_CARD = 959;
        public const int WPS_PRODUCT_WALLET = 960;
        public const int WPS_PRODUCT_CARD_AND_WALLET = 961;
    }

    public static class CustomerTypes
    {
        public const int SUPPLEMENTARY = 1;
        public const int MERCHANT = 2;
        public const int MAIN = 3;
    }

    public static class ContactTitles
    {
        public const int CREDIMAX_CUSTOMER = 1;
        public const int CHILD = 2;
        public const int FAMILY_MEMBER = 3;
        public const int CHILD_HAS_MAIN_CARD = 4;
    }
    public static class PaymentGatewayTypes
    {
        public const int CREDIT_CARD = 1;
        public const int DEBIT_CARD = 2;
        public const int DEBIT_CARD_NEW = 4;
        public const int DEBIT_CARD_NEW_IPAY = 666;
    }

    public static class PaymentGatewayStatuses
    {
        public const int DECLINED = 0;
        public const int APPROVED = 1;
        public const int UNKNOWN = -1;
    }

    public static class CmsWebSite
    {
        public const int CompID = 2;
    }

    public static class BenefitsTypes
    {
        public const int Allowances = 1;
        public const int Bonus = 2;
    }

    public static class TaskRetention
    {
        public const int WEEKLY = 1;
        public const int MONTHLY = 2;
        public const int DAILY = 3;
    }


    public static class WalletRetention
    {
        public const int WEEKLY = 1;
        public const int MONTHLY = 2;
        public const int ONE_TIME = 3;
        public const int DAILY = 4;
    }

    public static class PaymentTypes
    {
        public const int DEBIT_CARD = 7;
        public const int CREDIT_CARD = 8;
    }
    public static class CcyCode
    {
        public const int BHD = 183;

    }
    public static class CustActId
    {
        public const int CHILD_CARD = 1928;

    }
    public static class CustActStatus
    {
        public const int OPEN = 1;
        public const int CLOSED = 2;
        public const int FROZEN = 3;
    }
    public static class CustomerAccountSubType
    {
        public const int SAVINGS = 2;
    }
    public static class CustAcctSetup
    {
        public const int DEBIT_CARD = 4;
        public const int CREDIT_CARD = 3;
    }

    public static class CacheKeys
    {
        public const string CARD_DETAILS = "CARD_DETAILS_{0}";
        public const string USER_CARD_MENU = "USER_CARD_MENU_{0}";
        public const string USER_MENU = "USER_MENU_{0}";
    }
}

