using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Extensions;

namespace Optimum.Wallet.Application.Interfaces.Repositories
{
    public interface ICardRepository 
    {
        public Task<IEnumerable<WalletAccountType>> GetCardAccountTypesAsync(char type = 'C');
        public Task<IEnumerable<WalletCurrency>> GetCardCurrenciesAsync(int ContactID);
        Task<IEnumerable<T>> GetCards<T>(int contactId, int customerId, int cardId = -1, bool isSupplementary = false, string mainCardNumber = "", bool includeExpired = false, bool updateBalances = false, string customerCpr = "") where T : new();
        Task<List<WalletAccount>> GetWalletAccounts(int contactId, int CAID = -1, int type = 1, int PaySubType = -1);
        Task<List<WalletAccount>> GetCurrencyWallets(int RefContactID, int MainCAID);
        // TODO: Use GetWalletAccounts instead !
        Task<bool> doesUserOwnCaid(AppUser appUser, int CAID);
        Task<T> GetCardById<T>(int contactId, int customerId, int cardId, bool isSupplementary = false, string mainCardNumber = "", bool includeExpired = false, bool updateBalances = false, string customerCpr = "") where T : new();
    }
}
