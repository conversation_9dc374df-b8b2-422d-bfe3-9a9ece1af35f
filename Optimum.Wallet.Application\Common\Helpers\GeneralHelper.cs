using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.StaticFiles;
using QRCoder;
using Serilog;
using StandardizedQR;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
// using Optimum.Wallet.Api.Data.Repository; // TODO: Refactor this repository usage to Application layer if needed.
// using Optimum.Wallet.Api.Extensions; // TODO: Move required extension methods to Application if needed.
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Models;
using static QRCoder.PayloadGenerator;
using Optimum.Wallet.Core.Extensions;

namespace Optimum.Wallet.Application.Common.Helpers
{
    /// <summary>
    /// 
    /// </summary>
    public static class GeneralHelper
    {
        public static string Encrypt(string source, string purpose)
        {
            return source.Encrypt(purpose);
        }

        public static string Decrypt(string source, string purpose)
        {
            return source.Decrypt(purpose);
        }

        public static DateTime ParseDate(string dateValue, CultureInfo culture = null)
        {
            if (culture == null)
            {
                culture = new CultureInfo("en-GB");
            }

            DateTime date;
            if (!DateTime.TryParseExact(dateValue, "yyyy-MM-dd", culture, DateTimeStyles.None, out date)
                && !DateTime.TryParseExact(dateValue, "dd/MM/yyyy", culture, DateTimeStyles.None, out date)
                && !DateTime.TryParseExact(dateValue, "d/MM/yyyy", culture, DateTimeStyles.None, out date))
            {
                date = DateTime.MinValue;
            }

            return date;
        }
        public static DateTime getNextGoalDueDate(DateTime StartDate, int dayNumFrequency, bool excludeToday = false)
        {
            if (StartDate.Date <= DateTime.Today.Date)
            {
                //Weekly
                if (dayNumFrequency == 7)
                {
                    var DayOfWeek = StartDate.DayOfWeek;
                    var NextWeekday = GetNextWeekday(DateTime.Today, DayOfWeek);

                    //Get next GoalDueDate and ignore today period => for sevice process
                    if (excludeToday && NextWeekday.Date == DateTime.Today.Date)
                    {
                        NextWeekday = NextWeekday.AddDays(7);
                    }

                    return NextWeekday;
                }
                else
                {//Monthly
                    var addMonth = StartDate.Day < DateTime.Today.Day ? 1 : 0;

                    //Get next GoalDueDate and ignore today period => for sevice process
                    if (excludeToday && StartDate.Date == DateTime.Today.Date)
                    {
                        addMonth = 1;
                    }

                    var dayInmonth = DateTime.DaysInMonth(DateTime.Today.Year, DateTime.Today.Month + addMonth);
                    var StartDateDay = StartDate.Day;
                    if (StartDate.Day > dayInmonth)
                    {
                        StartDateDay = 1;
                    }

                    DateTime newStart = new DateTime(DateTime.Today.Year, DateTime.Today.Month + addMonth, StartDateDay);
                    return newStart;
                }
            }
            else
            {
                return StartDate;
            }

        }
        public static DateTime GetNextWeekday(DateTime start, DayOfWeek day)
        {
            // The (... + 7) % 7 ensures we end up with a value in the range [0, 6]
            int daysToAdd = ((int)day - (int)start.DayOfWeek + 7) % 7;
            return start.AddDays(daysToAdd);
        }
        public static string GetRandomString(int length, bool onlyDigits = false)
        {
            var valid = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
            if (onlyDigits)
            {
                valid = "0123456789";
            }

            var res = new StringBuilder();
            using (var rng = RandomNumberGenerator.Create())
            {
                var uintBuffer = new byte[sizeof(uint)];

                while (length-- > 0)
                {
                    rng.GetBytes(uintBuffer);
                    uint num = BitConverter.ToUInt32(uintBuffer, 0);
                    res.Append(valid[(int)(num % (uint)valid.Length)]);
                }
            }

            return res.ToString();
        }

        ///TO DO Plug this to the smsplace, conver to a service
        public static async Task<bool> SendSMS(string ERTID, string processCode, string mobileNumber, string message, string senderName)
        {
#if DEBUG
            return true;
#endif
            return await DapperHelper.ExceuteAsync(
                " INSERT INTO dbo.SMSMessageTbl( ERTID, ProcessCode, From_MobileNo, To_MobileNo, SMSMessage, DateModified, UserModified, SenderName, BankCode, Unicode, Alert)" +
                " VALUES(@ERTID, @ProcessCode, '', @MobileNo, @Message, GETDATE(), N'amthal', @SenderName, N'', 0, 0)",
                new
                {
                    ERTID,
                    ProcessCode = processCode,
                    MobileNo = mobileNumber,
                    Message = message,
                    SenderName = senderName
                }) > 0;
        }

        public static string GetMimeType(string fileName)
        {
            string contentType;
            new FileExtensionContentTypeProvider().TryGetContentType(fileName, out contentType);

            return contentType;
        }

        public static string GenerateStaticQR(WalletAccount account,MerchantData items,AppUser CurrentUser)
        {
            var globalUniqueIdentifier = Guid.NewGuid().ToString().Replace("-", string.Empty);
            var merchantName = account.Name?.Length > 25 ? account.Name.Substring(0, 25) : account.Name;
            var merchantPayload = MerchantPayload.CreateStatic(
                merchantGlobalUniqueIdentifier: globalUniqueIdentifier,
                merchantCategoryCode: items.ISO18245,
                transactionCurrencyNumericCode: Iso4217Currency.Bahrain.Value.NumericCode,
                countryCode: Iso3166Countries.Bahrain,
                merchantName: merchantName,
                merchantCity: items.City)
                .WithAlternateLanguage(Iso639Languages.English, merchantName, items.City)
                .WithAdditionalData(
                mobileNumber: CurrentUser.Mobile,
                additionalConsumerDataRequest: CurrentUser.Username,
                storeLabel: merchantName,
                terminalLabel: account.CAID + "",
                purposeOfTransaction: "Payment");
            return merchantPayload.GeneratePayload().Encrypt("QR");
        }

        public static Bitmap GetBitmapQR(string payload,int ppm = 20)
        {
            QRCodeGenerator qrGenerator = new QRCodeGenerator();
            QRCodeData qrCodeData = qrGenerator.CreateQrCode(payload, QRCodeGenerator.ECCLevel.Q);
            PngByteQRCode qrCode = new PngByteQRCode(qrCodeData);
            byte[] qrCodeBytes = qrCode.GetGraphic(ppm);
            
            using (var ms = new System.IO.MemoryStream(qrCodeBytes))
            {
                return new Bitmap(ms);
            }
        }

        public static string GenerateQR(decimal amount, string merchantName, string merchantCity, int ISO18245, int CAID, AppUser CurrentUser)
        {
            var globalUniqueIdentifier = Guid.NewGuid().ToString().Replace("-", string.Empty);
            merchantName = merchantName?.Length > 25 ? merchantName.Substring(0, 25) : merchantName;
            var merchantPayload = MerchantPayload
                .CreateDynamic(globalUniqueIdentifier, ISO18245, Iso4217Currency.Bahrain.Value.NumericCode, Iso3166Countries.Bahrain, merchantName, merchantCity)
                .WithAlternateLanguage(Iso639Languages.English, merchantName, merchantCity)
                .WithTransactionAmount(amount)
                .WithAdditionalData(
                    mobileNumber: CurrentUser.Mobile,
                    additionalConsumerDataRequest: CurrentUser.Username,
                    storeLabel: merchantName,
                    terminalLabel: CAID + "",
                    purposeOfTransaction: "Payment");
            var payload = merchantPayload.GeneratePayload();
            return payload.Encrypt("QR");
        }

        public static string GetStatusMessage(int id, string type, bool isSuccess)
        {
            // Initialise the message
            var message = "";

            switch (id)
            {
                // Set the message for the PIN service
                case PageTypes.PIN_CHANGE_SERVICE:
                    message =
                        isSuccess
                            ? "PinChangeSuccess"
                            : "PinChangeFail";
                    break;
                case PageTypes.PIN_REPLACE_REQUEST:
                    message =
                        isSuccess
                            ? "PinSMSSuccess"
                            : "PinSMSFail";
                    break;
                case PageTypes.STOP_CARD_REQUEST:
                    message =
                        isSuccess
                            ? "StopCardSuccess"
                            : "StopCardFail";
                    break;
                // Set the default message
                default:
                    message =
                        isSuccess
                            //? "Your " + type + " details have been successfully sent!"
                            //: "Failed to send your " + type + " details! Please Try again later.";
                            ?"TransactionProcessedSuccess"
                            : "TransactionProcessedFail";
                    break;
            }

            // Return the results
            return message;
        }
    }
}
