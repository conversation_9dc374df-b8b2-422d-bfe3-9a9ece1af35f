﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Accept" xml:space="preserve">
    <value>Accept</value>
  </data>
  <data name="Agree" xml:space="preserve">
    <value>Agree</value>
  </data>
  <data name="AnErrorOccured" xml:space="preserve">
    <value>An error occured.</value>
  </data>
  <data name="AnotherDeviceLogged" xml:space="preserve">
    <value>Your account is currently logged onto another device.</value>
  </data>
  <data name="AppName" xml:space="preserve">
    <value>B2C Wolke Wallet</value>
  </data>
  <data name="AtLeast" xml:space="preserve">
    <value>at least</value>
  </data>
  <data name="AtMost" xml:space="preserve">
    <value>at most</value>
  </data>
  <data name="AvailableBalance" xml:space="preserve">
    <value>Available Balance</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="BackOnline" xml:space="preserve">
    <value>You are back online. Welcome!</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>Change</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Col_ChooseListCategoryTbl_CLCatNameE" xml:space="preserve">
    <value>CLCatNameE</value>
  </data>
  <data name="Col_ChooseListItemTbl_CLNameE" xml:space="preserve">
    <value>CLNameE</value>
  </data>
  <data name="Col_CurrencyFile_SwiftCode" xml:space="preserve">
    <value>SwiftCode</value>
  </data>
  <data name="Col_ObjectsTbl_EngName" xml:space="preserve">
    <value>EngName</value>
  </data>
  <data name="Col_ProductServiceTbl_ProductNameE" xml:space="preserve">
    <value>ProductNameE</value>
  </data>
  <data name="Col_Wallet_RelationsTbl_Relation" xml:space="preserve">
    <value>Relation</value>
  </data>
  <data name="Col_Wallet_RelationsTbl_Relation1" xml:space="preserve">
    <value>Relation</value>
  </data>
  <data name="Col_Wallet_RetentionTbl_RetentionNameE" xml:space="preserve">
    <value>RetentionNameE</value>
  </data>
  <data name="Col_Wallet_RetentionTbl_RetentionNameE1" xml:space="preserve">
    <value>RetentionNameE</value>
  </data>
  <data name="Col_Wallet_TransactionTypeTbl_TypeNameE" xml:space="preserve">
    <value>TypeNameE</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="ErrorDataSubmitted" xml:space="preserve">
    <value>There is an error with the data submitted! Please Try again later.</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>Error occurred while processing your request.</value>
  </data>
  <data name="Exactly" xml:space="preserve">
    <value>exactly</value>
  </data>
  <data name="IncorrectFingerprintHash" xml:space="preserve">
    <value>Incorrect fingerprint hash.</value>
  </data>
  <data name="IncorrectLoginDetails" xml:space="preserve">
    <value>Incorrect login details.</value>
  </data>
  <data name="IncorrectLoginFormDetails" xml:space="preserve">
    <value>Invalid login form details.</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>info</value>
  </data>
  <data name="InternalError" xml:space="preserve">
    <value>An internal error has occurred. Please try again later.</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="LoginLocked" xml:space="preserve">
    <value>Login locked, please contact support.</value>
  </data>
  <data name="More" xml:space="preserve">
    <value>More</value>
  </data>
  <data name="MsgNotEligible" xml:space="preserve">
    <value>None of your cards are eligible for this service.</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="NoDataFound" xml:space="preserve">
    <value>No Data Found!</value>
  </data>
  <data name="NoInternetConnection" xml:space="preserve">
    <value>No internet connection detected</value>
  </data>
  <data name="OffersDesc" xml:space="preserve">
    <value>Should you have further inquiries, please do contact us. We are at your service.</value>
  </data>
  <data name="OffersWorkinghours" xml:space="preserve">
    <value />
  </data>
  <data name="Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="ParametersError" xml:space="preserve">
    <value>Please provide all required parameters.</value>
  </data>
  <data name="ProceedButton" xml:space="preserve">
    <value>Proceed</value>
  </data>
  <data name="Reject" xml:space="preserve">
    <value>Reject</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>required</value>
  </data>
  <data name="SaveChanges" xml:space="preserve">
    <value>Save Changes</value>
  </data>
  <data name="Saving" xml:space="preserve">
    <value>Saving...</value>
  </data>
  <data name="SelectCaption" xml:space="preserve">
    <value>Please choose an option</value>
  </data>
  <data name="ServicesTitle" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="SignOutConfirm" xml:space="preserve">
    <value>Are You sure you want to sign out?</value>
  </data>
  <data name="SubmitButton" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="SubmitButtonLoading" xml:space="preserve">
    <value>Submitting</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>success</value>
  </data>
  <data name="TitleNotEligible" xml:space="preserve">
    <value>Not Eligible</value>
  </data>
  <data name="UnexpectedError" xml:space="preserve">
    <value>An unexpected error has occurred. Please try again later</value>
  </data>
  <data name="UploaderFieldLimitDesc" xml:space="preserve">
    <value>The file size must be less than 2 MB.</value>
  </data>
  <data name="ValidationRuleCPRLong" xml:space="preserve">
    <value>The CPR field must be exactly 9 characters long.</value>
  </data>
  <data name="ValidationRuleDecimalPlaces" xml:space="preserve">
    <value>The {0} field must include three decimal places.</value>
    <comment>{0}</comment>
  </data>
  <data name="ValidationRuleEmail" xml:space="preserve">
    <value>The {0} field must be a valid email address.</value>
    <comment>{0}</comment>
  </data>
  <data name="ValidationRuleExactlyLength" xml:space="preserve">
    <value>The {0} must be exactly {2} digits long.</value>
    <comment>fieldTitle {0} | length {2}</comment>
  </data>
  <data name="ValidationRuleIBAN" xml:space="preserve">
    <value>The {0} field must be a valid IBAN number.</value>
  </data>
  <data name="ValidationRuleLength" xml:space="preserve">
    <value>The {0} field must be {1} {2} characters long.</value>
    <comment>fieldTitle {0} | term {1} | length {2}</comment>
  </data>
  <data name="ValidationRuleNumber" xml:space="preserve">
    <value>The {0} field must consist of numbers only.</value>
    <comment>{0}</comment>
  </data>
  <data name="ValidationRuleOnlyText" xml:space="preserve">
    <value>The field accept only text</value>
  </data>
  <data name="ValidationRulePhoneNumber" xml:space="preserve">
    <value>The {0} field must be a valid phone number.</value>
    <comment>{0}</comment>
  </data>
  <data name="ValidationRuleRange" xml:space="preserve">
    <value>The {0} field must be between {1} and {2}.</value>
    <comment>{0} {1}</comment>
  </data>
  <data name="ValidationRuleRequired" xml:space="preserve">
    <value>The {0} field is required.</value>
    <comment>{0}</comment>
  </data>
  <data name="ValidationRuleSpecialCharacter" xml:space="preserve">
    <value>The special character is not allowed.</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>warning</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
</root>