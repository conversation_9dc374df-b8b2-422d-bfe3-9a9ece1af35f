﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="allAreas" xml:space="preserve">
    <value>كل المناطق</value>
  </data>
  <data name="allCategories" xml:space="preserve">
    <value>كل الفئات</value>
  </data>
  <data name="allTypes" xml:space="preserve">
    <value>كل الأنواع</value>
  </data>
  <data name="AqaraatsMarketPlace" xml:space="preserve">
    <value>متجر عقارات</value>
  </data>
  <data name="Bakery" xml:space="preserve">
    <value>المخبز</value>
  </data>
  <data name="Cafeteria" xml:space="preserve">
    <value>الكافيتريا</value>
  </data>
  <data name="CarWash" xml:space="preserve">
    <value>غسيل السيارة</value>
  </data>
  <data name="CoffeeShop" xml:space="preserve">
    <value>المقهى</value>
  </data>
  <data name="ComingSoon" xml:space="preserve">
    <value>قريباً</value>
  </data>
  <data name="Delivering" xml:space="preserve">
    <value>التوصيل</value>
  </data>
  <data name="electricityInc" xml:space="preserve">
    <value>خدمات الكهرباء</value>
  </data>
  <data name="findproperty" xml:space="preserve">
    <value>البحث عن عقار</value>
  </data>
  <data name="findpropertydesc" xml:space="preserve">
    <value>العثور على خصائص مع الميزات المفضلة لديك</value>
  </data>
  <data name="FindYourProperty" xml:space="preserve">
    <value>ابحث عن عقارك</value>
  </data>
  <data name="Food" xml:space="preserve">
    <value>الطعام</value>
  </data>
  <data name="furnicherOp" xml:space="preserve">
    <value>خيارات التأثيث</value>
  </data>
  <data name="Gas" xml:space="preserve">
    <value>غاز</value>
  </data>
  <data name="Groceries" xml:space="preserve">
    <value>البقالة</value>
  </data>
  <data name="HouseKeeping" xml:space="preserve">
    <value>التدبير المنزلي</value>
  </data>
  <data name="includeUnavailable" xml:space="preserve">
    <value>إظهار العقارات الغير متوفرة</value>
  </data>
  <data name="Investment" xml:space="preserve">
    <value>إستثمار</value>
  </data>
  <data name="keyword" xml:space="preserve">
    <value>الكلمات الدليلية</value>
  </data>
  <data name="KhidmaatMarketPlace" xml:space="preserve">
    <value>متجر خدمات</value>
  </data>
  <data name="Laundry" xml:space="preserve">
    <value>غسيل الملابس</value>
  </data>
  <data name="Maintenance" xml:space="preserve">
    <value>صيانة</value>
  </data>
  <data name="MallatsMarketPlace" xml:space="preserve">
    <value>متجر مولات</value>
  </data>
  <data name="Malls" xml:space="preserve">
    <value>مولات</value>
  </data>
  <data name="MarketPlace" xml:space="preserve">
    <value>متجر</value>
  </data>
  <data name="maxArea" xml:space="preserve">
    <value>المساحة القصوى (م2)</value>
  </data>
  <data name="maxBathroom" xml:space="preserve">
    <value>الحد الأقصى لدورات المياة</value>
  </data>
  <data name="maxBedroom" xml:space="preserve">
    <value>الحد الأقصى لغرف النوم</value>
  </data>
  <data name="minArea" xml:space="preserve">
    <value>المساحة الدنيا (م2)</value>
  </data>
  <data name="minBathroom" xml:space="preserve">
    <value>الحد الأدنى لدورات المياة</value>
  </data>
  <data name="minBedroom" xml:space="preserve">
    <value>الحد الأدنى لغرف النوم</value>
  </data>
  <data name="MtaemMarketPlace" xml:space="preserve">
    <value>متجر مطاعم</value>
  </data>
  <data name="OurAgencies" xml:space="preserve">
    <value>وكالاتنا</value>
  </data>
  <data name="PendingOrders" xml:space="preserve">
    <value>الطلبات المعلقة</value>
  </data>
  <data name="Pharmacy" xml:space="preserve">
    <value>الصيدلية</value>
  </data>
  <data name="RealEstate" xml:space="preserve">
    <value>العقارات</value>
  </data>
  <data name="RealEstate1" xml:space="preserve">
    <value>العقارات</value>
  </data>
  <data name="Rent" xml:space="preserve">
    <value>تأجير</value>
  </data>
  <data name="reset" xml:space="preserve">
    <value>إعادة تعيين</value>
  </data>
  <data name="Restaurants" xml:space="preserve">
    <value>المطاعم</value>
  </data>
  <data name="Sale" xml:space="preserve">
    <value>بيع</value>
  </data>
  <data name="search" xml:space="preserve">
    <value>بـحـث</value>
  </data>
  <data name="selectMaxPrice" xml:space="preserve">
    <value>إختر السعر الأقصى</value>
  </data>
  <data name="selectMinPrice" xml:space="preserve">
    <value>إختر السعر الأدنى</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>الخدمات</value>
  </data>
  <data name="Shopping" xml:space="preserve">
    <value>تسوق</value>
  </data>
  <data name="Shopping1" xml:space="preserve">
    <value>تسوق</value>
  </data>
  <data name="showLess" xml:space="preserve">
    <value>إخفاء خيارات البحث</value>
  </data>
  <data name="showMore" xml:space="preserve">
    <value>إظهار المزيد من خيارات البحث</value>
  </data>
  <data name="TopHighlight" xml:space="preserve">
    <value>العقارات البارزة</value>
  </data>
  <data name="Trading" xml:space="preserve">
    <value>تجارة</value>
  </data>
  <data name="ViewAll" xml:space="preserve">
    <value>عرض الكل</value>
  </data>
  <data name="Water" xml:space="preserve">
    <value>الماء</value>
  </data>
</root>