using Dapper;
using Microsoft.Data.SqlClient;
using Serilog;
using System.Data;

namespace Optimum.Wallet.Application.Common.Helpers
{
    public static class DapperHelper
    {
        public static string DbName = Config.GetStringValue("DBName");
        public static string LedgerDbName = Config.GetStringValue("DBName") + "Ledger.dbo.";
        public static string UtilitiesDbName = Config.GetStringValue("DBName") + "Utilities.dbo.";
        public static string ServiceDbName = Config.GetStringValue("DBName") + "Service.dbo.";
        public static string PayableDbName = Config.GetStringValue("DBName") + "Payable.dbo.";

        public static IDbConnection GetDbConnection(string conn = "Utilities")
        {
            return new SqlConnection(Config.GetConnectionString(conn));
        }

        public async static Task<IEnumerable<T>> QueryAsync<T>(string sql, object param = null, string conn = "Utilities", CommandType? commandType = null)
        {
            IEnumerable<T> result = null;

            try
            {
                using (var db = GetDbConnection(conn))
                {
                    result = await db.QueryAsync<T>(sql, param, commandType: commandType);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex.ToString());
            }


            return result;
        }

        public async static Task<IEnumerable<TReturn>> QueryMapAsync<TFirst, TSecond, TReturn>(string sql, Func<TFirst, TSecond, TReturn> map, string splitOn, object param = null, string conn = "Utilities")
        {
            using (var db = GetDbConnection(conn))
            {
                var result = await db.QueryAsync(sql, map, param, splitOn: splitOn);

                return result;
            }
        }

        public static IEnumerable<T> Query<T>(string sql, object param = null, string conn = "Utilities", CommandType? commandType = null)
        {
            using (var db = GetDbConnection(conn))
            {
                return db.Query<T>(sql, param, commandType: commandType);
            }
        }

        public static IEnumerable<TReturn> QueryMap<TFirst, TSecond, TReturn>(string sql, Func<TFirst, TSecond, TReturn> map, string splitOn, object param = null, string conn = "Utilities")
        {
            using (var db = GetDbConnection(conn))
            {
                return db.Query(sql, map, param, splitOn: splitOn);
            }
        }

        public async static Task<int> ExceuteAsync(string sql, object param = null, string conn = "Utilities")
        {
            using (var db = GetDbConnection(conn))
            {
                var result = await db.ExecuteAsync(sql, param);

                return result;
            }
        }

        public async static Task<T> ExecuteScalarAsync<T>(string sql, object param = null, string conn = "Utilities")
        {
            using (var db = GetDbConnection(conn))
            {
                var result = await db.ExecuteScalarAsync<T>(sql, param);

                return result;
            }
        }

        public static int Exceute(string sql, object param = null, string conn = "Utilities")
        {
            using (var db = GetDbConnection(conn))
            {
                return db.Execute(sql, param);
            }
        }

        public static T ExecuteScalar<T>(string sql, object param = null, string conn = "Utilities")
        {
            using (var db = GetDbConnection(conn))
            {
                return db.ExecuteScalar<T>(sql, param);
            }
        }
    }
}
