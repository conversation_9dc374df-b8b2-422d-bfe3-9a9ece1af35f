using Microsoft.Extensions.Localization;

namespace Optimum.Wallet.Application.Common.Models
{
    public class MenuViewModel
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Url { get; set; }
        public int ParentId { get; set; }
        public string Icon { get; set; }
        public int ProductId { get; set; }
        public bool isComingSoon = false;
        public string PID { get; set; }
        public List<MenuViewModel> subMenu { get; set; }
    }

    public class UserMenus
    {
        public List<MenuViewModel> AllMenus { get; set; }
        public List<MenuViewModel> MainMenu { get; set; }
        public List<MenuViewModel> UserMenu { get; set; }
        public List<MenuViewModel> CardMenu { get; set; }
        public List<MenuViewModel> WalletMenu { get; set; }
        public List<MenuViewModel> MerchantMenu { get; set; }
    }

    public class CultureStrings
    {
        public IEnumerable<LocalizedString> accountLocalizer { get; set; }
        public IEnumerable<LocalizedString> servicesLocalizer { get; set; }
        public IEnumerable<LocalizedString> globalLocalizer { get; set; }
    }
}
