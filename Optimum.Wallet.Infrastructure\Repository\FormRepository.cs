using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Optimum.Wallet.Application.Common;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Extensions;
using Optimum.Wallet.Application.Interfaces;
using Optimum.Wallet.Application.Interfaces.Repositories;
using Optimum.Wallet.Domain.Entities;
using Serilog;
using System.Data;
using System.Diagnostics;
using System.Dynamic;
using System.Security.Claims;
using System.Text.RegularExpressions;

namespace Optimum.Wallet.Infrastructure.Repository
{
    public class FormRepository : IFormRepository
    {
        private readonly ILogger<FormRepository> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ICardRepository _cardRepository;
        private readonly IWalletRepository _walletRepository;
        private readonly IStringLocalizer<FormRepository> _loc;

        public FormRepository(ILogger<FormRepository> logger,IHttpContextAccessor httpContextAccessor, ICardRepository cardRepository, IWalletRepository walletRepository
            , IStringLocalizer<FormRepository> stringLocalizer)
        {
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _cardRepository = cardRepository;
            _walletRepository = walletRepository;
            _loc = stringLocalizer;
        }

        public async Task<IEnumerable<RequestFormField>> GetFormById(int formId, int contactId, int customerId, int tableId = -1, bool viewOnly = false, Dictionary<int, string> formValues = null, bool isLogRequest = false, bool checkExternalPublish = false, bool checkExternalEditable = false)
        {
            List<RequestDropDown> fieldValues = null;
            formValues = !viewOnly && formValues != null ? formValues : new Dictionary<int, string>();
            List<RequestFormFieldValidationRule> fieldsValidationRules = null;
            List<RequestFormFieldTab> fieldsTabsDetails = null;
            var fields = new List<RequestFormField>();
            try
            {
                if (!viewOnly)
                {
                    var fieldValuesQuery =
                        await DapperHelper.QueryAsync<RequestDropDown>(
                            $" SELECT cli.CLIId AS ID, {LanguageHelper.GetSelectStmt("cli.CLNameE", "Title", 1)}, clci.CLCatId AS ControlId" +
                            " FROM dbo.ChooseListItemTbl cli" +
                            " INNER JOIN dbo.ChooseListCategoryItemTbl clci ON cli.CLIId = clci.CLIId" +
                             LanguageHelper.GetLeftJoin("ChooseListItemTbl", "CLNameE", "cli.CLIId", 1) +
                            " WHERE clci.CatMapID = @CatMapID AND cli.Active = 1",
                            new
                            {
                                CatMapID = formId
                            }
                        );

                    fieldValues = fieldValuesQuery.ToList();

                    var fieldsValidationRulesQuery =
                         await DapperHelper.QueryAsync<RequestFormFieldValidationRule>(
                            " SELECT vr.RuleID AS Id, vr.RuleName AS Title, vrv.RuleValue AS Value, vrv.CLPId AS FieldId, vr.RuleName + '[' + vrv.RuleValue + ']' AS [Rule]" +
                            " FROM dbo.ValidationRuleValues vrv" +
                            " INNER JOIN dbo.ValidationRules vr ON vrv.RuleID = vr.RuleID" +
                            " INNER JOIN dbo.ChooseListPageTbl clp ON vrv.CLPId = clp.CLPId" +
                            " WHERE clp.iType = @iType AND vrv.Active = 1 AND vr.Active = 1",
                            new
                            {
                                iType = formId
                            }
                        );

                    fieldsValidationRules = fieldsValidationRulesQuery.ToList();

                    //Retrieve tabs details
                    //TODO:Can be make it dynamically if need
                    if (formId == PageTypes.CUSTOMER_KYC || formId == PageTypes.RETAIL_CUSTOMER_KYC)
                    {
                        var HeaderId = 1;
                        var fieldsTabsDetailsQuery = await DapperHelper.QueryAsync<RequestFormFieldTab>(
                                " SELECT Tab.CLPId FieldId,Tab.Tab TabNo,Tab.Sort ControlSort" +
                                " FROM dbo.ChooseListTabTbl Tab" +
                                " INNER JOIN dbo.ChooseListPageTbl clp ON Tab.CLPId = clp.CLPId" +
                                " WHERE clp.iType = @iType AND Tab.HeaderId=@HeaderId",
                                new
                                {
                                    iType = formId,
                                    HeaderId = HeaderId
                                }
                            );

                        fieldsTabsDetails = fieldsTabsDetailsQuery.ToList();
                    }
                }

                var tableName = isLogRequest ? "ChooseListDetailsLogTbl" : "ChooseListDetailsTbl";
                var fieldsQuery =
                    await DapperHelper.QueryAsync<RequestFormField>(
                        $" SELECT clp.CLCatId AS Id,{LanguageHelper.GetSelectStmt("clc.CLCatNameE", "Title", 0)}, clc.ControlType AS ControlType, clc.DataType AS DataType," +
                        $" CASE WHEN clc.ControlType = 'advpick' THEN ({LanguageHelper.GetSelectStmt("cli.CLNameE", "", 1)}) ELSE cld.CLIId END AS VALUE, " +
                        $" clp.{(checkExternalEditable ? "External" : "")}Editable AS Editable, clp.{(checkExternalPublish ? "External" : "")}Publish AS Publish, clp.PreSetValue AS PresetValue, clp.CLPId AS CLPId, cld.CLIId" +
                        " FROM dbo.ChooseListPageTbl clp" +
                        " INNER JOIN dbo.ChooseListCategoryTbl clc ON clp.CLCatId = clc.CLCatId" +
                       $" LEFT JOIN dbo.{tableName} cld ON cld.pageId = 9049 AND cld.TableId = @TableId AND cld.CLCatId = clc.CLCatId" +
                        " LEFT JOIN dbo.ChooseListItemTbl cli ON clc.ControlType = 'advpick' AND CONVERT(NVARCHAR, cli.CLIId) = cld.CLIId" +
                         LanguageHelper.GetLeftJoin("ChooseListCategoryTbl", "CLCatNameE", "clc.CLCatId", 0) +
                         LanguageHelper.GetLeftJoin("ChooseListItemTbl", "CLNameE", "cli.CLIId", 1) +
                        " WHERE clp.iType = @iType" +
                        " AND clp.active=1 " +
                        " ORDER BY cld.TableId, clp.SortCode, cld.SortCode",
                        new
                        {
                            TableId = tableId,
                            iType = formId
                        }
                    );

                var fieldsQueryTask = fieldsQuery.AsEnumerable().Select(async r =>
                    {
                        // Get the field's id
                        var id = r.ID;

                        // Get the field's CLPId
                        var CLPId = r.CLPId;

                        // Get the field's value
                        var value = formValues.ContainsKey(id) ? formValues[id] : r.Value;

                        // Get the field's dropdown values
                        var dropdownValues = new List<RequestDropDown>();
                        if (!viewOnly && fieldValues != null)
                        {
                            dropdownValues = fieldValues.Where(v => v.ControlId == id).ToList();
                        }

                        // Get the field's tabs
                        var tabsDetails = new List<RequestFormFieldTab>();
                        if (!viewOnly && fieldsTabsDetails != null)
                        {
                            tabsDetails = fieldsTabsDetails.Where(v => v.FieldId == CLPId).ToList();
                        }

                        // Get the field's card values
                        var cardValues = new List<CardDetailsViewModel>();
                        if (!viewOnly && _cardRepository != null)
                        {
                            // Check if we have to include expired cards
                            var includeExpiredCards = new List<int> {
                                PageTypes.UPGRADE_REQUEST,
                                PageTypes.DISPUTE_FORM,
                                PageTypes.REPLACE_REQUEST
                            }.Contains(formId);

                            var customerCpr = _httpContextAccessor.HttpContext.User?.Claims.Where(a => a.Type == ClaimTypes.NameIdentifier).FirstOrDefault().Value;
                            if (id == ControlTypes.CARD_NUMBER || id == ControlTypes.MAIN_CARD_NUMBER || r.ControlType == ControlDataTypes.Card)
                            {
                                var cardsListTask = await _cardRepository.GetCards<CardDetailsViewModel>(contactId, customerId, -1, false, "", includeExpiredCards, false, customerCpr);
                                var cardsList = cardsListTask.ToList();

                                cardValues.AddRange(cardsList);
                            }

                            if (id == ControlTypes.CARD_NUMBER || id == ControlTypes.SUPP_CARD_NUMBER || r.ControlType == ControlDataTypes.Card)
                            {
                                var cardsTask = await _cardRepository.GetCards<SuppCardDetailsViewModel>(contactId, customerId, -1, true, "", includeExpiredCards, false, customerCpr);
                                var cards = cardsTask.ToList();
                                
                                cardValues.AddRange(cards.Select(c => new CardDetailsViewModel
                                {
                                    CardId = c.CardId,
                                    ParentCardId = c.ParentCardId,
                                    CardHolderName = c.CardHolderName,
                                    CardHolderCpr = c.CardHolderCpr,
                                    CardNumber = c.SuppCardNumber,
                                    CardType = c.CardType,
                                    CardClassId = c.CardClassId,
                                    CardExpiry = c.CardExpiry,
                                    CardClass = "",
                                    Balance = new CardBalance
                                    {
                                        CreditLimit = c.Balance.CreditLimit,
                                        CreditLimitUsed = c.Balance.CreditLimitUsed,
                                        CashLimit = c.Balance.CashLimit,
                                        CashLimitUsed = c.Balance.CashLimitUsed,
                                        ClosingBalance = c.Balance.ClosingBalance,
                                        TodayBalance = c.Balance.TodayBalance,
                                        HoldAmount = c.Balance.HoldAmount,
                                        AvailableBalance = c.Balance.AvailableBalance,
                                        MinimumBalance = c.Balance.MinimumBalance,
                                        DueDate = c.Balance.DueDate,
                                        ParentCreditLimit = c.Balance.ParentCreditLimit
                                    },
                                    SupplementaryCards = new List<SuppCardDetailsViewModel>()
                                }).ToList());
                                cards = null; // PCI Requirement
                            }
                        }

                        var accountValues = new List<WalletAccount>();
                        if (!viewOnly && r.ControlType == ControlDataTypes.Wallet)
                        {
                            var walletAccounts = await _cardRepository.GetWalletAccounts(contactId, PaySubType: 1);

                            accountValues.AddRange(walletAccounts);
                        }

                        // Fetch any validation rules for this field
                        var rules = "";
                        if (!viewOnly && fieldsValidationRules != null)
                        {
                            rules =
                                fieldsValidationRules
                                .AsEnumerable()
                                .Where(v => v.FieldId == r.CLPId)
                                .GroupBy(v => v.FieldId)
                                .Select(g => string.Join("|", g.Select(gv => gv.Rule)))
                                .FirstOrDefault();
                        }

                        // Construct the field object
                        var field = new RequestFormField
                        {
                            ID = id,
                            Title = r.Title,
                            ControlType = r.ControlType,
                            DataType = r.DataType,
                            Value = value,
                            CLIId = r.CLIId,
                            Editable = !viewOnly && r.Editable,
                            Publish = r.Publish,
                            ValidationRules = rules,
                            PresetValue = r.PresetValue,
                            DropdownValues = dropdownValues,
                            Cards = cardValues,
                            Tabs = tabsDetails,
                            Accounts = accountValues
                        };

                        cardValues = null; // PCI Requirement

                        return field;
                    });

                var fieldsQueryTaskList = fieldsQueryTask.ToList();

                Task<RequestFormField[]> jointFieldsTask = Task.WhenAll(fieldsQueryTaskList);
                IEnumerable<RequestFormField> fieldsList = await jointFieldsTask;

                fields = fieldsList.ToList();

                // Invoke the garbage collector to get rid of the nullified data (PCI Requirement)
                GC.Collect();
            }
            catch (Exception ex)
            {

            }

            return fields.All(f => f == null) ? null : fields;
        }

        public async Task<Tuple<bool, int>> InsertRequest(int formId, AppUser currentUser, IFormCollection fields, bool isLogRequest = false)
        {
            var formTableId = -1;
            return await InsertRequest(formId, currentUser, fields, formTableId, true, true, isLogRequest);
        }

        public async Task<Tuple<bool, int>> InsertDictionaryRequest(int formId, AppUser currentUser, Dictionary<string, string> fields, bool processEmail = true, bool processWebService = true, bool isLogRequest = false)
        {
            var formTableId = -1;
            return await InsertDictionaryRequest(formId, currentUser.CustomerID, currentUser.ContactID, currentUser.Username, fields, formTableId, currentUser.CAID,currentUser.CRID,isLogRequest);
        }

        public async Task<Tuple<bool, int>> InsertDictionaryRequest(int formId, int customerId, int contactId, string username, Dictionary<string, string> fields, int formTableId, int caid, int crid, bool isLogRequest = false, bool processEmail = true, bool processWebService = true)
        {
            return await InsertDictionaryRequest(formId, customerId, contactId, username, fields, formTableId, null, processEmail, processWebService, isLogRequest, caid, crid);
        }

        public async Task<Tuple<bool, int>> InsertRequest(int formId, AppUser currentUser, IFormCollection fields, int formTableId, bool processEmail = true, bool processWebService = true, bool isLogRequest = false)
        {
            return await InsertRequest(formId, currentUser.CustomerID, currentUser.ContactID, currentUser.Username, fields, formTableId, currentUser, processEmail, processWebService, isLogRequest, currentUser.CAID, currentUser.CRID);
        }

        public async Task<Tuple<bool, int>> InsertRequest(int formId, int customerId, int contactId, string username, IFormCollection fields, int formTableId, int caid, int crid)
        {
            return await InsertRequest(formId, customerId, contactId, username, fields, formTableId, null, true, true, false, caid, crid);
        }

        public async Task<Tuple<bool, int>> InsertRequest(int formId, int customerId, int contactId, string username, IFormCollection fields, int formTableId, int caid, int crid, AppUser currentUser, WalletExchangeQuote quote, bool checkExternalPublish = false)
        {
            return await InsertRequest(formId, customerId, contactId, username, fields, formTableId, currentUser, true, true, false, caid, crid, quote, checkExternalPublish);
        }

        public async Task<Tuple<bool, int>> InsertRequest(int formId, int customerId, int contactId, string username, IFormCollection fields, int formTableId, AppUser currentUser = null, bool processEmail = true, bool processWebService = true, bool isLogRequest = false, int caid = -1, int crid = -1, WalletExchangeQuote quote = null, bool checkExternalPublish = false)
        {
            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}] Entered");

            // Keep track of the uploaded files paths.
            // This will be used to delete the files if the transaction wasn't commited.
            var filesList = new List<string>();

            try
            {
                var headerTblName = isLogRequest ? "CustomerServicesLogTbl" : "CustomerServicesTbl";
                var detailsTblName = isLogRequest ? "ChooseListDetailsLogTbl" : "ChooseListDetailsTbl";
                Dictionary<int, string> formValues;

                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}] request, creating header {headerTblName}... ");
                var formHeader = await DapperHelper.ExecuteScalarAsync<int>(
                   $" INSERT INTO dbo.{headerTblName} (ProductID, UserModified, DateModified, Active, RecID, Posted, TypeID, CAID, CRID,ContactId)" +
                    " VALUES (@ProductID, @UserModified, @DateModified, @Active, @RecID, @Posted, @TypeID, @CAID, @CRID,@ContactId);" +
                    " SELECT CONVERT(INT, SCOPE_IDENTITY());",
                    new
                    {
                        ProductID = formId,
                        UserModified = username,
                        DateModified = DateTime.Now,
                        Active = true,
                        RecID = customerId,
                        Posted = 1,
                        CAID = caid == -1 ? currentUser?.CAID : caid,
                        CRID = currentUser?.CRID ?? crid,
                        TypeID = 0,
                        ContactId = currentUser?.ContactID ?? contactId
                    }
                );
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}] request, adding header to database {headerTblName}");

                // Set the form table id
                formTableId = formHeader;
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] determined header id");

                // Keep track of the values so that it can be used to repopulate the form
                formValues = new Dictionary<int, string>();


                int fieldID;
                string fieldValue;
                var detailsSql = "";
                var detailsParameters = new ExpandoObject() as IDictionary<string, object>;
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] checking submitted fields: {JsonConvert.SerializeObject(fields)}");
                foreach (var field in fields)
                {
                    string fieldKey = field.Key;
                    // Skip over any field that doesn't follow the naming convention
                    var key = fieldKey.Replace(General.InputPrefix, "");
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] field: {key}");
                    if (!int.TryParse(key, out fieldID))
                    {
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] field key is invalid, skipping...");
                        continue;
                    }

                    // Get the current field's value and add it to the dictionary
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] getting field's value");
                    fieldValue = GetFieldValue(fieldID, fields[fieldKey], true, currentUser);
                    formValues.Add(fieldID, fieldValue);
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] field - {fieldKey}: {fieldValue}");

                    // Build the field's data
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Normal request, adding field details...");
                    detailsSql =
                        $" INSERT INTO dbo.{detailsTblName} (pageId, TableId, CLCatId, CLIId)" +
                         " VALUES (@pageId, @TableId, @CLCatId, @CLIId);" +
                         " SELECT CONVERT(INT, SCOPE_IDENTITY());";

                    detailsParameters = new ExpandoObject() as IDictionary<string, object>;
                    detailsParameters.Add("@pageId", 9049);
                    detailsParameters.Add("@TableId", formHeader);
                    detailsParameters.Add("@CLCatId", fieldID);
                    detailsParameters.Add("@CLIId", fieldValue);
                    var CLDtlId = await DapperHelper.ExecuteScalarAsync<int>(detailsSql, detailsParameters);
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Normal request, added field details");

                    // If we have a non-empty signature
                    if (new List<int> { ControlTypes.SIGNATURE, 213, 214, ControlTypes.CUST_KYC_CPR_PROOF__UPLOAD, /*Passport*/337, ControlTypes.CUST_KYC_FACE_PROOF_UPLOAD, ControlTypes.CUST_KYC_ADDRESS_PROOF_UPLOAD }.Contains(fieldID) && !string.IsNullOrWhiteSpace(fieldValue))
                    {
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] Signature field found");

                        // Extract the base64 data
                        var base64Groups = Regex.Match(fieldValue, @"data:image/(?<type>.+?),(?<data>.+)");
                        var base64Type = base64Groups?.Groups["type"]?.Value;
                        var base64Data = base64Groups?.Groups["data"]?.Value;

                        //TODO:Correct this code I use it temp for session
                        if (new List<int> { 213, 214, ControlTypes.CUST_KYC_CPR_PROOF__UPLOAD, /*Passport*/337, ControlTypes.CUST_KYC_FACE_PROOF_UPLOAD, ControlTypes.CUST_KYC_ADDRESS_PROOF_UPLOAD }.Contains(fieldID)) { base64Type = base64Type.Replace(";base64", ""); }

                        // Error: Couldn't extract the base64 data
                        if (string.IsNullOrWhiteSpace(base64Data))
                        {
                            return new Tuple<bool, int>(false, formTableId);
                        }

                        // Get the file extension
                        string extension = $".{base64Type}";

                        // Set the file name
                        var fileName = $"Doc{CLDtlId}{extension}";
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] Filename: {fileName}");

                        // Save the changes in order to populate the primary key column
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] saving to database...");
                        await DapperHelper.ExecuteScalarAsync<int>(
                           $" UPDATE dbo.{detailsTblName} SET CLIId = '{fileName}' WHERE CLDtlId = {CLDtlId};",
                            detailsParameters
                        );
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] saved to database");

                        // Convert the data to bytes in preperation for creating the image file
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] converting to bytes...");
                        var bytesData = Convert.FromBase64String(base64Data);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] converted to bytes");

                        // Get the forms folder in which the files will be uploaded
                        var appFolder = WalletApplication.AppFolder;
                        var formsFolder = Config.GetStringValue("FormsFolder");
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] Application Folder: {appFolder}");
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] Forms Folder: {formsFolder}");

                        // Save the file to disk
                        var path = Path.Combine(appFolder, formsFolder, fileName);
                        filesList.Add(path);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] Filepath: {path}");
                        using (var imageFile = new FileStream(path, FileMode.Create))
                        {
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] saving image to disk...");
                            imageFile.Write(bytesData, 0, bytesData.Length);
                            imageFile.Flush();
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] image saved");
                        }
                    }
                }

                if (formValues.Count > 0)
                {
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Setting the temp data");
                    //_controller.TempData["FormId"] = formId;
                    // _controller.TempData["FormValues"] = formValues;
                    //_controller.TempData["IsLogRequest"] = isLogRequest + "";
                }

                if (_httpContextAccessor != null && _httpContextAccessor.HttpContext.Request.Form.Files.Count > 0)
                {
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files] Attachment files found");

                    // Get the forms folder in which the files will be uploaded
                    var appFolder = WalletApplication.AppFolder;
                    var formsFolder = Config.GetStringValue("FormsFolder");
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files] Application Folder: {appFolder}");
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files] Forms Folder: {formsFolder}");

                    // Get the uploaded files
                    var files = _httpContextAccessor.HttpContext.Request.Form.Files;
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files] Count: {files.Count}");

                    // Process the files                        
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files] Processing files...");
                    foreach (var file in files)
                    {
                        // Skip over any field that doesn't follow the naming convention
                        var id = file.Name.Replace(General.InputPrefix, "");
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] Checking format...");

                        if (!int.TryParse(id, out fieldID) || file == null || file.Length <= 0)
                        {
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] Invalid file (id/not exist), skipping over...");
                            continue;
                        }

                        // Make sure that the file is valid
                        if (!FileUploadHelper.IsValid(file))
                        {
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] Invalid file criteria (mime/size), Terminating...");
                            return new Tuple<bool, int>(false, formTableId);
                        }

                        // Get the current field's value
                        fieldValue = file.FileName;
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] Filename: {fieldValue}");

                        // Get the file extension
                        var extension = Path.GetExtension(file.FileName)?.ToLower();

                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] request, adding field details...");
                        detailsSql =
                            $" INSERT INTO dbo.{detailsTblName} (pageId, TableId, CLCatId, CLIId)" +
                            $" VALUES (@pageId, @TableId, @CLCatId, @CLIId);";

                        detailsParameters = new ExpandoObject() as IDictionary<string, object>;
                        detailsParameters.Add("@pageId", 9049);
                        detailsParameters.Add("@TableId", formHeader);
                        detailsParameters.Add("@CLCatId", fieldID);
                        detailsParameters.Add("@CLIId", fieldValue);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] request, added field details");

                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] saving to database...");
                        var CLDtlId = await DapperHelper.ExecuteScalarAsync<int>(
                            detailsSql +
                           $" UPDATE dbo.{detailsTblName} SET CLIId = 'Doc' + CONVERT(VARCHAR, SCOPE_IDENTITY()) + '{extension}' WHERE CLDtlId = SCOPE_IDENTITY();" +
                            " SELECT CONVERT(INT, SCOPE_IDENTITY());",
                            detailsParameters
                        );
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] saved to database");

                        // Set the file name
                        var fileName = $"Doc{CLDtlId}{extension}";
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] fileName: {fileName}");

                        // Save the file to disk
                        var path = Path.Combine(appFolder, formsFolder, fileName);
                        filesList.Add(path);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] saving to: {path}");
                        using (Stream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write))
                        {
                            file.CopyTo(fileStream);
                        }
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] saved to: {path}");
                    }
                }

                // Get a list of non published fields for this form
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Getting non published fields...");
                var fieldsNonPublishQuery =
                    await DapperHelper.QueryAsync<ChooseListPageCategoryTbl>(
                        " SELECT clp.*, clc.DataType" +
                        " FROM dbo.ChooseListPageTbl clp" +
                        " INNER JOIN dbo.ChooseListCategoryTbl clc ON clp.CLCatId = clc.CLCatId" +
                        $" WHERE clp.pageId = 9049 AND clp.iType = @iType AND (clp.Publish = 0{(checkExternalPublish ? " OR clp.ExternalPublish = 0" : "")})",
                        new
                        {
                            @iType = formId
                        }
                    );
                var feildsNonPublish = fieldsNonPublishQuery.ToList();

                // Get either the preset value or default value depending on the field's type
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processing non published fields...");
                detailsSql = "";
                detailsParameters = new ExpandoObject() as IDictionary<string, object>;
                detailsParameters.Add("@pageId", 9049);
                var count = 0;
                foreach (var item in feildsNonPublish)
                {
                    detailsSql +=
                        $" INSERT INTO dbo.{detailsTblName} (pageId, TableId, CLCatId, CLIId)" +
                        $" VALUES (@pageId, @TableId{count}, @CLCatId{count}, @CLIId{count});";
                    detailsParameters.Add($"@TableId{count}", formHeader);
                    detailsParameters.Add($"@CLCatId{count}", item.CLCatId);
                    detailsParameters.Add($"@CLIId{count++}", GetFieldValue(item.CLCatId.GetValueOrDefault(-1), item.PreSetValue, false, currentUser));
                }

                // Persist the changes to the database
                if (!string.IsNullOrWhiteSpace(detailsSql))
                {
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/nonpub] saving to database...");
                    await DapperHelper.ExceuteAsync(detailsSql, detailsParameters);
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/nonpub] saved to database");
                }

                // Append other required field to the form values so that they can be used in processing
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] adding other required fields...");
                formValues.Add(ControlTypes.CARD_HOLDER_NAME, fields["cardholder"]);
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] added other required fields");

                // Check if we have to run a webservice
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] processing web services: {processWebService}...");
                var processWebServiceTuple = await ProcessWebService(formHeader, formId, contactId, currentUser, formValues, isLogRequest);
                var webServiceResult = processWebService ? processWebServiceTuple.Item1 : 0;
                var webServiceOutput = processWebServiceTuple.Item2;
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] processed web services: {processWebService} - Result: {webServiceResult}");

                // Reset the submit url redirection
           

                // Check if there is a submit url                    
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Checking if submit URL exists...");
                var submitUrl = feildsNonPublish.FirstOrDefault(f => f.DataType == ControlDataTypes.UrlSubmit)?.PreSetValue;
                if (!string.IsNullOrWhiteSpace(submitUrl))
                {
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Submit URL exists");

                    // Get the card's last digits
                    var cardLastDigits = formValues.ContainsKey(ControlTypes.CARD_NUMBER)
                        ? formValues[ControlTypes.CARD_NUMBER]?.GetStringPortion(0, 4)
                        : "";

                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Replacing the submit URL's params");
                    submitUrl = submitUrl?.Replace("@USER_NAME@", currentUser?.Name);
                    submitUrl = submitUrl?.Replace("@USER_CPR@", currentUser?.Username);
                    submitUrl = submitUrl?.Replace("@USER_MOBILE_PHONE@", currentUser?.Mobile);
                    submitUrl = submitUrl?.Replace("@USER_HOME_PHONE@", currentUser?.HomePhone);
                    submitUrl = submitUrl?.Replace("@USER_EMAIL@", currentUser?.Email);
                    submitUrl = submitUrl?.Replace("@CARD_LAST_4@", cardLastDigits);
                    submitUrl = submitUrl?.Replace("@APP_LANG@", LanguageHelper.GetCurrentLangCode());
                    submitUrl = submitUrl?.Replace("@IS_MOBILE@", "Y");
                    submitUrl = submitUrl?.Replace("@WEB_SERVICE_RESULT@", webServiceOutput ?? "-1");
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Replaced the submit URL's params: {submitUrl}");


                    
                }

                // Check if we have to send an email/sms
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processing email: {processEmail}");
                var processEmailSms = await ProcessEmailSms(formHeader, formId, contactId, currentUser, formValues, isLogRequest);
                var emailSmsResult = processEmail ? processEmailSms : 0;
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processed email: {processEmail} - Result: {emailSmsResult}");

                // Process wallet general receipts if required
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Fetching wallet transfer amount: {fields[General.InputPrefix + ControlTypes.AMOUNT]}");
                var amount = -1m;
                var targetAmount = -1m;
                List<int> WalletForms = new List<int> {
                    PageTypes.WALLET_ADD_MONEY,
                    PageTypes.WALLET_PAY_MERCHANT,
                    PageTypes.WALLET_SEND_MONEY,
                     PageTypes.WALLET_SCAN_AND_PAY,
                    PageTypes.WALLET_REQUEST_MONEY,
                    PageTypes.CARD_ADD_MONEY,
                    PageTypes.CARD_SEND_MONEY
                };

                var sAmount = fields[General.InputPrefix + ControlTypes.AMOUNT];
                var sTargetAmount = sAmount;
                if (quote != null && quote.CalculatedAmount > 0)
                {
                    sAmount = quote.IsSourceAmount ? sAmount : quote.CalculatedAmount + "";
                    sTargetAmount = quote.IsSourceAmount ? quote.CalculatedAmount + "" : sTargetAmount;
                }

                if (WalletForms.Contains(formId) && (!decimal.TryParse(sAmount, out amount) || amount <= 0 || !decimal.TryParse(sTargetAmount, out targetAmount) || targetAmount <= 0))
                {
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] wallet transfer amount is invalid.");
                    // _controller?.AddNotification(Resources.Services.InvalidAmountError, NotificationType.ERROR);
                    return new Tuple<bool, int>(false, formTableId);
                }

                var fromCAID = -1;
                var toCAID = -1;
                var processWallet = false;
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processing switch(Wallet/Card Actions) depend on formId => {formId}");

                var column = "";
                var input = "";

                switch (formId)
                {
                    case PageTypes.WALLET_PAY_MERCHANT:
                        processWallet = true;
                        fromCAID = caid;
                        var toCAIDQuery = await DapperHelper.QueryAsync<int>(
                            " SELECT CA.CAID" +
                            " FROM dbo.ContactTbl CT" +
                            " INNER JOIN dbo.CustomerAccountTbl CA ON CA.RefContactID = CT.ContactID AND CA.CustActId = 1" +
                            " WHERE CT.ContactMobile = @Phone",
                            new
                            {
                                Phone = fields[General.InputPrefix + 417]
                            }
                        );
                        toCAID = toCAIDQuery.FirstOrDefault();
                        break;
                    case PageTypes.WALLET_SCAN_AND_PAY:
                    case PageTypes.WALLET_SEND_MONEY:
                    case PageTypes.CARD_SEND_MONEY:
                    case PageTypes.WALLET_ADD_MONEY:
                    case PageTypes.CARD_ADD_MONEY:
                        processWallet = true;
                        fromCAID = caid;
                        if (formId == PageTypes.CARD_SEND_MONEY || (formId == PageTypes.WALLET_ADD_MONEY && fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_CARD_ALT + ""))
                        {
                            if (quote != null)
                            {
                                fromCAID = quote.FromCAID;
                                toCAID = quote.ToCAID;
                            }
                            else
                            {
                                //Get the card CAID
                                var fromCAIDQuery = await DapperHelper.QueryAsync<int>(
                                    " SELECT CA.CAID" +
                                    " FROM dbo.ContactTbl CT" +
                                    " INNER JOIN dbo.CustomerFile CF ON CT.CustomerID = CF.RecID" +
                                    " INNER JOIN dbo.CustomerRelationTbl CR ON CR.CustomerID = CF.RecID" +
                                    " INNER JOIN dbo.CustomerAccountTbl CA ON CA.CRID = CR.CRID AND CA.RefContactID = CT.ContactID AND CA.CustActId = 2" +
                                    " INNER JOIN dbo.ContactRelationTbl CRT ON CRT.ContactID = CT.ContactID AND CRT.CRID = CR.CRID" +
                                    $" WHERE CT.ContactId=@input AND CT.[Login] = 1 AND ct.Active = 1 AND CT.MobileLogin=1 ",
                                    new
                                    {
                                        input = currentUser.ContactID
                                    }
                                );
                                fromCAID = fromCAIDQuery.FirstOrDefault();
                            }
                        }

                        if (formId == PageTypes.WALLET_ADD_MONEY || formId == PageTypes.CARD_ADD_MONEY)
                        {
                            //If Payment option is debit card()
                            if (fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] + "" == PickerValues.PAYMENT_OPTION_DEBIT_CARD + "")
                            {
                                //Form is inserted return true to redirect to benefit
                                return new Tuple<bool, int>(true, formTableId);
                            }

                            toCAID = caid;

                            if (new List<string> { PickerValues.PAYMENT_OPTION_WALLET_ACCOUNT + "", PickerValues.PAYMENT_OPTION_CARD + "", PickerValues.PAYMENT_OPTION_CARD_ALT + "" }.Contains(fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] + "") && quote != null)
                            {
                                fromCAID = quote.FromCAID;
                                toCAID = quote.ToCAID;
                            }
                        }
                        else if (formId == PageTypes.WALLET_SEND_MONEY && fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + "" == PickerValues.PAYMENT_OPTION_CARD + "")
                        {
                            if (quote != null)
                            {
                                fromCAID = quote.FromCAID;
                                toCAID = quote.ToCAID;
                            }
                            else
                            {
                                // Send Money from wallet to own card - Get the card CAID                            
                                var toCAIDQueryT = await DapperHelper.QueryAsync<int>(
                                    " SELECT CA.CAID" +
                                    " FROM dbo.ContactTbl CT" +
                                    " INNER JOIN dbo.CustomerFile CF ON CT.CustomerID = CF.RecID" +
                                    " INNER JOIN dbo.CustomerRelationTbl CR ON CR.CustomerID = CF.RecID" +
                                    " INNER JOIN dbo.CustomerAccountTbl CA ON CA.CRID = CR.CRID AND CA.RefContactID = CT.ContactID AND CA.CustActId = 2" +
                                    " INNER JOIN dbo.ContactRelationTbl CRT ON CRT.ContactID = CT.ContactID AND CRT.CRID = CR.CRID" +
                                    $" WHERE CT.ContactId=@input AND CT.[Login] = 1 AND ct.Active = 1 AND CT.MobileLogin=1 ",
                                    new
                                    {
                                        input = currentUser.ContactID
                                    }
                                );
                                toCAID = toCAIDQueryT.FirstOrDefault();
                            }
                        }
                        else
                        {
                            if (quote != null)
                            {
                                fromCAID = quote.FromCAID;
                                toCAID = quote.ToCAID;
                            }
                            else
                            {
                                var senViewModelCPR = fields[General.InputPrefix + ControlTypes.PERSONAL_ID] + "";
                                var senViewModelMobile = fields[General.InputPrefix + 418] + "";

                                //Get column and value of contact
                                get_contactTbl_Columns(senViewModelCPR, senViewModelMobile, out column, out input);

                                var toCAIDQueryX = await DapperHelper.QueryAsync<int>(
                                    " SELECT CA.CAID" +
                                    " FROM dbo.ContactTbl CT" +
                                    " INNER JOIN dbo.CustomerAccountTbl CA ON CA.RefContactID = CT.ContactID AND CA.CustActId = 1" +
                                    $" WHERE CT.{column} = @input",
                                    new
                                    {
                                        input = input
                                    }
                                );
                                toCAID = toCAIDQueryX.FirstOrDefault();
                            }
                        }

                        break;

                }

                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Result of switch(Wallet/Card Actions) to be processed: processWallet: {processWallet} | From: {fromCAID} -> To: {toCAID} | CSID:{formTableId} | formId:{formId}");
                if (processWallet)
                {
                    if (fromCAID <= 0 || toCAID <= 0)
                    {
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] CAIDs are invalid.");
                        // _controller?.AddNotification(Resources.Services.NotMatchInOurRecordError, NotificationType.ERROR);
                        return new Tuple<bool, int>(false, formTableId);
                    }

                    //Update from caid & to caid
                    await DapperHelper.ExceuteAsync(
                        $"UPDATE ChooseListDetailsLogTbl SET CLIId='{fromCAID}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.CAID_FROM};"
                      + $"UPDATE ChooseListDetailsLogTbl SET CLIId='{toCAID}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.CAID_TO};"
                      + $"UPDATE ChooseListDetailsLogTbl SET CLIId='{currentUser.Username}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.FROM };"
                      );

                    // Create a general receipt for this payment
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processing general receipt...");

                    //set type id of usp_CreateGeneralReceiptWalletTransfer
                    var typeId = 2; // wallet to wallet
                    var isSuccessResponse = true;
                    if ((formId == PageTypes.CARD_ADD_MONEY && fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] + "" == PickerValues.PAYMENT_OPTION_WALLET_ACCOUNT + "") ||
                        (formId == PageTypes.WALLET_SEND_MONEY && new List<string> { PickerValues.PAYMENT_OPTION_CARD + "", PickerValues.PAYMENT_OPTION_OTHER_CARDS + "" }.Contains(fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + ""))
                       )
                    {
                        typeId = 3; // wallet to card
                    }
                    else if ((formId == PageTypes.WALLET_ADD_MONEY && fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_CARD_ALT + "") ||
                             (formId == PageTypes.CARD_SEND_MONEY && new List<string> { PickerValues.PAYMENT_OPTION_MY_ACCOUNTS + "", PickerValues.PAYMENT_OPTION_OTHERS + "" }.Contains(fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + ""))
                            )
                    {
                        typeId = 4; // card to wallet
                    }
                    else if ((formId == PageTypes.CARD_ADD_MONEY && fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_CARD_ALT + "") ||
                             (formId == PageTypes.CARD_SEND_MONEY && new List<string> { PickerValues.PAYMENT_OPTION_CARD + "", PickerValues.PAYMENT_OPTION_OTHER_CARDS + "" }.Contains(fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + ""))
                            )
                    {
                        typeId = 5; // card to card
                    }
                    else if (formId == PageTypes.WALLET_SEND_MONEY && fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + "" == PickerValues.PAYMENT_OPTION_WITHIN_BH + "")
                    {
                        typeId = 8; // wallet to IBAN BH
                    }

                    var result = isSuccessResponse ? await DapperHelper.ExecuteScalarAsync<string>(
                        $" EXEC dbo.usp_CreateGeneralReceiptWalletTransfer @UserName = N'{currentUser.Username}', @FromCAID = {fromCAID}, @FromAmount = {amount}, @ToCAID = {toCAID}, @BatchNo = 0, @FormID = {formId}, @CSID = {formTableId}, @Type = {typeId}, @ToAmount = {targetAmount}, @ExchangeRate = {quote.ExchangeRate};"
                    ) : "-1";

                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processed general receipt: {result}");




                    int ReceiptId = 0;
                    int.TryParse(result, out ReceiptId);
                    isSuccessResponse = isSuccessResponse && ReceiptId > 0;
                    #region UPDATE STATUS
                    //if (isSuccessResponse && (formId == PageTypes.CARD_ADD_MONEY /*|| (formId == PageTypes.WALLET_SEND_MONEY && fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + "" == PickerValues.PAYMENT_OPTION_CARD + "")*/))
                    //{
                    //    var cardNumber = formValues.ContainsKey(ControlTypes.CARD_NUMBER) ? formValues[ControlTypes.CARD_NUMBER] : "";
                    //    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Calling F13_PayMyCardPayment_Raw Card Number[{cardNumber}]");
                    //    var paymentResponse = WebServicesHelper.F13_PayMyCardPayment_Raw(cardNumber, amount + "", currentUser.Username, ReceiptId + "", fromCAID + "", "WC");
                    //    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Called F13_PayMyCardPayment_Raw Response: {paymentResponse}");
                    //    isSuccessResponse = paymentResponse == "000";
                    //    // TODO: Upon error, execute a wallet reversal.
                    //}

                    if (isSuccessResponse)
                    {
                        await DapperHelper.ExceuteAsync($"UPDATE ChooseListDetailsLogTbl SET CLIId='{StatusValues.APPROVED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS};"
                            + $"UPDATE ChooseListDetailsLogTbl SET CLIId='{result}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.RECEIPT_ID};"
                            );
                    }
                    else
                    {
                        await DapperHelper.ExceuteAsync($"UPDATE ChooseListDetailsLogTbl SET CLIId='{StatusValues.DECLINED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS}");
                        webServiceResult = -1;
                    }
                    #endregion

                    #region Notification Process
                    if (isSuccessResponse && (typeId == 3 || typeId == 4))
                    {
                        /*var cardCaid = formValues.ContainsKey(ControlTypes.CARD_NUMBER) ? formValues[ControlTypes.CARD_NUMBER] : "";
                        var cardDetails = DapperHelper.Query<KeyValuePair<string, decimal>>("SELECT CustAcCode AS [Key], ISNULL(StatementBalance, 0) AS [VALUE] FROM dbo.CustomerAccountTbl WHERE CAID = @CAID", new
                        {
                            CAID = cardCaid.Decrypt(General.CAIDEncryptPurpose)
                        }).FirstOrDefault();

                        var cardNumber = cardDetails.Key;
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Called F5_GetBalance Response to get the new card balance");
                        var customerBalance = cardDetails.Value;
                        cardNumber = cardNumber?.Substring(cardNumber.Length - 4, 4);
                        DapperHelper.ExecuteScalar<string>(
                               $" EXEC dbo.usp_SendReceiptGeneralProcessNotification_CardWallet @FromCAID = {fromCAID}, @Amount = {amount}, @ToCAID = {toCAID}, @ProductId = {formId}, @TypeId = {typeId},@CardNo=N'{cardNumber}',@CardBalance={customerBalance};"
                           );*/

                    }
                    #endregion

                    #region IBAN Money Transfer - B2B File
                    if (isSuccessResponse && typeId == 8)
                    {
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating a payment file...");
                        var fileName = (Config.GetStringValue("AUBBAHPaymentFileName") ?? "MALLAT_PAY_@DATE@_@TIME@").Replace("@DATE@", DateTime.Now.ToString("ddMMyyyy")).Replace("@TIME@", DateTime.Now.ToString("HHmmss"));
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating a payment file, fileName: {fileName}");
                        var filePath = Config.GetStringValue("AUBBAHPaymentFolder") + WalletApplication.BranchId + "\\Payments\\";
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating a payment file, filePath: {filePath}");
                        if (!Directory.Exists(filePath))
                        {
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Directory doesn't exist, creating a new one...");
                            Directory.CreateDirectory(filePath);
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Directory doesn't exist, created a new one");
                        }


                        var iban = fields[General.InputPrefix + ControlTypes.BENEFICIARY_IBAN] + "";
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Beneficiary IBAN: {iban}");

                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Getting beneficiary payment data...");
                        var paymentData = DapperHelper.Query<MoneyTransferBeneficiary>($@"
                            SELECT Id AS Id, BN.BeneficiaryName AS Name, BK.BankName AS BankName, NULL AS BankAccount, BN.BeneficiarySWIFTCode AS BankSwiftCode, BN.BeneficiaryIBAN AS BankIBAN
	                        FROM dbo.Wallet_BeneficiaryTbl BN
	                        INNER JOIN dbo.Banks BK ON BK.BankID = BN.BeneficiaryBankId
                            WHERE BN.ContactId = @ContactId AND BN.BeneficiaryIBAN = @IBAN
                        ", new
                        {
                            ContactId = currentUser.ContactID,
                            IBAN = iban
                        }).FirstOrDefault();
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Got beneficiary payment data: {JsonConvert.SerializeObject(paymentData)}");

                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating the payment text file...");
                        FileInfo t = new FileInfo($"{filePath}{fileName}.txt");
                        StreamWriter Tex = t.CreateText();

                        // Header Line
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating the payment text file header...");
                        var receiptRef = ReceiptId + "";
                        var crNumber = Config.GetStringValue("AUBBAHPaymentCompanyCR") ?? "44562-3";
                        var debitAccountNumber = Config.GetStringValue("AUBBAHPaymentDebitAccountNumber") ?? "*************";
                        var debitDescription = $"PAYMENT RECEIPT #{receiptRef}";
                        var headerLine = $"S1,{crNumber},{debitAccountNumber},MXD,1,{debitDescription},{DateTime.Now.ToString("dd/MM/yyyy")},{(Config.GetStringValue("AUBBAHPaymentBatchReference") ?? "MALLAT_PAY_@REF@").Replace("@REF@", receiptRef)}";
                        Tex.WriteLine(headerLine);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Created the payment text file header: {headerLine}");

                        // Details Line
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating the payment text file details...");
                        var transactionType = paymentData.BankSwiftCode.Equals("AUBBBHBM", StringComparison.InvariantCultureIgnoreCase) ? "TRF" : "LCL";
                        var creditAmount = amount.FormatAmount(WalletApplication.NoOfDecimals, "", false, "").Replace(",", "");
                        var exchangeRate = "";
                        var creditDescription = $"WALLET TRANSFER RECEIPT #{receiptRef}";
                        var detailsLine = $"S2,{transactionType},{creditAmount},{WalletApplication.Currency},{exchangeRate},,,,{paymentData.BankIBAN.ToUpper()},{receiptRef},,,{creditDescription},{creditDescription},,,,{paymentData.Name},,,{paymentData.BankName},,,,{paymentData.BankSwiftCode},,,,,,,,,,";
                        Tex.WriteLine(detailsLine);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Created the payment text file details: {detailsLine}");

                        // Trailer Line
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating the payment text file trailer...");
                        var trailerLine = $"S3,1";
                        Tex.WriteLine(trailerLine);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Created the payment text file trailer: {trailerLine}");

                        Tex.Flush();
                        Tex.Close();

                        if (Config.GetStringValue("AUBBAHPaymentFolderBatch") != null)
                        {
                            try
                            {
                                var f = Config.GetStringValue("AUBBAHPaymentFolderBatch").ToString().Replace("@branch@", WalletApplication.BranchId);
                                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Invoking the batch file: {f}");
                                if (System.IO.File.Exists(f))
                                {
                                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Starting the process...");
                                    Process a = new Process();
                                    a.StartInfo.FileName = f;
                                    a.StartInfo.CreateNoWindow = true;
                                    a.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                                    var isStarted = a.Start();
                                    a.Dispose();
                                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Started the process: {isStarted}");
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Failed to run the batch file.", ex);
                            }
                        }
                    }
                    #endregion
                }

                // Commit the changes if the email and/or sms were processed successfully
                var isSuccess = emailSmsResult >= 0 && webServiceResult >= 0;

                /* if (formId == PageTypes.STOP_CARD_REQUEST && isSuccess)
                {
                    // Get the card's last 4 digits
                    var cardNumber = formValues.ContainsKey(ControlTypes.CARD_NUMBER) ? formValues[ControlTypes.CARD_NUMBER] : "";
                    cardNumber = WebServicesHelper.GetCardNumber(cardNumber).FormatCardNumber(true, true, "0:0000", 0, 6);

                    DapperHelper.Exceute(
                        " INSERT INTO dbo.MobileNotificationProcessingQueue (CustomerId, MessageContent, MessageType, DateCreated, DateModified, Processed, NoOfTrails, Cont_CPR)" +
                        $" VALUES (@CustomerId, @MessageContent, @MessageType, @DateCreated, @DateModified, @Processed, @NoOfTrails, @Cont_CPR)",
                        new
                        {
                            CustomerId = customerId,
                            MessageContent = string.Format(Resources.Services.StopCardSuccessWithNumber, cardNumber),
                            MessageType = "A",
                            DateCreated = DateTime.Now,
                            DateModified = DateTime.Now,
                            Processed = false,
                            NoOfTrails = 0,
                            Cont_CPR = currentUser?.Username
                        }
                    );
                } */

                // Reset the form data (PCI Requirement)
                fields = null; formValues = null; GC.Collect();

                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Is successful request: {isSuccess}.");
                return new Tuple<bool, int>(isSuccess, formTableId);
            }
            catch (Exception ex)
            {
                _logger.LogError($"[FormRepository/InsertRequest/{formId}/{customerId}/-1]", ex);
            }

            // Something went wrong
            formTableId = -1;

            // Remove any leftover files
            if (filesList.Any())
            {
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/-1] Removing uploaded files...");
                foreach (var file in filesList.Where(File.Exists))
                {
                    File.Delete(file);
                }
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/-1] Removed uploaded files");
            }

            return new Tuple<bool, int>(false, formTableId);
        }

        public async Task<Tuple<bool, int>> InsertDictionaryRequest(int formId, int customerId, int contactId, string username, Dictionary<string, string> fields, int formTableId, AppUser currentUser = null, bool processEmail = true, bool processWebService = true, bool isLogRequest = false, int caid = -1, int crid = -1, WalletExchangeQuote quote = null, bool checkExternalPublish = false)
        {
            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}] Entered");

            // Keep track of the uploaded files paths.
            // This will be used to delete the files if the transaction wasn't commited.
            var filesList = new List<string>();

            try
            {
                var headerTblName = isLogRequest ? "CustomerServicesLogTbl" : "CustomerServicesTbl";
                var detailsTblName = isLogRequest ? "ChooseListDetailsLogTbl" : "ChooseListDetailsTbl";
                Dictionary<int, string> formValues;

                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}] request, creating header {headerTblName}... ");
                var formHeader = await DapperHelper.ExecuteScalarAsync<int>(
                   $" INSERT INTO dbo.{headerTblName} (ProductID, UserModified, DateModified, Active, RecID, Posted, TypeID, CAID, CRID,ContactId)" +
                    " VALUES (@ProductID, @UserModified, @DateModified, @Active, @RecID, @Posted, @TypeID, @CAID, @CRID,@ContactId);" +
                    " SELECT CONVERT(INT, SCOPE_IDENTITY());",
                    new
                    {
                        ProductID = formId,
                        UserModified = username,
                        DateModified = DateTime.Now,
                        Active = true,
                        RecID = customerId,
                        Posted = 1,
                        CAID = caid == -1 ? currentUser?.CAID : caid,
                        CRID = currentUser?.CRID ?? crid,
                        TypeID = 0,
                        ContactId = currentUser?.ContactID ?? contactId
                    }
                );
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}] request, adding header to database {headerTblName}");

                // Set the form table id
                formTableId = formHeader;
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] determined header id");

                // Keep track of the values so that it can be used to repopulate the form
                formValues = new Dictionary<int, string>();


                int fieldID;
                string fieldValue;
                var detailsSql = "";
                var detailsParameters = new ExpandoObject() as IDictionary<string, object>;
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] checking submitted fields: {JsonConvert.SerializeObject(fields)}");
                foreach (var field in fields)
                {
                    string fieldKey = field.Key;
                    // Skip over any field that doesn't follow the naming convention
                    var key = fieldKey.Replace(General.InputPrefix, "");
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] field: {key}");
                    if (!int.TryParse(key, out fieldID))
                    {
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] field key is invalid, skipping...");
                        continue;
                    }

                    // Get the current field's value and add it to the dictionary
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] getting field's value");
                    fieldValue = GetFieldValue(fieldID, fields[fieldKey], true, currentUser);
                    formValues.Add(fieldID, fieldValue);
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] field - {fieldKey}: {fieldValue}");

                    // Build the field's data
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Normal request, adding field details...");
                    detailsSql =
                        $" INSERT INTO dbo.{detailsTblName} (pageId, TableId, CLCatId, CLIId)" +
                         " VALUES (@pageId, @TableId, @CLCatId, @CLIId);" +
                         " SELECT CONVERT(INT, SCOPE_IDENTITY());";

                    detailsParameters = new ExpandoObject() as IDictionary<string, object>;
                    detailsParameters.Add("@pageId", 9049);
                    detailsParameters.Add("@TableId", formHeader);
                    detailsParameters.Add("@CLCatId", fieldID);
                    detailsParameters.Add("@CLIId", fieldValue);
                    var CLDtlId = await DapperHelper.ExecuteScalarAsync<int>(detailsSql, detailsParameters);
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Normal request, added field details");

                    // If we have a non-empty signature
                    if (new List<int> { ControlTypes.SIGNATURE, 213, 214, ControlTypes.CUST_KYC_CPR_PROOF__UPLOAD, /*Passport*/337, ControlTypes.CUST_KYC_FACE_PROOF_UPLOAD, ControlTypes.CUST_KYC_ADDRESS_PROOF_UPLOAD }.Contains(fieldID) && !string.IsNullOrWhiteSpace(fieldValue))
                    {
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] Signature field found");

                        // Extract the base64 data
                        var base64Groups = Regex.Match(fieldValue, @"data:image/(?<type>.+?),(?<data>.+)");
                        var base64Type = base64Groups?.Groups["type"]?.Value;
                        var base64Data = base64Groups?.Groups["data"]?.Value;

                        //TODO:Correct this code I use it temp for session
                        if (new List<int> { 213, 214, ControlTypes.CUST_KYC_CPR_PROOF__UPLOAD, /*Passport*/337, ControlTypes.CUST_KYC_FACE_PROOF_UPLOAD, ControlTypes.CUST_KYC_ADDRESS_PROOF_UPLOAD }.Contains(fieldID)) { base64Type = base64Type.Replace(";base64", ""); }

                        // Error: Couldn't extract the base64 data
                        if (string.IsNullOrWhiteSpace(base64Data))
                        {
                            return new Tuple<bool, int>(false, formTableId);
                        }

                        // Get the file extension
                        string extension = $".{base64Type}";

                        // Set the file name
                        var fileName = $"Doc{CLDtlId}{extension}";
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] Filename: {fileName}");

                        // Save the changes in order to populate the primary key column
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] saving to database...");
                        await DapperHelper.ExecuteScalarAsync<int>(
                           $" UPDATE dbo.{detailsTblName} SET CLIId = '{fileName}' WHERE CLDtlId = {CLDtlId};",
                            detailsParameters
                        );
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] saved to database");

                        // Convert the data to bytes in preperation for creating the image file
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] converting to bytes...");
                        var bytesData = Convert.FromBase64String(base64Data);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] converted to bytes");

                        // Get the forms folder in which the files will be uploaded
                        var appFolder = WalletApplication.AppFolder;
                        var formsFolder = Config.GetStringValue("FormsFolder");
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] Application Folder: {appFolder}");
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] Forms Folder: {formsFolder}");

                        // Save the file to disk
                        var path = Path.Combine(appFolder, formsFolder, fileName);
                        filesList.Add(path);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] Filepath: {path}");
                        using (var imageFile = new FileStream(path, FileMode.Create))
                        {
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] saving image to disk...");
                            imageFile.Write(bytesData, 0, bytesData.Length);
                            imageFile.Flush();
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/sign] image saved");
                        }
                    }
                }

                if (formValues.Count > 0)
                {
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Setting the temp data");
                    //_controller.TempData["FormId"] = formId;
                    // _controller.TempData["FormValues"] = formValues;
                    //_controller.TempData["IsLogRequest"] = isLogRequest + "";
                }

                if (_httpContextAccessor != null && _httpContextAccessor.HttpContext.Request.Form.Files.Count > 0)
                {
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files] Attachment files found");

                    // Get the forms folder in which the files will be uploaded
                    var appFolder = WalletApplication.AppFolder;
                    var formsFolder = Config.GetStringValue("FormsFolder");
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files] Application Folder: {appFolder}");
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files] Forms Folder: {formsFolder}");

                    // Get the uploaded files
                    var files = _httpContextAccessor.HttpContext.Request.Form.Files;
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files] Count: {files.Count}");

                    // Process the files                        
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files] Processing files...");
                    foreach (var file in files)
                    {
                        // Skip over any field that doesn't follow the naming convention
                        var id = file.Name.Replace(General.InputPrefix, "");
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] Checking format...");

                        if (!int.TryParse(id, out fieldID) || file == null || file.Length <= 0)
                        {
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] Invalid file (id/not exist), skipping over...");
                            continue;
                        }

                        // Make sure that the file is valid
                        if (!FileUploadHelper.IsValid(file))
                        {
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] Invalid file criteria (mime/size), Terminating...");
                            return new Tuple<bool, int>(false, formTableId);
                        }

                        // Get the current field's value
                        fieldValue = file.FileName;
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] Filename: {fieldValue}");

                        // Get the file extension
                        var extension = Path.GetExtension(file.FileName)?.ToLower();

                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] request, adding field details...");
                        detailsSql =
                            $" INSERT INTO dbo.{detailsTblName} (pageId, TableId, CLCatId, CLIId)" +
                            $" VALUES (@pageId, @TableId, @CLCatId, @CLIId);";

                        detailsParameters = new ExpandoObject() as IDictionary<string, object>;
                        detailsParameters.Add("@pageId", 9049);
                        detailsParameters.Add("@TableId", formHeader);
                        detailsParameters.Add("@CLCatId", fieldID);
                        detailsParameters.Add("@CLIId", fieldValue);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] request, added field details");

                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] saving to database...");
                        var CLDtlId = await DapperHelper.ExecuteScalarAsync<int>(
                            detailsSql +
                           $" UPDATE dbo.{detailsTblName} SET CLIId = 'Doc' + CONVERT(VARCHAR, SCOPE_IDENTITY()) + '{extension}' WHERE CLDtlId = SCOPE_IDENTITY();" +
                            " SELECT CONVERT(INT, SCOPE_IDENTITY());",
                            detailsParameters
                        );
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] saved to database");

                        // Set the file name
                        var fileName = $"Doc{CLDtlId}{extension}";
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] fileName: {fileName}");

                        // Save the file to disk
                        var path = Path.Combine(appFolder, formsFolder, fileName);
                        filesList.Add(path);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] saving to: {path}");
                        using (Stream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write))
                        {
                            file.CopyTo(fileStream);
                        }
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/files/{id}] saved to: {path}");
                    }
                }

                // Get a list of non published fields for this form
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Getting non published fields...");
                var fieldsNonPublishQuery =
                    await DapperHelper.QueryAsync<ChooseListPageCategoryTbl>(
                        " SELECT clp.*, clc.DataType" +
                        " FROM dbo.ChooseListPageTbl clp" +
                        " INNER JOIN dbo.ChooseListCategoryTbl clc ON clp.CLCatId = clc.CLCatId" +
                        $" WHERE clp.pageId = 9049 AND clp.iType = @iType AND (clp.Publish = 0{(checkExternalPublish ? " OR clp.ExternalPublish = 0" : "")})",
                        new
                        {
                            @iType = formId
                        }
                    );
                var feildsNonPublish = fieldsNonPublishQuery.ToList();

                // Get either the preset value or default value depending on the field's type
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processing non published fields...");
                detailsSql = "";
                detailsParameters = new ExpandoObject() as IDictionary<string, object>;
                detailsParameters.Add("@pageId", 9049);
                var count = 0;
                foreach (var item in feildsNonPublish)
                {
                    detailsSql +=
                        $" INSERT INTO dbo.{detailsTblName} (pageId, TableId, CLCatId, CLIId)" +
                        $" VALUES (@pageId, @TableId{count}, @CLCatId{count}, @CLIId{count});";
                    detailsParameters.Add($"@TableId{count}", formHeader);
                    detailsParameters.Add($"@CLCatId{count}", item.CLCatId);
                    detailsParameters.Add($"@CLIId{count++}", GetFieldValue(item.CLCatId.GetValueOrDefault(-1), item.PreSetValue, false, currentUser));
                }

                // Persist the changes to the database
                if (!string.IsNullOrWhiteSpace(detailsSql))
                {
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/nonpub] saving to database...");
                    await DapperHelper.ExceuteAsync(detailsSql, detailsParameters);
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/nonpub] saved to database");
                }

                // Append other required field to the form values so that they can be used in processing
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] adding other required fields...");
                formValues.Add(ControlTypes.CARD_HOLDER_NAME, fields["cardholder"]);
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] added other required fields");

                // Check if we have to run a webservice
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] processing web services: {processWebService}...");
                var processWebServiceTuple = await ProcessWebService(formHeader, formId, contactId, currentUser, formValues, isLogRequest);
                var webServiceResult = processWebService ? processWebServiceTuple.Item1 : 0;
                var webServiceOutput = processWebServiceTuple.Item2;
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] processed web services: {processWebService} - Result: {webServiceResult}");

                

                // Check if there is a submit url                    
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Checking if submit URL exists...");
                var submitUrl = feildsNonPublish.FirstOrDefault(f => f.DataType == ControlDataTypes.UrlSubmit)?.PreSetValue;
                if (!string.IsNullOrWhiteSpace(submitUrl))
                {
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Submit URL exists");

                    // Get the card's last digits
                    var cardLastDigits = formValues.ContainsKey(ControlTypes.CARD_NUMBER)
                        ? formValues[ControlTypes.CARD_NUMBER]?.GetStringPortion(0, 4)
                        : "";

                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Replacing the submit URL's params");
                    submitUrl = submitUrl?.Replace("@USER_NAME@", currentUser?.Name);
                    submitUrl = submitUrl?.Replace("@USER_CPR@", currentUser?.Username);
                    submitUrl = submitUrl?.Replace("@USER_MOBILE_PHONE@", currentUser?.Mobile);
                    submitUrl = submitUrl?.Replace("@USER_HOME_PHONE@", currentUser?.HomePhone);
                    submitUrl = submitUrl?.Replace("@USER_EMAIL@", currentUser?.Email);
                    submitUrl = submitUrl?.Replace("@CARD_LAST_4@", cardLastDigits);
                    submitUrl = submitUrl?.Replace("@APP_LANG@", LanguageHelper.GetCurrentLangCode());
                    submitUrl = submitUrl?.Replace("@IS_MOBILE@", "Y");
                    submitUrl = submitUrl?.Replace("@WEB_SERVICE_RESULT@", webServiceOutput ?? "-1");
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Replaced the submit URL's params: {submitUrl}");


                    
                }

                // Check if we have to send an email/sms
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processing email: {processEmail}");
                var processEmailSms = await ProcessEmailSms(formHeader, formId, contactId, currentUser, formValues, isLogRequest);
                var emailSmsResult = processEmail ? processEmailSms : 0;
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processed email: {processEmail} - Result: {emailSmsResult}");

                // Process wallet general receipts if required
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Fetching wallet transfer amount: {fields[General.InputPrefix + ControlTypes.AMOUNT]}");
                var amount = -1m;
                var targetAmount = -1m;
                List<int> WalletForms = new List<int> {
                    PageTypes.WALLET_ADD_MONEY,
                    PageTypes.WALLET_PAY_MERCHANT,
                    PageTypes.WALLET_SEND_MONEY,
                     PageTypes.WALLET_SCAN_AND_PAY,
                    PageTypes.WALLET_REQUEST_MONEY,
                    PageTypes.CARD_ADD_MONEY,
                    PageTypes.CARD_SEND_MONEY
                };

                var sAmount = fields[General.InputPrefix + ControlTypes.AMOUNT];
                var sTargetAmount = sAmount;
                if (quote != null && quote.CalculatedAmount > 0)
                {
                    sAmount = quote.IsSourceAmount ? sAmount : quote.CalculatedAmount + "";
                    sTargetAmount = quote.IsSourceAmount ? quote.CalculatedAmount + "" : sTargetAmount;
                }

                if (WalletForms.Contains(formId) && (!decimal.TryParse(sAmount, out amount) || amount <= 0 || !decimal.TryParse(sTargetAmount, out targetAmount) || targetAmount <= 0))
                {
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] wallet transfer amount is invalid.");
                    // _controller?.AddNotification(Resources.Services.InvalidAmountError, NotificationType.ERROR);
                    return new Tuple<bool, int>(false, formTableId);
                }

                var fromCAID = -1;
                var toCAID = -1;
                var processWallet = false;
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processing switch(Wallet/Card Actions) depend on formId => {formId}");

                var column = "";
                var input = "";

                switch (formId)
                {
                    case PageTypes.WALLET_PAY_MERCHANT:
                        processWallet = true;
                        fromCAID = caid;
                        var toCAIDQuery = await DapperHelper.QueryAsync<int>(
                            " SELECT CA.CAID" +
                            " FROM dbo.ContactTbl CT" +
                            " INNER JOIN dbo.CustomerAccountTbl CA ON CA.RefContactID = CT.ContactID AND CA.CustActId = 1" +
                            " WHERE CT.ContactMobile = @Phone",
                            new
                            {
                                Phone = fields[General.InputPrefix + 417]
                            }
                        );
                        toCAID = toCAIDQuery.FirstOrDefault();
                        break;
                    case PageTypes.WALLET_SCAN_AND_PAY:
                    case PageTypes.WALLET_SEND_MONEY:
                    case PageTypes.CARD_SEND_MONEY:
                    case PageTypes.WALLET_ADD_MONEY:
                    case PageTypes.CARD_ADD_MONEY:
                        processWallet = true;
                        fromCAID = caid;
                        if (formId == PageTypes.CARD_SEND_MONEY || (formId == PageTypes.WALLET_ADD_MONEY && fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_CARD_ALT + ""))
                        {
                            if (quote != null)
                            {
                                fromCAID = quote.FromCAID;
                                toCAID = quote.ToCAID;
                            }
                            else
                            {
                                //Get the card CAID
                                var fromCAIDQuery = await DapperHelper.QueryAsync<int>(
                                    " SELECT CA.CAID" +
                                    " FROM dbo.ContactTbl CT" +
                                    " INNER JOIN dbo.CustomerFile CF ON CT.CustomerID = CF.RecID" +
                                    " INNER JOIN dbo.CustomerRelationTbl CR ON CR.CustomerID = CF.RecID" +
                                    " INNER JOIN dbo.CustomerAccountTbl CA ON CA.CRID = CR.CRID AND CA.RefContactID = CT.ContactID AND CA.CustActId = 2" +
                                    " INNER JOIN dbo.ContactRelationTbl CRT ON CRT.ContactID = CT.ContactID AND CRT.CRID = CR.CRID" +
                                    $" WHERE CT.ContactId=@input AND CT.[Login] = 1 AND ct.Active = 1 AND CT.MobileLogin=1 ",
                                    new
                                    {
                                        input = currentUser.ContactID
                                    }
                                );
                                fromCAID = fromCAIDQuery.FirstOrDefault();
                            }
                        }

                        if (formId == PageTypes.WALLET_ADD_MONEY || formId == PageTypes.CARD_ADD_MONEY)
                        {
                            //If Payment option is debit card()
                            if (fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] + "" == PickerValues.PAYMENT_OPTION_DEBIT_CARD + "")
                            {
                                //Form is inserted return true to redirect to benefit
                                return new Tuple<bool, int>(true, formTableId);
                            }

                            toCAID = caid;

                            if (new List<string> { PickerValues.PAYMENT_OPTION_WALLET_ACCOUNT + "", PickerValues.PAYMENT_OPTION_CARD + "", PickerValues.PAYMENT_OPTION_CARD_ALT + "" }.Contains(fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] + "") && quote != null)
                            {
                                fromCAID = quote.FromCAID;
                                toCAID = quote.ToCAID;
                            }
                        }
                        else if (formId == PageTypes.WALLET_SEND_MONEY && fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + "" == PickerValues.PAYMENT_OPTION_CARD + "")
                        {
                            if (quote != null)
                            {
                                fromCAID = quote.FromCAID;
                                toCAID = quote.ToCAID;
                            }
                            else
                            {
                                // Send Money from wallet to own card - Get the card CAID                            
                                var toCAIDQueryT = await DapperHelper.QueryAsync<int>(
                                    " SELECT CA.CAID" +
                                    " FROM dbo.ContactTbl CT" +
                                    " INNER JOIN dbo.CustomerFile CF ON CT.CustomerID = CF.RecID" +
                                    " INNER JOIN dbo.CustomerRelationTbl CR ON CR.CustomerID = CF.RecID" +
                                    " INNER JOIN dbo.CustomerAccountTbl CA ON CA.CRID = CR.CRID AND CA.RefContactID = CT.ContactID AND CA.CustActId = 2" +
                                    " INNER JOIN dbo.ContactRelationTbl CRT ON CRT.ContactID = CT.ContactID AND CRT.CRID = CR.CRID" +
                                    $" WHERE CT.ContactId=@input AND CT.[Login] = 1 AND ct.Active = 1 AND CT.MobileLogin=1 ",
                                    new
                                    {
                                        input = currentUser.ContactID
                                    }
                                );
                                toCAID = toCAIDQueryT.FirstOrDefault();
                            }
                        }
                        else
                        {
                            if (quote != null)
                            {
                                fromCAID = quote.FromCAID;
                                toCAID = quote.ToCAID;
                            }
                            else
                            {
                                var senViewModelCPR = fields[General.InputPrefix + ControlTypes.PERSONAL_ID] + "";
                                var senViewModelMobile = fields[General.InputPrefix + 418] + "";

                                //Get column and value of contact
                                get_contactTbl_Columns(senViewModelCPR, senViewModelMobile, out column, out input);

                                var toCAIDQueryX = await DapperHelper.QueryAsync<int>(
                                    " SELECT CA.CAID" +
                                    " FROM dbo.ContactTbl CT" +
                                    " INNER JOIN dbo.CustomerAccountTbl CA ON CA.RefContactID = CT.ContactID AND CA.CustActId = 1" +
                                    $" WHERE CT.{column} = @input",
                                    new
                                    {
                                        input = input
                                    }
                                );
                                toCAID = toCAIDQueryX.FirstOrDefault();
                            }
                        }

                        break;

                }

                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Result of switch(Wallet/Card Actions) to be processed: processWallet: {processWallet} | From: {fromCAID} -> To: {toCAID} | CSID:{formTableId} | formId:{formId}");
                if (processWallet)
                {
                    if (fromCAID <= 0 || toCAID <= 0)
                    {
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] CAIDs are invalid.");
                        // _controller?.AddNotification(Resources.Services.NotMatchInOurRecordError, NotificationType.ERROR);
                        return new Tuple<bool, int>(false, formTableId);
                    }

                    //Update from caid & to caid
                    await DapperHelper.ExceuteAsync(
                        $"UPDATE ChooseListDetailsLogTbl SET CLIId='{fromCAID}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.CAID_FROM};"
                      + $"UPDATE ChooseListDetailsLogTbl SET CLIId='{toCAID}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.CAID_TO};"
                      + $"UPDATE ChooseListDetailsLogTbl SET CLIId='{currentUser.Username}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.FROM };"
                      );

                    // Create a general receipt for this payment
                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processing general receipt...");

                    //set type id of usp_CreateGeneralReceiptWalletTransfer
                    var typeId = 2; // wallet to wallet
                    var isSuccessResponse = true;
                    if ((formId == PageTypes.CARD_ADD_MONEY && fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] + "" == PickerValues.PAYMENT_OPTION_WALLET_ACCOUNT + "") ||
                        (formId == PageTypes.WALLET_SEND_MONEY && new List<string> { PickerValues.PAYMENT_OPTION_CARD + "", PickerValues.PAYMENT_OPTION_OTHER_CARDS + "" }.Contains(fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + ""))
                       )
                    {
                        typeId = 3; // wallet to card
                    }
                    else if ((formId == PageTypes.WALLET_ADD_MONEY && fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_CARD_ALT + "") ||
                             (formId == PageTypes.CARD_SEND_MONEY && new List<string> { PickerValues.PAYMENT_OPTION_MY_ACCOUNTS + "", PickerValues.PAYMENT_OPTION_OTHERS + "" }.Contains(fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + ""))
                            )
                    {
                        typeId = 4; // card to wallet
                    }
                    else if ((formId == PageTypes.CARD_ADD_MONEY && fields[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_CARD_ALT + "") ||
                             (formId == PageTypes.CARD_SEND_MONEY && new List<string> { PickerValues.PAYMENT_OPTION_CARD + "", PickerValues.PAYMENT_OPTION_OTHER_CARDS + "" }.Contains(fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + ""))
                            )
                    {
                        typeId = 5; // card to card
                    }
                    else if (formId == PageTypes.WALLET_SEND_MONEY && fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + "" == PickerValues.PAYMENT_OPTION_WITHIN_BH + "")
                    {
                        typeId = 8; // wallet to IBAN BH
                    }

                    var result = isSuccessResponse ? await DapperHelper.ExecuteScalarAsync<string>(
                        $" EXEC dbo.usp_CreateGeneralReceiptWalletTransfer @UserName = N'{currentUser.Username}', @FromCAID = {fromCAID}, @FromAmount = {amount}, @ToCAID = {toCAID}, @BatchNo = 0, @FormID = {formId}, @CSID = {formTableId}, @Type = {typeId}, @ToAmount = {targetAmount}, @ExchangeRate = {quote.ExchangeRate};"
                    ) : "-1";

                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Processed general receipt: {result}");




                    int ReceiptId = 0;
                    int.TryParse(result, out ReceiptId);
                    isSuccessResponse = isSuccessResponse && ReceiptId > 0;
                    #region UPDATE STATUS
                    //if (isSuccessResponse && (formId == PageTypes.CARD_ADD_MONEY /*|| (formId == PageTypes.WALLET_SEND_MONEY && fields[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + "" == PickerValues.PAYMENT_OPTION_CARD + "")*/))
                    //{
                    //    var cardNumber = formValues.ContainsKey(ControlTypes.CARD_NUMBER) ? formValues[ControlTypes.CARD_NUMBER] : "";
                    //    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Calling F13_PayMyCardPayment_Raw Card Number[{cardNumber}]");
                    //    var paymentResponse = WebServicesHelper.F13_PayMyCardPayment_Raw(cardNumber, amount + "", currentUser.Username, ReceiptId + "", fromCAID + "", "WC");
                    //    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Called F13_PayMyCardPayment_Raw Response: {paymentResponse}");
                    //    isSuccessResponse = paymentResponse == "000";
                    //    // TODO: Upon error, execute a wallet reversal.
                    //}

                    if (isSuccessResponse)
                    {
                        await DapperHelper.ExceuteAsync($"UPDATE ChooseListDetailsLogTbl SET CLIId='{StatusValues.APPROVED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS};"
                            + $"UPDATE ChooseListDetailsLogTbl SET CLIId='{result}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.RECEIPT_ID};"
                            );
                    }
                    else
                    {
                        await DapperHelper.ExceuteAsync($"UPDATE ChooseListDetailsLogTbl SET CLIId='{StatusValues.DECLINED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS}");
                        webServiceResult = -1;
                    }
                    #endregion

                    #region Notification Process
                    if (isSuccessResponse && (typeId == 3 || typeId == 4))
                    {
                        /*var cardCaid = formValues.ContainsKey(ControlTypes.CARD_NUMBER) ? formValues[ControlTypes.CARD_NUMBER] : "";
                        var cardDetails = DapperHelper.Query<KeyValuePair<string, decimal>>("SELECT CustAcCode AS [Key], ISNULL(StatementBalance, 0) AS [VALUE] FROM dbo.CustomerAccountTbl WHERE CAID = @CAID", new
                        {
                            CAID = cardCaid.Decrypt(General.CAIDEncryptPurpose)
                        }).FirstOrDefault();

                        var cardNumber = cardDetails.Key;
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Called F5_GetBalance Response to get the new card balance");
                        var customerBalance = cardDetails.Value;
                        cardNumber = cardNumber?.Substring(cardNumber.Length - 4, 4);
                        DapperHelper.ExecuteScalar<string>(
                               $" EXEC dbo.usp_SendReceiptGeneralProcessNotification_CardWallet @FromCAID = {fromCAID}, @Amount = {amount}, @ToCAID = {toCAID}, @ProductId = {formId}, @TypeId = {typeId},@CardNo=N'{cardNumber}',@CardBalance={customerBalance};"
                           );*/

                    }
                    #endregion

                    #region IBAN Money Transfer - B2B File
                    if (isSuccessResponse && typeId == 8)
                    {
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating a payment file...");
                        var fileName = (Config.GetStringValue("AUBBAHPaymentFileName") ?? "MALLAT_PAY_@DATE@_@TIME@").Replace("@DATE@", DateTime.Now.ToString("ddMMyyyy")).Replace("@TIME@", DateTime.Now.ToString("HHmmss"));
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating a payment file, fileName: {fileName}");
                        var filePath = Config.GetStringValue("AUBBAHPaymentFolder") + WalletApplication.BranchId + "\\Payments\\";
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating a payment file, filePath: {filePath}");
                        if (!Directory.Exists(filePath))
                        {
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Directory doesn't exist, creating a new one...");
                            Directory.CreateDirectory(filePath);
                            _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Directory doesn't exist, created a new one");
                        }


                        var iban = fields[General.InputPrefix + ControlTypes.BENEFICIARY_IBAN] + "";
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Beneficiary IBAN: {iban}");

                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Getting beneficiary payment data...");
                        var paymentData = DapperHelper.Query<MoneyTransferBeneficiary>($@"
                            SELECT Id AS Id, BN.BeneficiaryName AS Name, BK.BankName AS BankName, NULL AS BankAccount, BN.BeneficiarySWIFTCode AS BankSwiftCode, BN.BeneficiaryIBAN AS BankIBAN
	                        FROM dbo.Wallet_BeneficiaryTbl BN
	                        INNER JOIN dbo.Banks BK ON BK.BankID = BN.BeneficiaryBankId
                            WHERE BN.ContactId = @ContactId AND BN.BeneficiaryIBAN = @IBAN
                        ", new
                        {
                            ContactId = currentUser.ContactID,
                            IBAN = iban
                        }).FirstOrDefault();
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Got beneficiary payment data: {JsonConvert.SerializeObject(paymentData)}");

                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating the payment text file...");
                        FileInfo t = new FileInfo($"{filePath}{fileName}.txt");
                        StreamWriter Tex = t.CreateText();

                        // Header Line
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating the payment text file header...");
                        var receiptRef = ReceiptId + "";
                        var crNumber = Config.GetStringValue("AUBBAHPaymentCompanyCR") ?? "44562-3";
                        var debitAccountNumber = Config.GetStringValue("AUBBAHPaymentDebitAccountNumber") ?? "*************";
                        var debitDescription = $"PAYMENT RECEIPT #{receiptRef}";
                        var headerLine = $"S1,{crNumber},{debitAccountNumber},MXD,1,{debitDescription},{DateTime.Now.ToString("dd/MM/yyyy")},{(Config.GetStringValue("AUBBAHPaymentBatchReference") ?? "MALLAT_PAY_@REF@").Replace("@REF@", receiptRef)}";
                        Tex.WriteLine(headerLine);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Created the payment text file header: {headerLine}");

                        // Details Line
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating the payment text file details...");
                        var transactionType = paymentData.BankSwiftCode.Equals("AUBBBHBM", StringComparison.InvariantCultureIgnoreCase) ? "TRF" : "LCL";
                        var creditAmount = amount.FormatAmount(WalletApplication.NoOfDecimals, "", false, "").Replace(",", "");
                        var exchangeRate = "";
                        var creditDescription = $"WALLET TRANSFER RECEIPT #{receiptRef}";
                        var detailsLine = $"S2,{transactionType},{creditAmount},{WalletApplication.Currency},{exchangeRate},,,,{paymentData.BankIBAN.ToUpper()},{receiptRef},,,{creditDescription},{creditDescription},,,,{paymentData.Name},,,{paymentData.BankName},,,,{paymentData.BankSwiftCode},,,,,,,,,,";
                        Tex.WriteLine(detailsLine);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Created the payment text file details: {detailsLine}");

                        // Trailer Line
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Creating the payment text file trailer...");
                        var trailerLine = $"S3,1";
                        Tex.WriteLine(trailerLine);
                        _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Created the payment text file trailer: {trailerLine}");

                        Tex.Flush();
                        Tex.Close();

                        if (Config.GetStringValue("AUBBAHPaymentFolderBatch") != null)
                        {
                            try
                            {
                                var f = Config.GetStringValue("AUBBAHPaymentFolderBatch").ToString().Replace("@branch@", WalletApplication.BranchId);
                                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Invoking the batch file: {f}");
                                if (System.IO.File.Exists(f))
                                {
                                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Starting the process...");
                                    Process a = new Process();
                                    a.StartInfo.FileName = f;
                                    a.StartInfo.CreateNoWindow = true;
                                    a.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                                    var isStarted = a.Start();
                                    a.Dispose();
                                    _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Started the process: {isStarted}");
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}/{currentUser.Username}] Failed to run the batch file.", ex);
                            }
                        }
                    }
                    #endregion
                }

                // Commit the changes if the email and/or sms were processed successfully
                var isSuccess = emailSmsResult >= 0 && webServiceResult >= 0;

                /* if (formId == PageTypes.STOP_CARD_REQUEST && isSuccess)
                {
                    // Get the card's last 4 digits
                    var cardNumber = formValues.ContainsKey(ControlTypes.CARD_NUMBER) ? formValues[ControlTypes.CARD_NUMBER] : "";
                    cardNumber = WebServicesHelper.GetCardNumber(cardNumber).FormatCardNumber(true, true, "0:0000", 0, 6);

                    DapperHelper.Exceute(
                        " INSERT INTO dbo.MobileNotificationProcessingQueue (CustomerId, MessageContent, MessageType, DateCreated, DateModified, Processed, NoOfTrails, Cont_CPR)" +
                        $" VALUES (@CustomerId, @MessageContent, @MessageType, @DateCreated, @DateModified, @Processed, @NoOfTrails, @Cont_CPR)",
                        new
                        {
                            CustomerId = customerId,
                            MessageContent = string.Format(Resources.Services.StopCardSuccessWithNumber, cardNumber),
                            MessageType = "A",
                            DateCreated = DateTime.Now,
                            DateModified = DateTime.Now,
                            Processed = false,
                            NoOfTrails = 0,
                            Cont_CPR = currentUser?.Username
                        }
                    );
                } */

                // Reset the form data (PCI Requirement)
                fields = null; formValues = null; GC.Collect();

                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/{formTableId}] Is successful request: {isSuccess}.");
                return new Tuple<bool, int>(isSuccess, formTableId);
            }
            catch (Exception ex)
            {
                _logger.LogError($"[FormRepository/InsertRequest/{formId}/{customerId}/-1]", ex);
            }

            // Something went wrong
            formTableId = -1;

            // Remove any leftover files
            if (filesList.Any())
            {
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/-1] Removing uploaded files...");
                foreach (var file in filesList.Where(File.Exists))
                {
                    File.Delete(file);
                }
                _logger.LogInformation($"[FormRepository/InsertRequest/{formId}/{customerId}/-1] Removed uploaded files");
            }

            return new Tuple<bool, int>(false, formTableId);
        }

        /*
         * Return the field value depending on its type. */
        private string GetFieldValue(int fieldType, string fieldValue, bool isPublished, AppUser currentUser)
        {
            string returnValue;
            bool isEmpty = string.IsNullOrWhiteSpace(fieldValue);
            switch (fieldType)
            {
                case ControlTypes.APPLICATION_DATE:
                case ControlTypes.TRANSACTION_DATE:
                case ControlTypes.VALUE_DATE:
                    returnValue = !isEmpty && isPublished ? fieldValue : DateTime.Now + "";
                    break;
                case ControlTypes.APPLICANT_NAME:
                    returnValue = !isEmpty && isPublished ? fieldValue : currentUser?.Name ?? "";
                    break;
                case ControlTypes.CPR:
                    returnValue = !isEmpty && isPublished ? fieldValue : currentUser?.Username ?? "";
                    break;
                case ControlTypes.STATUS:
                    returnValue = !isEmpty && isPublished ? fieldValue : StatusValues.DEFAULT + "";
                    break;
                default:
                    returnValue = fieldValue;
                    break;
            }

            return returnValue;
        }

        public async Task<IEnumerable<EmailValues>> GetFormFieldAndValuesById(int FormID, int pageID, bool isLogRequest = false)
        {
            var tableName = isLogRequest ? "ChooseListDetailsLogTbl" : "ChooseListDetailsTbl";
            var queryAsync =
                await DapperHelper.QueryAsync<EmailValues>(
                    $" SELECT pst.ProductNameE AS FormName,{LanguageHelper.GetSelectStmt("clc.CLCatNameE", "Category")}, clc.CLCatId AS CategoryId, clc.ControlType AS ControlType, clc.DataType AS DataType," +
                    $" CASE WHEN clc.ControlType = 'advpick' THEN ({LanguageHelper.GetSelectStmt("cli.CLNameE", "", 1)}) ELSE cld.CLIId END AS ItemName" +
                   $" FROM dbo.{tableName} cld" +
                    " INNER JOIN dbo.ChooseListCategoryTbl clc ON cld.CLCatId = clc.CLCatId" +
                    " LEFT JOIN dbo.ChooseListItemTbl cli ON CONVERT(NVARCHAR, cli.CLIId) = cld.CLIId" +
                    " LEFT JOIN dbo.ChooseListPageTbl clp ON clp.pageId = cld.pageId AND clp.CLCatId = cld.CLCatId" +
                    " LEFT JOIN dbo.ProductServiceTbl pst ON pst.ProductID = clp.iType" +
                      LanguageHelper.GetLeftJoin("ChooseListCategoryTbl", "CLCatNameE", "clc.CLCatId") +
                      LanguageHelper.GetLeftJoin("ChooseListItemTbl", "CLNameE", "cli.CLIId", 1) +
                    " WHERE clp.iType = @iType AND cld.TableId = @TableId AND cld.pageId = 9049" +
                    " ORDER BY clp.SortCode, cld.SortCode",
                    new
                    {
                        iType = pageID,
                        TableId = FormID
                    }
                );
            var query = queryAsync.AsEnumerable();

            return query;
        }

        public async Task<bool> UpdateRequest(int formTableId, Dictionary<int, string> fields, bool isLogRequest = false)
        {
            // Make sure that we have valid data
            if (formTableId <= 0 || fields == null || fields.Count <= 0)
            {
                return false;
            }

            // Get the form fields from the database
            var detailsTbl = $"ChooseListDetails{(isLogRequest ? "Log" : "")}Tbl";
            var formDataQuery = await DapperHelper.QueryAsync<ChooseListDetailsCategoryTbl>(
                " SELECT cld.*, clc.DataType" +
               $" FROM dbo.{detailsTbl} cld" +
                " INNER JOIN dbo.ChooseListCategoryTbl clc ON clc.CLCatId = cld.CLCatId" +
                " WHERE cld.TableId = @TableId",
                new
                {
                    TableId = formTableId
                }
            );
            var formData = formDataQuery.ToList();

            // Go through the form fields and update the required fields
            var updateSql = "";
            var updateParameters = new ExpandoObject() as IDictionary<string, object>;
            foreach (var field in formData)
            {
                if (!fields.ContainsKey(field.CLCatId ?? -1)) continue;

                // Get the field's value
                var value = fields[field.CLCatId ?? -1];

                // Check if we have to sum or subtract the field's value with the given value
                if (value.StartsWith("+=") || value.StartsWith("-="))
                {
                    // Get rid of any non numeric characters except the period
                    var currentNumber = Regex.Replace(field.CLIId, "[^0-9.]", "");

                    // Initialise the two decimals
                    var decimal1 = 0m;
                    var decimal2 = 0m;
                    decimal.TryParse(currentNumber, out decimal1);
                    decimal.TryParse(value.Substring(2), out decimal2);

                    // Calculate the new value
                    field.CLIId = (value.StartsWith("-=") ? decimal1 - decimal2 : decimal1 + decimal2) + "";

                    // If the field is of type money, format it as such
                    if (field.DataType.ToUpper() == "MONEY")
                    {
                        field.CLIId = field.CLIId.FormatAmount();
                    }
                }
                else
                {
                    // Otherwise, set the value normally
                    field.CLIId = value;
                }

                updateSql += $" UPDATE dbo.{detailsTbl} SET CLIId = @CLIId{field.CLDtlId} WHERE CLDtlId = {field.CLDtlId};";
                updateParameters.Add($"@CLIId{field.CLDtlId}", field.CLIId);
            }

            if (!string.IsNullOrWhiteSpace(updateSql))
            {
                DapperHelper.Exceute(updateSql, updateParameters);
            }

            // Reset the form data (PCI Requirement)
            fields = null; formData = null; formTableId = 0; GC.Collect();

            return true;
        }

        public async Task<Tuple<int, string>> ProcessWebService(int formHeaderId, int pageId, int contactId, AppUser currentUser = null, Dictionary<int, string> submitFields = null, bool isLogRequest = false)
        {
            _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}/{pageId}/{contactId}/{isLogRequest}] Entered.");

            // Keep track of the result
            var result = 0;

            // Keep track of the submitted fields
            submitFields = submitFields ?? new Dictionary<int, string>();

            // Initialize the output
            var output = "";

            try
            {
                // Get the webservice fields categories
                var webServiceFieldCategories = new List<string>
                {
                    ControlDataTypes.WebServiceFunction,
                    ControlDataTypes.WebServiceParams
                };

                // Get the form's web service fields
                _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] Getting web service fields.");
                var tableName = isLogRequest ? "ChooseListDetailsLogTbl" : "ChooseListDetailsTbl";
                var webServiceFieldsQuery =
                    await DapperHelper.QueryAsync<RequestFormField>(
                        " SELECT clc.DataType AS DataType, cld.CLIId AS Value" +
                       $" FROM dbo.{tableName} cld" +
                        " INNER JOIN dbo.ChooseListCategoryTbl clc ON clc.CLCatId = cld.CLCatId" +
                       $" WHERE cld.TableId = @TableId AND clc.DataType IN ({string.Join(",", webServiceFieldCategories.Select(s => $"'{s}'"))})",
                        new
                        {
                            TableId = formHeaderId
                        }
                    );
                var webServiceFields = webServiceFieldsQuery.ToList();
                _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] Got web service fields.");

                // Build the webservice's fields details
                var webServiceFunction = webServiceFields.FirstOrDefault(r => r.DataType == ControlDataTypes.WebServiceFunction)?.Value;
                var webServiceParams = webServiceFields.FirstOrDefault(r => r.DataType == ControlDataTypes.WebServiceParams)?.Value;
                _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] webServiceFunction: {webServiceFunction}");
                _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] webServiceParams: {webServiceParams}");

                // No webservices to process
                if (string.IsNullOrWhiteSpace(webServiceFunction))
                {
                    _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] No web service found!");
                    return new Tuple<int, string>(result, output);
                }

                // Get the form fields
                _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] Getting form fields...");
                var fieldsAsync = await GetFormFieldAndValuesById(formHeaderId, pageId, isLogRequest);
                var fields = fieldsAsync.ToList();
                _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] Got form fields: {fields.Count}");

                // Add the user details to the fields list
                if (currentUser != null)
                {
                    _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] Found user details.");

                    // Get the card's last digits
                    var cardLastDigits = submitFields.ContainsKey(ControlTypes.CARD_NUMBER)
                        ? submitFields[ControlTypes.CARD_NUMBER]?.GetStringPortion(0, 4)
                        : "";
                    _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] cardLastDigits: {cardLastDigits}");

                    // Get the cardholder's embossing name
                    var cardHolderName = submitFields.ContainsKey(ControlTypes.CARD_HOLDER_NAME)
                        ? submitFields[ControlTypes.CARD_HOLDER_NAME]
                        : "";
                    _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] cardHolderName: {cardHolderName}");

                    fields.AddRange(new List<EmailValues>
                    {
                        new EmailValues {Category = "USER_NAME", ItemName = currentUser.Name},
                        new EmailValues {Category = "USER_CPR", ItemName = currentUser.Username},
                        new EmailValues {Category = "USER_MOBILE_PHONE", ItemName = currentUser.Mobile},
                        new EmailValues {Category = "USER_HOME_PHONE", ItemName = currentUser.HomePhone},
                        new EmailValues {Category = "USER_EMAIL", ItemName = currentUser.Email},
                        new EmailValues {Category = "CARD_LAST_4", ItemName = cardLastDigits},
                        new EmailValues {Category = "CARD_EMBOSS", ItemName = cardHolderName}
                    });
                }

                // Replace the parameters placeholders
                _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] Replacing parameters placeholders...");
                foreach (var item in fields)
                {
                    webServiceParams = webServiceParams?.Replace("@" + item.Category + "@", item.ItemName);
                }
                _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] Replaced parameters placeholders: {webServiceParams}");

                // Get the functions and parameters list
                var functions = webServiceFunction.Split('|');
                var parameters = webServiceParams?.Split('|');
                var functionsLength = functions.Length;
                var paramsLength = parameters?.Length ?? 0;

                // Run the webservices ! RUN WEB SERIVCE NOT DONE
                _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] functionsLength: {functionsLength}");
                var successCount = 0;
                for (var i = 0; i < functionsLength; i++)
                {
                    var functionParams = paramsLength > i ? parameters?[i] : null;
                    _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] function{i}: {functionParams}, calling web service...");
                    // successCount += WebServicesHelper.RunWebService(functions[i], functionParams, out output) ? 1 : 0;
                    _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] function{i}: {successCount}, called web service.");
                }

                // If all webservices were processed successfully, update the form's status
                if (successCount >= functionsLength)
                {
                    // Set the status to success
                    var fielViewModelUpdate = new Dictionary<int, string>
                    {
                        {ControlTypes.STATUS, StatusValues.APPROVED + ""}
                    };

                    // Update the form
                    _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] updating form status field...");
                    var updated = await UpdateRequest(formHeaderId, fielViewModelUpdate, isLogRequest);
                    _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] updated form status field: {updated}");
                    result = updated ? successCount : -1;
                }
                else
                {
                    // Some of the web services were not processed successfully, indicate an error
                    _logger.LogInformation($"[FormRepository/ProcessWebService/{formHeaderId}] Not all web services ran successfully!");
                    result = -1;
                }
            }
            catch (Exception ex)
            {
                // Something went wrong
                result = -1;
                _logger.LogError($"[FormRepository/ProcessWebService/{formHeaderId}/{pageId}] Error", ex);
            }

            // Return the final result
            return new Tuple<int, string>(result, output);
        }

        public async Task<int> ProcessEmailSms(int formHeaderId, int pageId, int contactId, AppUser currentUser = null, Dictionary<int, string> submitFields = null, bool isLogRequest = false)
        {
            // Keep track of the result
            var result = 0;

            // Keep track of the submitted fields
            submitFields = submitFields ?? new Dictionary<int, string>();

            try
            {
                // Get the form fields
                var fieldsAsync = await GetFormFieldAndValuesById(formHeaderId, pageId, isLogRequest);
                var fields = fieldsAsync.ToList();

                // Get the email fields categories
                var emailFieldCategories = new List<int>
                {
                    ControlTypes.EMAIL_SUBJECT_ID,
                    ControlTypes.EMAIL_SUBJECT_ID,
                    ControlTypes.EMAIL_BODY_ID,
                    ControlTypes.EMAIL_RECIPIENT_ID,
                    ControlTypes.EMAIL_CC_ID,
                    ControlTypes.EMAIL_ATTACHMENT_ID
                };

                // Get the form's email fields
                var tableName = isLogRequest ? "ChooseListDetailsLogTbl" : "ChooseListDetailsTbl";
                var emailFieldsQuery =
                    await DapperHelper.QueryAsync<RequestFormField>(
                    " SELECT cld.ClCatId AS ID, cld.CLIId AS Value" +
                   $" FROM dbo.{tableName} cld" +
                        " WHERE cld.TableId = @TableId" +
                   $" AND cld.CLCatId IN({string.Join(",", emailFieldCategories)})",
                        new
                        {
                            TableId = formHeaderId
                        }
                );
                var emailFields = emailFieldsQuery.ToList();

                // Build the email's fields details
                var emailSubject = emailFields.FirstOrDefault(r => r.ID == ControlTypes.EMAIL_SUBJECT_ID)?.Value;
                var emailBody = emailFields.FirstOrDefault(r => r.ID == ControlTypes.EMAIL_BODY_ID)?.Value;
                var emailRecipient = emailFields.FirstOrDefault(r => r.ID == ControlTypes.EMAIL_RECIPIENT_ID)?.Value;
                var emailCc = emailFields.FirstOrDefault(r => r.ID == ControlTypes.EMAIL_CC_ID)?.Value;
                var emailSender = WalletApplication.GetEnvVariable("DefaultEmailSender", "<EMAIL>");

                // Get the sms fields categories
                var smsFieldCategories = new List<int>
                {
                    ControlTypes.SMS_RECIPIENT,
                    ControlTypes.SMS_SENDER,
                    ControlTypes.SMS_BODY
                };

                // Get the form's sms fields
                var smsFieldsQuery =
                    await DapperHelper.QueryAsync<RequestFormField>(
                    " SELECT cld.ClCatId AS ID, cld.CLIId AS Value" +
                   $" FROM dbo.{tableName} cld" +
                        " WHERE cld.TableId = @TableId" +
                   $" AND cld.CLCatId IN({string.Join(",", smsFieldCategories)})",
                        new
                        {
                            TableId = formHeaderId
                        }
                );
                var smsFields = smsFieldsQuery.ToList();

                // Build the SMS fields details
                var smsRecipient = smsFields.FirstOrDefault(r => r.ID == ControlTypes.SMS_RECIPIENT)?.Value;
                var smsBody = smsFields.FirstOrDefault(r => r.ID == ControlTypes.SMS_BODY)?.Value;
                var smsSender = WalletApplication.GetEnvVariable("DefaultSmsSenderName", "Optimum");

                if (currentUser != null)
                {
                    // Get the card's last digits
                    var cardLastDigits = submitFields.ContainsKey(ControlTypes.CARD_NUMBER)
                        ? submitFields[ControlTypes.CARD_NUMBER]?.GetStringPortion(0, 4)
                        : "";

                    // Get the cardholder's embossing name
                    var cardHolderName = submitFields.ContainsKey(ControlTypes.CARD_HOLDER_NAME)
                        ? submitFields[ControlTypes.CARD_HOLDER_NAME]
                        : "";

                    // Add the user details to the fields list
                    fields.AddRange(new List<EmailValues>
                    {
                        new EmailValues {Category = "USER_NAME", ItemName = currentUser.Name},
                        new EmailValues {Category = "USER_CPR", ItemName = currentUser.Username},
                        new EmailValues {Category = "USER_MOBILE_PHONE", ItemName = currentUser.Mobile},
                        new EmailValues {Category = "USER_HOME_PHONE", ItemName = currentUser.HomePhone},
                        new EmailValues {Category = "USER_EMAIL", ItemName = currentUser.Email},
                        new EmailValues {Category = "CARD_LAST_4", ItemName = cardLastDigits},
                        new EmailValues {Category = "CARD_EMBOSS", ItemName = cardHolderName}
                    });
                }

                foreach (var item in fields)
                {
                    emailBody = emailBody?.Replace("@" + item.Category + "@", item.ItemName);
                    emailSubject = emailSubject?.Replace("@" + item.Category + "@", item.ItemName);
                    smsBody = smsBody?.Replace("@" + item.Category + "@", item.ItemName);
                }

                // Check if we need to send an email message
                if (!string.IsNullOrWhiteSpace(emailSubject)
                    && !string.IsNullOrWhiteSpace(emailBody)
                    && !string.IsNullOrWhiteSpace(emailRecipient)
                    && !string.IsNullOrWhiteSpace(emailSender))
                {
                    var emailContent = await DapperHelper.ExecuteScalarAsync<int>(
                        " INSERT INTO dbo.EmailContents (MailBody, MailSubject, CC_EmailID)" +
                        " VALUES(@MailBody, @MailSubject, @CC_EmailID);" +
                        " SELECT CONVERT(INT, SCOPE_IDENTITY());",
                        new
                        {
                            MailBody = emailBody,
                            MailSubject = emailSubject,
                            CC_EmailID = emailCc
                        },
                        "Documents"
                    );

                    // Add the email attachments
                    var appFolder = WalletApplication.AppFolder;
                    var formsFolder = Config.GetStringValue("FormsFolder");

                    // Get the attachment files
                    var files = fields.Where(r => r.DataType == ControlDataTypes.EmailAttachment).ToList();

                    // Process the files
                    if (files.Any())
                    {
                        var count = 0;
                        var insertSql = "";
                        var insertParameters = new ExpandoObject() as IDictionary<string, object>;
                        insertParameters.Add("@ContentID", emailContent);

                        foreach (var file in files)
                        {
                            // Get the file path
                            var path = Path.Combine(appFolder, formsFolder, file.ItemName);

                            // Get the file extension
                            var extension = Path.GetExtension(path)?.ToLower().Replace(".", "");

                            // Read the file into a blob
                            var fileBlob = File.ReadAllBytes(path);

                            // Build the attachment details
                            insertSql +=
                                 " INSERT INTO dbo.EmailDocuments (DocumentName, ContentType, DocumentEXT, DocumentBLOB, ContentID)" +
                                $" VALUES (@DocumentName{count}, @ContentType{count}, @DocumentEXT{count}, @DocumentBLOB{count}, @ContentID);";
                            insertParameters.Add($"@DocumentName{count}", file.ItemName);
                            insertParameters.Add($"@ContentType{count}", GeneralHelper.GetMimeType(path));
                            insertParameters.Add($"@DocumentEXT{count}", extension);
                            insertParameters.Add($"@DocumentBLOB{count++}", fileBlob);
                        }

                        // Persist the changes to the database
                        await DapperHelper.ExceuteAsync(insertSql, insertParameters, "Documents");
                    }


                    await DapperHelper.ExceuteAsync(
                        " INSERT INTO dbo.EmailReportDetails (ERTID, ProcessCode, EventDate, DataSQL, EntityNameE, EntityNameA, FileGenerated, FileError, From_EmailID, To_EmailID, AttachmentFile_Path, EmailSubject, EmailText, EmailError, ContentID, CC_EmailID)" +
                        " VALUES (@ERTID, @ProcessCode, @EventDate, @DataSQL, @EntityNameE, @EntityNameA, @FileGenerated, @FileError, @From_EmailID, @To_EmailID, @AttachmentFile_Path, @EmailSubject, @EmailText, @EmailError, @ContentID, @CC_EmailID)",
                        new
                        {
                            ERTID = 0,
                            ProcessCode = contactId + "",
                            EventDate = DateTime.Now,
                            DataSQL = "Set DateFormat dmy;",
                            EntityNameE = "",
                            EntityNameA = "",
                            FileGenerated = 1,
                            FileError = "",
                            From_EmailID = emailSender,
                            To_EmailID = emailRecipient,
                            AttachmentFile_Path = "",
                            EmailSubject = emailSubject,
                            EmailText = emailBody,
                            EmailError = "",
                            ContentID = emailContent,
                            CC_EmailID = emailCc
                        }
                    );

                    // Reset the form data (PCI Requirement)
                    fields = null; formHeaderId = 0; GC.Collect();

                    // Indicates that the email was processed successfully
                    result += 1;
                }

                // Check if we need to send an sms message
                if (!string.IsNullOrWhiteSpace(smsRecipient)
                    && !string.IsNullOrWhiteSpace(smsSender)
                    && !string.IsNullOrWhiteSpace(smsBody))
                {
                    await DapperHelper.ExceuteAsync(
                        " INSERT INTO dbo.SMSMessageTbl (ERTID, ProcessCode, From_MobileNo, To_MobileNo, SMSMessage, DateModified, UserModified, SenderName, BankCode, Unicode, Alert)" +
                        " VALUES (@ERTID, @ProcessCode, @From_MobileNo, @To_MobileNo, @SMSMessage, @DateModified, @UserModified, @SenderName, @BankCode, @Unicode, @Alert)",
                        new
                        {
                            ERTID = 0,
                            ProcessCode = contactId + "",
                            From_MobileNo = "",
                            To_MobileNo = smsRecipient,
                            SMSMessage = smsBody,
                            DateModified = DateTime.Now,
                            UserModified = "amthal",
                            SenderName = smsSender,
                            BankCode = "",
                            Unicode = false,
                            Alert = false
                        }
                    );

                    // Indicates that the sms was processed successfully
                    result += 1;
                }
            }
            catch (Exception ex)
            {
                // Something went wrong
                result = -1;
            }

            // Return the final result
            return result;
        }

        #region Define columns
        //====================================== Define columns ==============================================================================
        void get_contactTbl_Columns(string cpr, string mobile, out string column, out string input)
        {
            column = "";
            input = "";

            if (!string.IsNullOrWhiteSpace(cpr))
            {
                column = "Cont_CPR";
                input = cpr;
            }
            else if (!string.IsNullOrWhiteSpace(mobile))
            {
                column = "ContactMobile";
                input = mobile;
            }
        }
        //======================================End Define columns ============================================================================
        #endregion

        public ResponseViewModel ValidateWalletMaxAmount(decimal amount, int productId, string customMessage = "")
        {
            _logger.LogInformation($"FormRepository/ValidateAmount] Amount: {amount}");
            ResponseViewModel Response = new ResponseViewModel() { Status = NotificationType.SUCCESS };

            var sMaxAmount = WalletApplication.GetEnvVariable("MaxAmountWalletTransferPerTransaction", "500");
            decimal MaxAmount = 0;
            decimal.TryParse(sMaxAmount, out MaxAmount);

            _logger.LogInformation($"FormRepository/ValidateAmount] Amount: {amount} | MaxAmount:{MaxAmount}");

            if (amount > MaxAmount)
            {
                _logger.LogError($"FormRepository/ValidateAmount] Amount enterd ({amount}) is greater than maximum amount ({MaxAmount})");

                Response.Status = NotificationType.ERROR;

                if (!string.IsNullOrWhiteSpace(customMessage))
                {
                    Response.Message = string.Format(customMessage, MaxAmount.FormatAmount());
                }
                else
                {
                    if (productId == PageTypes.WALLET_SEND_MONEY || productId == PageTypes.WALLET_SCAN_AND_PAY)
                    {
                        Response.Message = string.Format("TransferMoreThanError", MaxAmount.FormatAmount());
                    }
                    else if (productId == PageTypes.WALLET_REQUEST_MONEY)
                    {
                        Response.Message = string.Format("RequestMoreThanError", MaxAmount.FormatAmount());
                    }
                }
            }
            return Response;
        }

        public async Task<ResponseViewModel> ValidateWalletMaxTotalAmountTransfer(decimal amount, int CAID, int contactId)
        {
            _logger.LogInformation($"FormRepository/ValidateWalletTotalAmountTransfer] Amount: {amount}");
            ResponseViewModel Response = new ResponseViewModel() { Status = NotificationType.SUCCESS };

            var sTotalAmount = WalletApplication.GetEnvVariable("TotalAmountWalletTransferPerDay", "1000");
            var sTotalCount = WalletApplication.GetEnvVariable("TotalCountWalletTransferPerDay", "10");

            decimal MaxTotalAmount = 0;
            int totalCount;
            int.TryParse(sTotalCount, out totalCount);
            decimal.TryParse(sTotalAmount, out MaxTotalAmount);

            _logger.LogInformation($"FormRepository/ValidateWalletTotalAmountTransfer] Amount: {amount} | MaxAmount:{MaxTotalAmount}");

            var transactionCountAndAmountQuery = await DapperHelper.QueryAsync<transactionCountAndAmount>(
                                            "dbo.usp_GetTransactionCountAndAmount",
                                            new
                                            {
                                                CAID = CAID,
                                                ContactID = contactId,
                                                ValuDate = DateTime.Now.ToString("dd/MM/yyyy"),
                                                InputAmount = amount
                                            }
                                            , "Service",
                                            commandType: CommandType.StoredProcedure);
            var transactionCountAndAmount = transactionCountAndAmountQuery.FirstOrDefault();

            _logger.LogInformation($"FormRepository/ValidateWalletTotalAmountTransfer] usp_GetTransactionCountAndAmount result| TotalCount: {transactionCountAndAmount.TotalCount} | TotalAmount:{transactionCountAndAmount.TotalAmount}");

            if (transactionCountAndAmount.TotalCount > totalCount)
            {
                _logger.LogError($"FormRepository/ValidateWalletTotalAmountTransfer]  total transactions count today(usp_GetTransactionCountAndAmount):{transactionCountAndAmount.TotalCount} > total count:{transactionCountAndAmount.TotalCount}");
                Response.Status = NotificationType.ERROR;
                Response.Message = string.Format("LimitReachError", totalCount);
            }


            if (transactionCountAndAmount.TotalAmount > MaxTotalAmount)
            {
                _logger.LogError($"FormRepository/ValidateWalletTotalAmountTransfer] total amount transfer today include current amount({amount}) is greater than maximum amount allowed transfer perday ({MaxTotalAmount})");

                Response.Status = NotificationType.ERROR;

                var remainingAmount = MaxTotalAmount - (transactionCountAndAmount.TotalAmount - amount);
                if (remainingAmount <= 0)
                    Response.Message = String.Format("ReachMaximumDailyTransferLimitError", MaxTotalAmount.FormatAmount());
                else
                    Response.Message = String.Format("ExceedMaximumDailyTransferLimitError", MaxTotalAmount.FormatAmount(), remainingAmount.FormatAmount());
            }

            return Response;

        }

        public async Task<ResponseViewModel> CreateGeneralReceiptWalletTransfer(int fromCAID, int toCAID, decimal amount, AppUser currentUser, int type, bool isSourceAmount = true)
        {
            string CPR = currentUser.Username;
            ResponseViewModel Response = new ResponseViewModel() { };
            try
            {
                //Check amount
                if (amount <= 0)
                {
                    _logger.LogInformation($"FormRepository/CreateGeneralReceiptWalletTransfer] wallet transfer amount is invalid.");
                    Response.Message = "InvalidAmountError";
                    Response.Status = NotificationType.ERROR;
                    return Response;
                }

                //Validate Maxuimum Amount 
                var customMessage = "";
                if (type == 5 /*Smart Money*/)
                {
                    customMessage = "You cannot transfer more than {0} using your wallet. Please use another mode of payment.";
                }
                var validateMaxAmountResponse = ValidateWalletMaxAmount(amount, PageTypes.WALLET_SEND_MONEY, customMessage);
                if (validateMaxAmountResponse.Status.ToLower() == NotificationType.ERROR.ToLower())
                {
                    return validateMaxAmountResponse;
                }

                //Validate total amount transfer it as today
                var validateMaxAmountPerDayResponse = await ValidateWalletMaxTotalAmountTransfer(amount, fromCAID, currentUser.ContactID);
                if (validateMaxAmountPerDayResponse.Status.ToLower() == NotificationType.ERROR.ToLower())
                {
                    return validateMaxAmountPerDayResponse;
                }


                //Get Customer Wallet Balance 
                var customerBalance = await _walletRepository.GetCustomerWalletBalance(fromCAID, currentUser.ContactID);
                if (amount > customerBalance)
                {
                    _logger.LogError($"FormRepository/CreateGeneralReceiptWalletTransfer]/CustomerBalance:{customerBalance}/ insufficient wallet balance.");
                    Response.Message = "InsufficientWalletBalance";
                    Response.Status = NotificationType.ERROR;
                    return Response;
                }

                //Check CAIDs
                if (fromCAID <= 0 || toCAID <= 0)
                {
                    _logger.LogError($"FormRepository/CreateGeneralReceiptWalletTransfer]/fromCAID:{fromCAID}/toCAID:{toCAID}/ wrong data with CAIDs..");
                    //_controller?.AddNotification("The given phone number is not registered!", NotificationType.ERROR);
                    Response.Message = "ErrorOccurred";
                    Response.Status = NotificationType.ERROR;
                    return Response;
                }

                var toContactDetailsQuery = await DapperHelper.QueryAsync<ContactTbl>(
                " SELECT CT.*" +
                " FROM dbo.ContactTbl CT" +
                " INNER JOIN dbo.CustomerAccountTbl CA ON CA.RefContactID = CT.ContactID " +
                " WHERE CA.CAID=@CAID ",
                new
                {
                    CAID = toCAID
                });

                var toContactDetails = toContactDetailsQuery.FirstOrDefault() ?? new ContactTbl();

                //Check to mobile is Active & is registred
                if (type != 5 && ((!toContactDetails.Active) || (!toContactDetails.MobileLogin ?? true)))
                {
                    _logger.LogError($"FormRepository/CreateGeneralReceiptWalletTransfer]/Active:{toContactDetails.Active}/MobileLogin:{toContactDetails.MobileLogin}/ receiver is not active");
                    Response.Message = "InactiveUserError";
                    Response.Status = NotificationType.ERROR;
                    return Response;
                }

                // Get Mobile No. of the reciver which is BFC
                var toMobile = toContactDetails.ContactMobile;

                //Fill fields of form
                Dictionary<int, string> fields = new Dictionary<int, string>();
                //fields[44] = "";    //	Transaction Date
                //fields[406] = "";   //	Value Date
                fields[407] = currentUser.Username;   //	From
                fields[409] = fromCAID + "";   //	CAID From
                fields[410] = toCAID + "";   //	CAID To
                fields[43] = amount + "";    //	Amount
                fields[412] = "Create General Receipt Wallet Transfer";   //	Description
                //fields[413] = "";   //	Mail Merge SMS ID
                //fields[414] = "";   //	Push Message ID
                //fields[415] = "";   //	Email ID
                //fields[416] = "";   //	Receipt ID
                fields[418] = toMobile;  //	Send To
                fields[492] = toContactDetails.Cont_CPR;  //	CPR

                var formId = PageTypes.WALLET_SEND_MONEY;
                // Insert the request to the database and submit an email report
                var formTableId = -1;
                var inserted = await InsertLogForm(formId, currentUser, fields, formTableId);
                formTableId = inserted.Item2;

                if (inserted.Item1)
                {
                    _logger.LogInformation($"FormRepository/CreateGeneralReceiptWalletTransfer]/{CPR}/{amount}] Wallet transfer to be processed: From CAID: {fromCAID} -> To CAID: {toCAID} | amount:{amount} | FormID:{formId} | CSID:{formTableId} ");

                    var exResultQuery = await DapperHelper.QueryAsync<WalletExchangeQuote>(
                             "usp_CalculateWalletExchangeRate",
                             new
                             {
                                 DbName = DapperHelper.DbName,
                                 Date = DateTime.Now.ToString("dd/MM/yyyy"),
                                 FromCAID = fromCAID,
                                 ToCAID = toCAID,
                                 Amount = amount,
                                 IsSourceAmount = isSourceAmount
                             },
                             "Ledger",
                             System.Data.CommandType.StoredProcedure
                         );
                    var exResult = exResultQuery.FirstOrDefault();

                    var sourceAmount = (isSourceAmount ? amount : exResult.CalculatedAmount);
                    var targetAmount = (isSourceAmount ? exResult.CalculatedAmount : amount);

                    var result = await DapperHelper.ExecuteScalarAsync<string>(
                        $" EXEC dbo.usp_CreateGeneralReceiptWalletTransfer @UserName = N'{CPR}', @FromCAID = {fromCAID}, @FromAmount = {sourceAmount},@ToAmount={targetAmount}, @ToCAID = {toCAID}, @BatchNo = 0, @FormID = {formId}, @CSID = {formTableId},@Type={type}, @ExchangeRate = {exResult.ExchangeRate};");

                    _logger.LogInformation($"FormRepository/CreateGeneralReceiptWalletTransfer]/{CPR}/{amount}] Processed general receipt: {result}");

                    int ReceiptId = 0;
                    int.TryParse(result, out ReceiptId);
                    //Executed successfully
                    if (ReceiptId > 0)
                    {
                        await DapperHelper.ExceuteAsync($"UPDATE ChooseListDetailsLogTbl SET CLIId='{StatusValues.APPROVED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS};"
                            + $"UPDATE ChooseListDetailsLogTbl SET CLIId='{result}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.RECEIPT_ID};"
                            );
                    }
                    else
                    {
                        await DapperHelper.ExceuteAsync($"UPDATE ChooseListDetailsLogTbl SET CLIId='{StatusValues.DECLINED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS}");
                    }

                    Response.ReferenceNumber = result + "";
                    Response.Message = "TransactionProcessedSuccess";
                    Response.Status = NotificationType.SUCCESS;
                    return Response;
                }
                else
                {
                    _logger.LogInformation($"FormRepository/CreateGeneralReceiptWalletTransfer] failed while insert new form..");
                    Response.Message = "ErrorOccurred";
                    Response.Status = NotificationType.ERROR;
                    return Response;
                }

            }
            catch (Exception ex)
            {
                _logger.LogInformation($"FormRepository/CreateGeneralReceiptWalletTransfer]/{CPR}/{amount}]", ex);
                Response.Status = NotificationType.ERROR;
                Response.Message = ex.Message;
                return Response;
            }

        }

        public async Task<Tuple<bool, int>> InsertForm(int formId, AppUser currentUser, Dictionary<int, string> fields, int formTableId)
        {
            return await InsertForm(formId, currentUser, currentUser.CustomerID, currentUser.Username + "", fields, (currentUser?.CAID ?? -1), false, formTableId);
        }

        public async Task<Tuple<bool, int>> InsertLogForm(int formId, AppUser currentUser, Dictionary<int, string> fields, int formTableId)
        {
            return await InsertForm(formId, currentUser, currentUser.CustomerID, currentUser.Username, fields, (currentUser?.CAID ?? -1), true, formTableId);
        }
        public async Task<Tuple<bool, int>> InsertLogForm(int formId, AppUser currentUser, Dictionary<int, string> fields, int CAID, int formTableId)
        {
            return await InsertForm(formId, currentUser, currentUser.CustomerID, currentUser.Username, fields, CAID, true, formTableId);
        }

        public async Task<Tuple<bool, int>> InsertForm(int formId, AppUser currentUser, int customerId, string username, Dictionary<int, string> fields, int CAID, bool isLogRequest, int formTableId)
        {
            var headerTblName = isLogRequest ? "CustomerServicesLogTbl" : "CustomerServicesTbl";
            var detailsTblName = isLogRequest ? "ChooseListDetailsLogTbl" : "ChooseListDetailsTbl";
            var logsMethodTitle = isLogRequest ? "InsertTransactionForm" : "InsertForm";

            _logger.LogInformation($"[FormRepository/{logsMethodTitle}/{formId}/{customerId}/{username}] Entered");

            try
            {
                // No data to add
                if (!fields.Any())
                {
                    formTableId = -1;
                    return new Tuple<bool, int>(true, formTableId);
                }

                // Build the form's header details
                var formHeader = await DapperHelper.ExecuteScalarAsync<int>(
                    $" INSERT INTO dbo.{headerTblName} (ProductID, UserModified, DateModified, Active, RecID, Posted,ContactId,CRID,CAID)" +
                    " VALUES (@ProductID, @UserModified, @DateModified, @Active, @RecID, @Posted,@ContactId,@CRID,@CAID);" +
                    " SELECT CONVERT(INT, SCOPE_IDENTITY());",
                        new
                        {
                            ProductID = formId,
                            UserModified = username,
                            DateModified = DateTime.Now,
                            Active = true,
                            RecID = customerId,
                            Posted = 1,
                            CAID = CAID,
                            CRID = currentUser?.CRID ?? -1,
                            TypeID = 0,
                            ContactId = currentUser?.ContactID ?? -1
                        }
                );

                // Set the form table id
                formTableId = formHeader;

                _logger.LogInformation($"[FormRepository/{logsMethodTitle}/{formId}/{customerId}/{username}] Insert into CustomerServicesTbl & Get ID which is {formTableId} ");

                var AllFeilds =
                   (await DapperHelper.QueryAsync<ChooseListPageCategoryTbl>(
                       " SELECT clp.*, clc.DataType" +
                       " FROM dbo.ChooseListPageTbl clp" +
                       " INNER JOIN dbo.ChooseListCategoryTbl clc ON clp.CLCatId = clc.CLCatId" +
                       " WHERE clp.pageId = 9049 AND clp.iType = @iType ",
                       new
                       {
                           @iType = formId
                       }
                   )).ToList();

                _logger.LogInformation($"[FormRepository/{logsMethodTitle}/{formId}/{customerId}/{formTableId}] All form fields count:{AllFeilds.Count}...");
                _logger.LogInformation($"[FormRepository/{logsMethodTitle}/{formId}/{customerId}/{formTableId}] Processing passed fields| count:{fields.Count}...");

                var detailsSql = "";

                var detailsParameters = new ExpandoObject() as IDictionary<string, object>;
                detailsParameters.Add("@pageId", 9049);
                var count = 0;
                List<int> CLCatIdsInserted = new List<int>();

                foreach (var key in fields.Keys)
                {
                    CLCatIdsInserted.Add(key);

                    detailsSql +=
                        $" INSERT INTO dbo.{detailsTblName} (pageId, TableId, CLCatId, CLIId)" +
                       $" VALUES (@pageId, @TableId{count}, @CLCatId{count}, @CLIId{count});";

                    detailsParameters.Add($"@TableId{count}", formHeader);
                    detailsParameters.Add($"@CLCatId{count}", key);
                    detailsParameters.Add($"@CLIId{count}", fields[key]);
                    count++;
                }

                var NonPassedcount = 0;
                foreach (var item in AllFeilds.Where(o => !CLCatIdsInserted.Contains(o.CLCatId ?? 0)))
                {
                    NonPassedcount++;
                    detailsSql +=
                        $" INSERT INTO dbo.{detailsTblName} (pageId, TableId, CLCatId, CLIId)" +
                        $" VALUES (@pageId, @TableId{count}, @CLCatId{count}, @CLIId{count});";
                    detailsParameters.Add($"@TableId{count}", formHeader);
                    detailsParameters.Add($"@CLCatId{count}", item.CLCatId);
                    detailsParameters.Add($"@CLIId{count}", GetFieldValue(item.CLCatId.GetValueOrDefault(-1), item.PreSetValue, false, currentUser));
                    count++;
                }

                _logger.LogInformation($"[FormRepository/{logsMethodTitle}/{formId}/{customerId}/{formTableId}] Processing Non passed fields| count:{NonPassedcount}...");


                // Save the changes and commit to the db
                DapperHelper.Exceute(detailsSql, detailsParameters);

                // Reset the form data (PCI Requirement)
                fields = null; GC.Collect();

                // All good
                return new Tuple<bool, int>(true, formTableId);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"[FormRepository/{logsMethodTitle}/{formId}/{customerId}]Exception:", ex);
            }

            formTableId = -1;
            return new Tuple<bool, int>(false, formTableId);
        }

        public async Task<bool> InsertForm(int formId, AppUser currentUser, List<Dictionary<int, string>> formsList, List<int> formTableIds)
        {
            // Initiliase the list of form ids that will be returned
            formTableIds = new List<int>();

            try
            {
                // No data to add
                if (!formsList.Any())
                {
                    return true;
                }

                // Add the fields
                foreach (var form in formsList)
                {
                    var formTableId = -1;
                    var inserted = await InsertForm(formId, currentUser, form, formTableId);
                    formTableId = inserted.Item2;
                    if (inserted.Item1) formTableIds.Add(formTableId);
                }
            }
            catch (Exception ex)
            {

            }

            // Return the results
            return formTableIds.Any();
        }

        public async Task<List<CardStatementModelView>> GetWalletStatementTransaction(int CAID, string month = "0", string Type = "")
        {
            var whereStmt = "";
            if (string.IsNullOrWhiteSpace(month)) { month = "0"; }
            var AccountNoStmt = " AND CA.ARAccountNo = G.AccountNo ";
            switch (Type.ToUpper())
            {
                case "HOLD":
                    AccountNoStmt = " AND CA.ARAdvAccount = G.AccountNo ";
                    break;
                case "WALLET":
                    AccountNoStmt = " AND CA.ARAccountNo = G.AccountNo ";
                    break;
                default:
                    AccountNoStmt = " AND CA.ARAccountNo = G.AccountNo ";
                    break;
            }


            var GLtransactionsQuery = await DapperHelper.QueryAsync<CardStatementModelView>("SET DATEFORMAT DMY;SELECT InputDate AS 'Date', ValueDate 'PostingDate', Description" +
                ",CASE WHEN G.[Sign]='CR' AND G.Amount >0 THEN -G.Amount ELSE G.Amount END AS 'TransactionAmount',CA.SwiftCode TransactionCurrency,CASE WHEN G.[Sign]='CR' AND G.Amount >0 THEN -G.Amount ELSE G.Amount END AS 'BillingAmount','' MerchantName" +
                ",'' CardNumber,G.InputDate As 'DueDate', CA.[Decimal] AS NoOfDecimals " +
                "FROM dbo.[General Ledger] G " +
                "INNER JOIN dbo.vw_CustomerFile CA ON CA.CAID = G.CAID " + AccountNoStmt +
                "WHERE G.CAID=@CAID " + whereStmt +
                $"AND DATEPART(m, G.InputDate) = DATEPART(m, DATEADD(m, {month}, getdate())) " +
                $"AND DATEPART(yyyy, G.InputDate) = DATEPART(yyyy, DATEADD(m, {month}, getdate())) " +
                "ORDER BY G.InputDate DESC, G.ID DESC"
                , new
                {
                    CAID = CAID
                }, "Ledger");
            var GLtransactions = GLtransactionsQuery.ToList();

            return GLtransactions;
        }

        public async Task<IEnumerable<Form>> GetFormsByParentId(int parentId, int contactId, int customerId, int tableId = -1, bool viewOnly = false, Dictionary<int, string> formValues = null, int formId = -1)
        {
            var parentFormsQuery =
                await DapperHelper.QueryAsync<FormHeader>(
                    $" SELECT pst.ProductID AS ID,{LanguageHelper.GetSelectStmt("pst.ProductNameE", "Title")}, cst.ID AS DetailsId" +
                    " FROM dbo.ProductServiceTbl pst" +
                    " LEFT JOIN dbo.CustomerServicesTbl cst  ON cst.ProductID  = pst.ProductID AND cst.RecID  = @RecID AND cst.Active  = 1" +
                    " LEFT JOIN dbo.CustomerServicesTbl cst2 ON cst2.ProductID = pst.ProductID AND cst2.RecID = @RecID AND cst2.Active = 1 AND cst.ID < cst2.ID" +
                      LanguageHelper.GetLeftJoin("ProductServiceTbl", "ProductNameE", "pst.ProductID") +
                   $" WHERE pst.ParentID = @ParentID AND cst2.ID IS NULL AND pst.Active = 1 {(formId != -1 ? " AND pst.ProductID = @ProductID" : "")}" +
                    " ORDER BY pst.SortCode",
                    new
                    {
                        RecID = customerId,
                        ParentID = parentId,
                        ProductID = formId
                    }
                );
            var parentForms = parentFormsQuery.ToList();

            var forms = new List<Form>();
            foreach (var parentForm in parentForms)
            {
                var formById = await GetFormById(parentForm.ID, contactId, customerId, tableId == -2 ? parentForm.DetailsId : tableId, viewOnly, formValues);
                var form = new Form
                {
                    ID = parentForm.ID,
                    Title = parentForm.Title,
                    Fields = formById.ToList()
            };

                forms.Add(form);
            }

            return forms;
        }

        public async Task<Dictionary<string, int>> GetNotificationsCount(string customerId, string contactId)
        {
            // Keep track of the notification counts
            var counts = new Dictionary<string, int>();

            // Get the list of notifications
            var data =
                (await DapperHelper.QueryAsync<NotificationCount>(
                    " SELECT CLIId AS Status, COUNT(CLIId) AS Count" +
                    " FROM dbo.CustomerServicesTbl cst" +
                    " INNER JOIN dbo.ChooseListDetailsTbl cld ON cst.ID = cld.TableId" +
                   $" WHERE cst.ProductID = {PageTypes.NOTIFICATIONS_PAGE}" +
                   $" AND cst.RecID = {customerId}" +
                   $" AND cst.ContactId = {contactId}" +
                   $" AND cst.Active = 1 AND cld.CLCatId = {ControlTypes.READ}" +
                    " GROUP BY CLIId"
                )).ToList();

            // Calculate the total and unread notifications
            counts = new Dictionary<string, int>
            {
                { "TOTAL" , data.Sum(r => r.Count) },
                { "UNREAD", data.FirstOrDefault(r => r.Status == PickerValues.NO + "")?.Count ?? 0 },
            };

            // Figure out the read notifications count
            counts["READ"] = counts["TOTAL"] - counts["UNREAD"];

            // Return the counts
            return counts;
        }

        public async Task<RequestViewModel> CreateRequestFormModel(AppUser appUser,int formId,Dictionary<int,string> formValues,int tableId, 
            bool viewOnly, int contactId = -1, 
            int customerId = -1, 
            bool isLogRequest = false, bool checkExternalPublish = false, bool checkExternalEditable = false)
        {
            customerId = customerId != -1 ? customerId : appUser.CustomerID;
            contactId = contactId != -1 ? contactId : appUser.ContactID;

            var titleQuery = await DapperHelper.QueryAsync<string>($"SELECT {LanguageHelper.GetSelectStmt("pst.ProductNameE", "ProductNameE")} FROM dbo.ProductServiceTbl pst" +
                 LanguageHelper.GetLeftJoin("ProductServiceTbl", "ProductNameE", "pst.ProductID") +
                $" WHERE pst.ProductID = @ProductID", new { ProductID = formId });
            var title = titleQuery.FirstOrDefault();


            if (_httpContextAccessor.HttpContext.User.Identity.IsAuthenticated && formValues == null)
            {
                formValues = new Dictionary<int, string>
                {
                    { ControlTypes.APPLICANT_NAME, appUser.Name },
                    { ControlTypes.USER_CPR, appUser.Username },
                    { ControlTypes.USER_MOBILE_NUMBER, appUser.Mobile }
                };
            }

            // Check if the form is for view only
            tableId = tableId == 0 ? -1 : tableId;
            viewOnly = tableId != -1;

            var fields = await GetFormById(formId, contactId, customerId, tableId, viewOnly, formValues, isLogRequest, checkExternalPublish, checkExternalEditable);

            var model = new RequestViewModel
            {
                Title = !string.IsNullOrWhiteSpace(title) ? title : "Untitled",
                Fields = fields.ToList()
            };

            return model;
        }

    }
}