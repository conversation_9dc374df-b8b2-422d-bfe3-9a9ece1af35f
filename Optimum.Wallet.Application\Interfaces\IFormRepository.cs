using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Extensions;
using Microsoft.AspNetCore.Http;

namespace Optimum.Wallet.Application.Interfaces
{
    public interface IFormRepository
    {
        Task<IEnumerable<RequestFormField>> GetFormById(int formId, int contactId, int customerId, int tableId = -1, bool viewOnly = false, Dictionary<int, string> formValues = null, bool isLogRequest = false, bool checkExternalPublish = false, bool checkExternalEditable = false);
        Task<bool> UpdateRequest(int formTableId, Dictionary<int, string> fields, bool isLogRequest = false);
        Task<int> ProcessEmailSms(int formHeaderId, int pageId, int contactId, AppUser currentUser = null, Dictionary<int, string> submitFields = null, bool isLogRequest = false);
        ResponseViewModel ValidateWalletMaxAmount(decimal amount, int productId, string customMessage = "");
        Task<Tuple<bool, int>> InsertForm(int formId, AppUser currentUser, Dictionary<int, string> fields, int formTableId);
        Task<Tuple<bool, int>> InsertForm(int formId, AppUser currentUser, int customerId, string username, Dictionary<int, string> fields, int CAID, bool isLogRequest, int formTableId);
        Task<Tuple<bool, int>> InsertRequest(int formId, AppUser currentUser, IFormCollection fields, bool isLogRequest);
        Task<Tuple<bool, int>> InsertRequest(int formId, AppUser currentUser, IFormCollection fields, int formTableId, bool processEmail, bool processWebService, bool isLogRequest);
        Task<Tuple<bool, int>> InsertRequest(int formId, int customerId, int contactId, string username, IFormCollection fields, int formTableId, int caid, int crid);
        Task<Tuple<bool, int>> InsertRequest(int formId, int customerId, int contactId, string username, IFormCollection fields, int formTableId, int caid, int crid, AppUser currentUser, WalletExchangeQuote quote, bool checkExternalPublish = false);
        Task<ResponseViewModel> ValidateWalletMaxTotalAmountTransfer(decimal amount, int CAID, int contactId);
        Task<List<CardStatementModelView>> GetWalletStatementTransaction(int CAID, string month = "0", string Type = "");
        Task<ResponseViewModel> CreateGeneralReceiptWalletTransfer(int fromCAID, int toCAID, decimal amount, AppUser currentUser, int type, bool isSourceAmount = true);
        Task<Dictionary<string, int>> GetNotificationsCount(string customerId, string contactId);
        public Task<IEnumerable<Form>> GetFormsByParentId(int parentId, int contactId, int customerId, int tableId = -1, bool viewOnly = false, Dictionary<int, string> formValues = null, int formId = -1);
        public Task<RequestViewModel> CreateRequestFormModel(AppUser appUser, int formId, Dictionary<int, string> formValues, int tableId,
            bool viewOnly, int contactId = -1,
            int customerId = -1,
            bool isLogRequest = false, bool checkExternalPublish = false, bool checkExternalEditable = false);
        public Task<Tuple<bool, int>> InsertDictionaryRequest(int formId, AppUser currentUser, Dictionary<string, string> fields, bool processEmail = true, bool processWebService = true, bool isLogRequest = false);
    }
}