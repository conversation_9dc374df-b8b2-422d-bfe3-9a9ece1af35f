<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols" Version="8.12.1" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.12.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="QRCoder" Version="1.6.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="StandardizedQR" Version="1.0.21" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Optimum.Wallet.Core\Optimum.Wallet.Core.csproj" />
    <ProjectReference Include="..\Optimum.Wallet.Domain\Optimum.Wallet.Domain.csproj" />
  </ItemGroup>
</Project>
