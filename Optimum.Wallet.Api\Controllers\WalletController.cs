using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Optimum.Wallet.Infrastructure.Repository;
using Optimum.Wallet.Application.Common;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Extensions;
using Optimum.Wallet.Application.Interfaces.Repositories;
using Optimum.Wallet.Application.Interfaces;
using System.Drawing;

namespace Optimum.Wallet.Api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    public class WalletController : BaseController<CardsController>
    {
        private readonly IStringLocalizer<WalletController> _loc;
        private readonly ICardRepository _cardRepository;
        private readonly IWalletRepository _walletRepository;
        private readonly IFormRepository _formRepository;
        private readonly CacheHelper _cacheHelper;

        public WalletController(IStringLocalizer<WalletController> stringLocalizer,
            ICardRepository cardRepository, IDataProtectionProvider dataProtection, 
            CacheHelper cacheHelper, IWalletRepository walletRepository, IFormRepository formRepository)
        {
            _loc = stringLocalizer;
            _cardRepository = cardRepository;
            Misc._rootProvider = DataProtectionProvider.Create(new DirectoryInfo(@"C:\\WalletKeysExample"));
            _cacheHelper = cacheHelper;
            _walletRepository = walletRepository;
            _formRepository = formRepository;
        }

        [HttpGet]
        public async Task<IActionResult> GetWallets()
        {
            var walletAccounts = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, PaySubType: 1);
            return Ok(BaseResponse<List<WalletAccount>>.Success(walletAccounts));
        }

        [HttpGet("GetWalletMenu")]
        public async Task<IActionResult> GetWalletMenu()
        {
            var data = await _walletRepository.GetWalletMenu(CurrentUser.ContactID, _cardRepository);
            data.ForEach(a => a.PID = a.ProductId.ToString().Encrypt("PID"));
            return Ok(BaseResponse<List<MenuViewModel>>.Success(data));
        }

        [HttpGet("GetWalletCurrency")]
        public async Task<IActionResult> GetWalletCurrency()
        {
            var data = await _walletRepository.GetWalletCurrenciesAsync(CurrentUser.ContactID);
            return Ok(BaseResponse<List<WalletCurrency>>.Success(data.ToList()));
        }

        [HttpGet("GetGoalAccounts")]
        public async Task<IActionResult> GetGoalAccounts()
        {
            var GoalsAccounts = await _walletRepository.M_GetGoalsAccounts(CurrentUser.ContactID + "", "", 0);
            return Ok(BaseResponse<List<GoalAccount>>.Success(GoalsAccounts));
        }

        [HttpGet("GetGoalSubAccount")]
        public async Task<IActionResult> GetGoalSubAccount()
        {
            var data = await _walletRepository.GetSingleAccount<ChildAccount>(CurrentUser.ContactID, CurrentUser.CAID);
            return Ok(BaseResponse<ChildAccount>.Success(data));
        }

        [HttpGet("GetGoalAccount")]
        public async Task<IActionResult> GetGoalAccount(string Id)
        {
            var CAID = Id.Decrypt("CAID");
            if(string.IsNullOrEmpty(CAID))
            {
                return NotFound(BaseResponse<string>.Failure("Invalid Goal Id"));
            }
            var AccountQuery = await DapperHelper.QueryAsync<GoalAccount>($@"
                SELECT CA.EffectiveDate AS StartDate,CA.CAID AS [CAID], CA.CustAcNameE AS [Name], ISNULL(CA.StatementBalance, 0) AS [Balance], CF.CcyCode AS [CcyCode], CF.SwiftCode AS [SwiftCode], CF.CcyName AS [CcyName], CF.[Decimal] AS [NoOfDecimals] 
                ,CA.CABillingDate AS [DeadLine],CA.CRLimit AS [Limit],CA.RetentionChippingAmt,CA.RetentionFrequency,CA.Photo,CA.RefCAID               
                FROM dbo.CustomerAccountTbl CA
                INNER JOIN dbo.CustAcctSetupTbl CAS ON CAS.CustActId = CA.CustActId AND CAS.Default_ = 1
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON AF.AccountNo = CA.CAAccountNo
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy
                WHERE CA.RefContactID = @ContactID AND CA.CAID=@CAID", new
            {
                ContactID = CurrentUser.ContactID,
                CAID = CAID
            });

            var Account = AccountQuery.FirstOrDefault();
            Account.GoalDefaultCAID = await DapperHelper.ExecuteScalarAsync<int>($"SELECT TOP 1 FromCAID FROM dbo.GoalsChippingAmtFrequencyDetailsTbl WHERE ToGoalCAID={CAID} AND Active=1 ORDER BY Id DESC");

            return Ok(BaseResponse<GoalAccount>.Success(Account));
        }

        [HttpGet("GetWalletAccountType")]
        public async Task<IActionResult> GetWalletAccountType()
        {
            var data = await _walletRepository.GetWalletAccountTypesAsync();
            return Ok(BaseResponse<List<WalletAccountType>>.Success(data.ToList()));
        }

        [HttpPost("AddWalletAccount")]
        public async Task<IActionResult> AddWalletAccount([FromBody] AddWalletRequest addWalletRequest) 
        {
            var Response = new ResponseViewModel() { };

            var id = addWalletRequest.id;
            var type = addWalletRequest.type;
            var name = addWalletRequest.name;

            // Validate the submitted data
            if (id <= 0 || type <= 0 || string.IsNullOrWhiteSpace(name))
            {
                Response.Message = "ErrorDataSubmitted";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Success(Response));
            }

            var currencyQuery = await DapperHelper.QueryAsync<WalletCurrency>($@"
                SELECT CF.RecID AS Id, CF.CcyName AS [Name], CF.SwiftCode AS [Code], CF.Scan AS Image, CF.Active AS Active 
                FROM [Currency File] CF 
                WHERE CF.RecID = @Id AND CF.BranchID = @BranchId
                AND CF.Active = 1 AND CF.Cancel = 0
            ", new
            {
                Id = id,
                BranchId = WalletApplication.BranchId
            }, "Ledger");
            var currency = currencyQuery.FirstOrDefault();

            if (currency == null)
            {
                Response.Message = "ErrorDataSubmitted";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Success(Response));
            }

            var caid = await DapperHelper.ExecuteScalarAsync<int>($@"
                DECLARE @CAID INT;
                DECLARE @AutoAccountCode VARCHAR(255)
                SELECT @AutoAccountCode = [Value] FROM dbo.EnvSetupTbl WHERE VariableName='AutoCustomerAccountCode' and Active = 1 AND Branch = '{WalletApplication.BranchId}'
                IF (ISNULL(@AutoAccountCode, '') = '' OR ISNULL(@AutoAccountCode, '') = '1') 
                BEGIN 
                    SET @AutoAccountCode = '8'
                END

                INSERT INTO dbo.CustomerAccountTbl (
				CRID, CustActId, CustAcCode, CustAcNameE, CustAcNameA, CRDays, CRLimit, StatusID, EffectiveDate, UserModified, DateModified, DateInput, 
				CACRExpDate, CABillingDate, CADiscountCus, CAStateDate, CAACOfficer, CAAccountNo, CAGoodsReceivedAct, CAAdvAccount, AutoInvoice, StatementBalance, 
				StatementRetention, RetentionFrequency, SubProfitSeg, AnalysisID, Photo, CATaxAccount, ADStatementBalance, GRStatementBalance, VATStatus, 
				VATCatID, TDSClassID, TransactionCount, PanNo, Tax_No, PayType, RefContactID, TotalDueBalance, PaySubType)
                SELECT TOP 1 '{CurrentUser.CRID}',CAS.CustActId,FORMAT(CAS.SeqNo + 1,'D' + @AutoAccountCode), @Name, @Name,CAS.DefCRDays,CAS.DefCRLimit,1,GETDATE(),'amthal',GETDATE(),GETDATE(),
                NULL,NULL,0,GETDATE(),CSF.ACOfficer,CAS.ARAccountNo CAAccountNo,CAS.ARGoodsReceivedAct CAGoodsReceivedAct,CAS.ARAdvAccount CAAdvAccount,1,0,
                3,0,NULL,NULL,NULL,CAS.ARTaxAccount CATaxAccount,0,0,CAS.VATStatus,
                CAS.VATCatID,0,0,0,CAS.TaxAccount,0,@ContactID,0, @PaySubType
                FROM dbo.CustAcctSetupTbl CAS 
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON CAS.ARAccountNo = AF.AccountNo AND CAS.BranchID = '{WalletApplication.BranchId}' AND CAS.Default_ = 1 AND CAS.Active = 1 AND CAS.CustAcctType = 1
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy AND CF.SwiftCode = '{currency.Code}' AND CF.BranchID = CAS.BranchID
                INNER JOIN dbo.CustomerFile CSF ON CSF.RecID = '{CurrentUser.CustomerID}' 

                SET @CAID = SCOPE_IDENTITY();

                UPDATE CAS
                SET CAS.SeqNo = CAS.SeqNo + 1
                FROM dbo.CustAcctSetupTbl CAS
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON CAS.ARAccountNo = AF.AccountNo AND CAS.BranchID = '{WalletApplication.BranchId}' AND CAS.Default_ = 1 AND CAS.Active = 1 AND CAS.CustAcctType = 1
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy AND CF.SwiftCode = '{currency.Code}' AND CF.BranchID = CAS.BranchID
                INNER JOIN dbo.CustomerFile CSF ON CSF.RecID = '{CurrentUser.CustomerID}'

                SELECT @CAID
            ", new
            {
                ContactID = CurrentUser.ContactID,
                Name = name,
                PaySubType = type
            });

            Response.Message = caid > 0 ? "Account has been created successfully." : "Failed to create your account.";
            Response.Status = caid > 0 ? NotificationType.SUCCESS : NotificationType.ERROR;
            return Ok(BaseResponse<ResponseViewModel>.Success(Response));
        }

        [HttpPost("AddGoal")]
        public async Task<IActionResult> AddGoal([FromBody] AddGoalRequest addGoalRequest)
        {
            var goalName = addGoalRequest.Name;
            var goalAmount = addGoalRequest.Amount;
            var goalDeadline = addGoalRequest.Deadline;
            var goalStartDate = addGoalRequest.StartDate;
            var goalChippingSavings = addGoalRequest.ChippingSavings;
            var RetentionFrequency = addGoalRequest.RetentionFrequency;
            var currencyId = addGoalRequest.currencyId;
            var sourceCAID = addGoalRequest.sourceCAID;

            int fromCAID = 0;
            if (!int.TryParse(sourceCAID, out fromCAID))
            {
                return BadRequest(BaseResponse<string>.Failure(new string[] { "ErrorDataSubmitted" }));
            }

            var hasCaid = await _cardRepository.doesUserOwnCaid(CurrentUser, fromCAID);

            if (!hasCaid)
            {
                return BadRequest(BaseResponse<string>.Failure(new string[] { "ErrorDataSubmitted" }));
            }

            DateTime date = GeneralHelper.ParseDate(goalDeadline.ToShortDateString());
            DateTime dgoalStartDate = GeneralHelper.ParseDate(goalStartDate.ToShortDateString());

            var dayNumFrequency = 7;
            if (RetentionFrequency == WalletRetention.WEEKLY)
                dayNumFrequency = 7;
            else if (RetentionFrequency == WalletRetention.MONTHLY)
                dayNumFrequency = 30;

            var currencyQuery = await DapperHelper.QueryAsync<WalletCurrency>($@"
                SELECT CF.RecID AS Id, CF.CcyName AS [Name], CF.SwiftCode AS [Code], CF.Scan AS Image, CF.Active AS Active 
                FROM [Currency File] CF 
                WHERE CF.RecID = @Id AND CF.BranchID = @BranchId
                AND CF.Active = 1 AND CF.Cancel = 0
            ", new
            {
                Id = currencyId,
                BranchId = WalletApplication.BranchId
            }, "Ledger");
            var currency = currencyQuery.FirstOrDefault();

            if (currency == null)
            {
                return BadRequest(BaseResponse<string>.Failure(new string[] { "ErrorDataSubmitted" }));
            }

            //TODO: Make function to get sql Text of Insert CustomerAccountTbl 
            var caid = await DapperHelper.ExecuteScalarAsync<int>($@"SET DATEFORMAT DMY;
                                    DECLARE @NewCAID INT;
                                    DECLARE @ParentCAID INT;
                                    DECLARE @Name NVARCHAR(50);
                                    DECLARE @AutoAccountCode VARCHAR(255);
                                    SELECT @AutoAccountCode = [Value] FROM dbo.EnvSetupTbl WHERE VariableName='AutoCustomerAccountCode' and Active = 1 AND Branch = @BranchId    
                                    IF (ISNULL(@AutoAccountCode, '') = '' OR ISNULL(@AutoAccountCode, '') = '1')     
                                    BEGIN     
                                        SET @AutoAccountCode = '8'    
                                    END   
                                    --------------------------------------------BEGIN: GET THE PARENT CAID---------------------------------
                                    SELECT @ParentCAID = CAID FROM dbo.CustomerAccountTbl WHERE RefContactID=@ContactId AND PayType=@PayType AND ISNULL(RefCAID,'')=''
                                    SELECT @ParentCustomerId = CustomerID,@Name=ContactEng FROM dbo.ContactTbl WHERE ContactID= @ContactId
                                    --------------------------------------------END:  GET THE PARENT CAID---------------------------------
                                    --------------------------------------------BEGIN: INSERT THE CHILD CAID -------------------------------
                                    INSERT INTO dbo.CustomerAccountTbl (    
                                    CRID, CustActId, CustAcCode, CustAcNameE, CustAcNameA, CRDays, CRLimit, StatusID, EffectiveDate, UserModified, DateModified, DateInput,     
                                    CACRExpDate, CABillingDate, CADiscountCus, CAStateDate, CAACOfficer, CAAccountNo, CAGoodsReceivedAct, CAAdvAccount, AutoInvoice, StatementBalance,     
                                    StatementRetention, RetentionFrequency, SubProfitSeg, AnalysisID, Photo, CATaxAccount, ADStatementBalance, GRStatementBalance, VATStatus,     
                                    VATCatID, TDSClassID, TransactionCount, PanNo, Tax_No, PayType, RefContactID, TotalDueBalance,RefCAID,RetentionChippingAmt,PaySubType)    
                                    SELECT TOP 1 @CRID,CAS.CustActId,FORMAT(CAS.SeqNo + 1,'D' + @AutoAccountCode),@GoalName,@GoalName,CAS.DefCRDays,@CRLimit,1,@EffectiveDate,'amthal',GETDATE(),GETDATE(),    
                                    NULL,@CABillingDate,0,GETDATE(),CSF.ACOfficer,CAS.ARAccountNo CAAccountNo,CAS.ARGoodsReceivedAct CAGoodsReceivedAct,CAS.ARAdvAccount CAAdvAccount,1,0,    
                                    3,@RetentionFrequency,NULL,NULL,@GoalPhoto,CAS.ARTaxAccount CATaxAccount,0,0,CAS.VATStatus,    
                                    CAS.VATCatID,0,0,0,CAS.TaxAccount,@PayType,@ContactId,0 ,NULL,@goalChippingSavings,2     
                                    FROM dbo.CustAcctSetupTbl CAS     
                                    INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON CAS.ARAccountNo = AF.AccountNo AND CAS.BranchID = @BranchId AND CAS.Default_ = 1 AND CAS.Active = 1    
                                    INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy AND CF.SwiftCode = @CurrencyCode AND CF.BranchID = CAS.BranchID    
                                    INNER JOIN dbo.CustomerFile CSF ON CSF.RecID = @ParentCustomerId    

                                    SET @NewCAID = SCOPE_IDENTITY();

                                    UPDATE CAS    
                                    SET CAS.SeqNo = CAS.SeqNo + 1    
                                    FROM dbo.CustAcctSetupTbl CAS    
                                    INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON CAS.ARAccountNo = AF.AccountNo AND CAS.BranchID = @BranchId AND CAS.Default_ = 1 AND CAS.Active = 1    
                                    INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy AND CF.SwiftCode = @CurrencyCode AND CF.BranchID = CAS.BranchID    
                                    INNER JOIN dbo.CustomerFile CSF ON CSF.RecID = @ParentCustomerId   

                                    
                                    --------------------------------------------END: INSERT THE CHILD CAID ---------------------------------  
            "
            + "INSERT INTO dbo.GoalsChippingAmtFrequencyDetailsTbl"
                    + "(ToGoalCAID,FromCard,FromCAID,ChippingAmount,CreatedDate,DueDate,Active,PackNo,DateModified,UserModified)"
                    + "VALUES"
                    + "(@NewCAID,"                             //-- ToGoalCAID - int
                    + " @fromCard,"                           //-- FromCard - nvarchar(80)
                    + $"{fromCAID},"                 //-- FromCAID - int
                    + "@goalChippingSavings,"               //-- ChippingAmount - money
                    + "GETDATE(),"                         //-- CreatedDate - datetime
                    + $"@DueDate,"                        //-- DueDate - datetime
                    + "1,"                               //-- Active - bit
                    + "1,"                               //-- PackNo - bit
                    + "GETDATE(),"                      //-- DateModified - datetime
                    + $"N'{CurrentUser.Username}'"     //-- UserModified - nvarchar(50)
                    + " )" +
                    " SELECT @NewCAID "

                    + @"INSERT INTO dbo.iLeadGoalsHistory
                            (CAID, GoalAmount, ContributionAmount, FirstContributionDate, GoalDeadlineDate, Frequency, UserModified, DateModified,GoalName)
                            VALUES(@NewCAID, @GoalAmount, @ContributionAmount, @FirstContributionDate, @GoalDeadlineDate, @Frequency, @UserModified, GETDATE(),@GoalName); "

            , new
            {
                CurrencyCode = currency.Code,
                GoalName = goalName,
                BranchId = WalletApplication.BranchId,
                ContactId = CurrentUser.ContactID,
                ParentCustomerId = CurrentUser.CustomerID,
                CRID = CurrentUser.CRID,
                PayType = 0,
                CRLimit = goalAmount,
                CABillingDate = date.ToString("dd/MM/yyyy"),
                GoalPhoto = "",
                goalChippingSavings = goalChippingSavings,
                RetentionFrequency = RetentionFrequency,
                fromCard = "", //card.CardNumber,
                DueDate = dgoalStartDate.ToString("dd-MM-yyyy"),
                EffectiveDate = dgoalStartDate.ToString("dd-MM-yyyy"),
                //History parameters
                GoalAmount = goalAmount,
                ContributionAmount = goalChippingSavings,
                FirstContributionDate = dgoalStartDate.ToString("dd-MM-yyyy"),
                GoalDeadlineDate = date.ToString("dd/MM/yyyy"),
                Frequency = RetentionFrequency,
                UserModified = CurrentUser.Username
            });

            if (caid > 0)
            {
                #region Photo Process
                var uRsponse = FileUploadHelper.uploadFile("CaPho", caid, "AccountPhotoFolder", $"{General.InputPrefix}goalPhoto", addGoalRequest.picture);
                //TODO:Add else
                if (uRsponse.Status == NotificationType.SUCCESS)
                {
                    var fileName = uRsponse.ReferenceNumber;
                    var i = await DapperHelper.ExceuteAsync("UPDATE CustomerAccountTbl SET Photo=@Photo WHERE CAID=@CAID;", new
                    {
                        CAID = caid,
                        Photo = fileName
                    });
                }
                #endregion
            }
            return Ok(BaseResponse<int>.Success(caid));
        }

        [HttpPost("EditGoal")]
        public async Task<IActionResult> EditGoal([FromBody] EditGoalRequest editGoalRequest)
        {
            var goalName = editGoalRequest.Name;
            var goalAmount = editGoalRequest.Amount;
            var goalDeadline = editGoalRequest.Deadline.ToShortDateString();
            var goalStartDate = editGoalRequest.StartDate.ToShortDateString();
            var goalChippingSavings = editGoalRequest.ChippingSavings;
            var RetentionFrequency = editGoalRequest.RetentionFrequency;
            var currencyId = editGoalRequest.currencyId;
            var sourceCAID = editGoalRequest.sourceCAID;
            var CAID = editGoalRequest.CAID.Decrypt("CAID");

            int fromCAID = 0;
            if (!int.TryParse(sourceCAID, out fromCAID) || string.IsNullOrEmpty(CAID))
            {
                return BadRequest(BaseResponse<string>.Failure( "ErrorDataSubmitted" ));
            }

            var dayNumFrequency = 7;
            if (RetentionFrequency == WalletRetention.WEEKLY )
                dayNumFrequency = 7;
            else if (RetentionFrequency == WalletRetention.MONTHLY)
                dayNumFrequency = 30;

            var NextGoalDueDate = GeneralHelper.getNextGoalDueDate(GeneralHelper.ParseDate(goalStartDate), dayNumFrequency);

            var updateStmt = "SET DATEFORMAT DMY; UPDATE dbo.CustomerAccountTbl " +
                   "SET CustAcNameE=@GoalName" +
                   ",CustAcNameA=@GoalName" +
                   ",CRLimit=@CRLimit" +
                   ",CABillingDate=@CABillingDate" +
                   ",RetentionChippingAmt=@goalChippingSavings" +
                   ",RetentionFrequency=@RetentionFrequency" +
                   //",EffectiveDate=@EffectiveDate" +
                   " WHERE CAID=@CAID; "
                   + $" UPDATE GoalsChippingAmtFrequencyDetailsTbl SET Active=0 WHERE ToGoalCAID={CAID} ;"
                   + $" DECLARE @PackNo INT; SELECT  @PackNo= MAX(ISNULL(PackNo,0))+1 FROM GoalsChippingAmtFrequencyDetailsTbl WHERE ToGoalCAID={CAID}  "
                   + "INSERT INTO dbo.GoalsChippingAmtFrequencyDetailsTbl"
                   + "(ToGoalCAID,FromCard,FromCAID,ChippingAmount,CreatedDate,DueDate,Active,PackNo,DateModified,UserModified)"
                   + "VALUES"
                   + "(@CAID,"                                //-- ToGoalCAID - int
                   + " @fromCard,"                           //-- FromCard - nvarchar(80)
                   + $"{fromCAID},"                 //-- FromCAID - int
                   + "@goalChippingSavings,"               //-- ChippingAmount - money
                   + "GETDATE(),"                         //-- CreatedDate - datetime
                   + $"@DueDate,"                        //-- DueDate - datetime
                   + "1,"                               //-- Active - bit
                   + "ISNULL(@PackNo,1),"                        //-- PackNo - bit
                   + "GETDATE(),"                      //-- DateModified - datetime
                   + $"N'{CurrentUser.Username}'"     //-- UserModified - nvarchar(50)
                   + " )";
            updateStmt += @"INSERT INTO dbo.iLeadGoalsHistory
                            (CAID, GoalAmount, ContributionAmount, FirstContributionDate, GoalDeadlineDate, Frequency, UserModified, DateModified,GoalName)
                            VALUES (@CAID, @GoalAmount, @ContributionAmount, @FirstContributionDate, @GoalDeadlineDate, @Frequency, @UserModified, GETDATE(),@GoalName);  ";


            var i = await DapperHelper.ExceuteAsync(updateStmt, new
            {
                GoalName = goalName,
                CAID = CAID,
                CRLimit = goalAmount,
                CABillingDate = GeneralHelper.ParseDate(goalDeadline).ToString("dd-MM-yyyy"),
                /*EffectiveDate = GeneralHelper.ParseDate(goalStartDate).ToString("dd-MM-yyyy"),*/
                goalChippingSavings = goalChippingSavings,
                RetentionFrequency = RetentionFrequency,
                fromCard = "",
                DueDate = NextGoalDueDate.ToString("dd-MM-yyyy")                ,
                GoalAmount = goalAmount,
                ContributionAmount = goalChippingSavings,
                FirstContributionDate = GeneralHelper.ParseDate(goalStartDate).ToString("dd-MM-yyyy"),
                GoalDeadlineDate = GeneralHelper.ParseDate(goalDeadline).ToString("dd-MM-yyyy"),
                Frequency = RetentionFrequency,
                UserModified = CurrentUser.Username
            });
            #region Photo Process
            var uRsponse = FileUploadHelper.uploadFile("CaPho", Convert.ToInt32(CAID), "AccountPhotoFolder", $"{General.InputPrefix}E_goalPhoto", editGoalRequest.picture);
            //TODO:Add else
            if (uRsponse.Status == NotificationType.SUCCESS)
            {
                var fileName = uRsponse.ReferenceNumber;
                var res = await DapperHelper.ExceuteAsync("UPDATE CustomerAccountTbl SET Photo=@Photo WHERE CAID=@CAID;", new
                {
                    CAID = CAID,
                    Photo = fileName
                });
            }
            #endregion

            return Ok(BaseResponse<int>.Success());
        }

        [HttpPost("TransferGoal")]
        public async Task<IActionResult> TransferGoal([FromBody] TransferGoalRequest transferGoalRequest)
        {
            var sfromCAID = transferGoalRequest.fromCAID.Decrypt("CAID");
            var stoCAID = transferGoalRequest.toCAID.Decrypt("CAID");
            var Amount = transferGoalRequest.amount;
            var fromCAID = 0;
            int.TryParse(sfromCAID, out fromCAID);
            var toCAID = 0;
            int.TryParse(stoCAID, out toCAID);
            if (fromCAID == toCAID)
            {
                return BadRequest(BaseResponse<string>.Failure("The source and destination can not be same"));
            }
            if (Amount == 0)
            {
                return BadRequest(BaseResponse<string>.Failure("invalid transfer amount"));
            }
            var data = await _formRepository.CreateGeneralReceiptWalletTransfer(fromCAID, toCAID, Amount, CurrentUser, 2);
            return Ok(BaseResponse<ResponseViewModel>.Success(data));
        }

        [HttpPost("WalletTransfer")]
        public async Task<IActionResult> WalletTransfer([FromBody] WalletTransferRequest walletTransferRequest)
        {
            var fromCAID = walletTransferRequest.fromCAID;
            var toCAID = walletTransferRequest.toCAID;

            // Don't allow the user to transfer from/to other accounts
            var hasFromCaid = await _cardRepository.doesUserOwnCaid(CurrentUser, fromCAID);
            var hasToCaid = await _cardRepository.doesUserOwnCaid(CurrentUser, toCAID);

            if (!hasFromCaid || !hasToCaid)
            {
                return BadRequest(BaseResponse<string>.Failure(new string[] { "ErrorDataSubmitted" }));
            }

            var amount = walletTransferRequest.amount;

            if (fromCAID == toCAID)
            {
                return BadRequest(BaseResponse<string>.Failure(new string[] { "The source and destination can not be same" }));
            }

            var transferResponse = await _formRepository.CreateGeneralReceiptWalletTransfer(fromCAID, toCAID, amount, CurrentUser, 2);

            return Ok(BaseResponse<ResponseViewModel>.Success(transferResponse));
        }        

        [HttpGet("MerchantWallet")]
        public async Task<IActionResult> MerchantWallet()
        {
            var walletAccounts = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, -1, 2);
            return Ok(BaseResponse<List<WalletAccount>>.Success(walletAccounts));
        }

        [HttpGet("GetWalletStatement")]
        public async Task<IActionResult> GetWalletStatement(string CAID = "", string Type = "")
        {
            // Get the statement headers
            var resultQuery = await DapperHelper.QueryAsync<dynamic>("SET DATEFORMAT DMY;" +
                " SELECT -1 'Value' ,FORMAT(DATEADD(m, -1, getdate()),'MMMM') + ' ' +CONVERT(NVARCHAR, DATEPART(yyyy, DATEADD(m, -1, getdate()))) 'Text' " +
                "UNION " +
                "SELECT -2 'Value' ,FORMAT(DATEADD(m, -2, getdate()),'MMMM') + ' ' +CONVERT(NVARCHAR, DATEPART(yyyy, DATEADD(m, -2, getdate()))) 'Text' " +
                "UNION " +
                "SELECT -3 'Value' ,FORMAT(DATEADD(m, -3, getdate()),'MMMM') + ' ' +CONVERT(NVARCHAR, DATEPART(yyyy, DATEADD(m, -3, getdate()))) 'Text' " +
                "ORDER BY 1 DESC"
                            );
            var result = resultQuery.ToDictionary(row => (int)row.Value, row => (string)row.Text);
            return Ok(BaseResponse<Dictionary<int,string>>.Success(result));
        }

        [HttpPost("StatementResult")]
        public async Task<IActionResult> StatementResult([FromBody] WalletStatementRequest walletStatementRequest)
        {
            var type = walletStatementRequest.type;
            var card = walletStatementRequest.card;
            var date = walletStatementRequest.date;
            var suppCards = walletStatementRequest.suppCards;
            var onlySupp = walletStatementRequest.onlySupp;
            var page = walletStatementRequest.page;
            var IsWalletStatement = walletStatementRequest.IsWalletStatement;
            var sCAID = walletStatementRequest.sCAID;
            var StmtType = walletStatementRequest.StmtType;

            // Try to fetch the supplementary cards
            var suppDict = new Dictionary<string, string>();
            try
            {
                suppDict = JsonConvert.DeserializeObject<Dictionary<string, string>>(suppCards);
            }
            catch (Exception ex)
            {

            }

            int CAID = CurrentUser.CAID;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (!int.TryParse(sCAID.Decrypt("CAID"), out CAID))
                {
                    CAID = CurrentUser.CAID;
                }
            }

            var userLinkedCaid = await _cardRepository.doesUserOwnCaid(CurrentUser, CAID);
            if (!userLinkedCaid)
            {
                Logger.LogDebug($"[StatementResult][{CurrentUser.Username}] User tried to get the statements of a CAID not linked to their account!");
                return Unauthorized();
            }

            List<CardStatementModelView> model = null;
            if (IsWalletStatement)
            {
                model = await _formRepository.GetWalletStatementTransaction(CAID, date, StmtType);
            }

            /* else
            {
                model = WebServicesHelper.GetStatementTransaction(
                    CurrentUser.Username,
                    card,
                    type, date, suppDict, onlySupp);
            } */

            return Ok(BaseResponse<List<CardStatementModelView>>.Success(model));
        }

        
        [HttpGet("GetStatementBalance")]
        public async Task<IActionResult> GetStatementBalance()
        {
            var walletBalancesAsync = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID);
            var walletBalances = walletBalancesAsync.Select(a => 
            new WalletStatementBalanceResponse() 
            { 
                HashedCAID = a.HashedCAID, 
                Balance = a.Balance.FormatAmount(a.NoOfDecimals, a.SwiftCode) 
            }).AsList();
            return Ok(BaseResponse<List<WalletStatementBalanceResponse>>.Success(walletBalances));
        }

        [HttpGet("GenerateQR")]
        public async Task<IActionResult> GenerateQR(string sCAID,bool isMerchant = false)
        {
            ResponseViewModel Response = new();

            Logger.LogDebug($"{{IP}}[Wallet]/[GenerateStaticQR]/[{CurrentUser.Username}]Generate Static QR Clicked.");
            int CAID = CurrentUser.CAID;
            WalletAccount account = null;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (!int.TryParse(sCAID.Decrypt("CAID"), out CAID))
                {
                    CAID = CurrentUser.CAID;
                }
                account = (await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, CAID, isMerchant ? 2 : 1)).FirstOrDefault();
            }

            if (account == null)
            {
                Logger.LogDebug($"[Wallet/GenerateStaticQR][{CurrentUser.Username}] Could not find the wallet account.");
                Response.Message = "ErrorOccurred";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            var items = await _walletRepository.GetMerchantDataAsync(CurrentUser.CustomerID);

            if (items == null)
            {
                Logger.LogDebug($"[Wallet/GenerateStaticQR][{CurrentUser.Username}] Data Retrieved From CustomerFile IS NULL.");

                Response.Message = "ErrorOccurred";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            if (items.ISO18245 <= 0)
            {
                Logger.LogDebug($"[Wallet/GenerateStaticQR][{CurrentUser.Username}] ISO 18245[{items.ISO18245}] IS Invalid.");

                Response.Message = "ErrorOccurred";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            var payload = GeneralHelper.GenerateStaticQR(account, items, CurrentUser);

            Logger.LogDebug($"[Wallet/GenerateStaticQR][{CurrentUser.Username}] payload Code:{payload}");

            var imgBarPath = "";
            using (Bitmap bitMap = GeneralHelper.GetBitmapQR(payload))
            {
                using (MemoryStream ms = new MemoryStream())
                {
                    bitMap.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
                    byte[] byteImage = ms.ToArray();
                    imgBarPath = "data:image/png;base64," + Convert.ToBase64String(byteImage);
                }                
            }

            var data = new
            {
                QRTitle = account.Name,
                QRDescription = $"A/C Number: {account.AccountNumber}",
                QR = imgBarPath
            };

            return Ok(BaseResponse<dynamic>.Success(data));
        }

        [HttpGet("Balance")]
        public async Task<IActionResult> Balance()
        {
            var balance = (await DapperHelper.QueryAsync<decimal>(
                "SELECT ISNULL(StatementBalance, 0) FROM dbo.CustomerAccountTbl WHERE CAID = @CAID", new
                {
                    CAID = CurrentUser.CAID
                })).FirstOrDefault();

            return Ok(BaseResponse<decimal>.Success(balance));
        }

        [HttpPost("BlockUnBlock")]
        public async Task<IActionResult> BlockUnBlock(string CAID = "")
        {
            CAID = CAID.Decrypt(General.CAIDEncryptPurpose);

            ResponseViewModel response = new ResponseViewModel()
            {
                Message = "ErrorDataSubmitted",
                Status = NotificationType.ERROR
            };

            var Status = await DapperHelper.ExecuteScalarAsync<int>($"SELECT StatusID FROM dbo.CustomerAccountTbl WHERE CAID={CAID}");

            if (Status == CustActStatus.OPEN)
            {
                var I = DapperHelper.Exceute($"UPDATE dbo.CustomerAccountTbl SET StatusID='{CustActStatus.FROZEN}' WHERE CAID={CAID}");
                if (I > 0)
                {
                    response.Message = "CardBlockSuccess";
                    response.Status = NotificationType.SUCCESS;
                }
            }

            if (Status == CustActStatus.FROZEN)
            {
                var I = await DapperHelper.ExceuteAsync($"UPDATE dbo.CustomerAccountTbl SET StatusID='{CustActStatus.OPEN}' WHERE CAID={CAID}");
                if (I > 0)
                {
                    response.Message = "CardUnBlockSuccess";
                    response.Status = NotificationType.SUCCESS;
                }
            }
            response.TransactionNumber = DapperHelper.ExecuteScalar<int>($"SELECT StatusID FROM dbo.CustomerAccountTbl WHERE CAID={CAID}") + "";

            return Ok(BaseResponse<ResponseViewModel>.Success(response));
        }
    }
}
