using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Logging;
using Optimum.Wallet.Application.Common;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Application.Interfaces.Repositories;

namespace Optimum.Wallet.Infrastructure.Repository
{
    public class WalletRepository : IWalletRepository
    {
        ILogger<WalletRepository> _logger;
        private readonly IDataProtectionProvider _dataProtection;
        public WalletRepository(ILogger<WalletRepository> logger, IDataProtectionProvider dataProtection)
        {
            _logger = logger;
            _dataProtection = dataProtection;
            Misc._rootProvider = _dataProtection.CreateProtector(GetType().FullName);
        }
        public async Task<IEnumerable<WalletCurrency>> GetWalletCurrenciesAsync(int ContactID)
        {
            return await DapperHelper.QueryAsync<WalletCurrency>($@"
                SELECT MIN(CF.RecID) AS Id, MIN(CF.CcyName) AS [Name], CF.SwiftCode AS [Code], MIN(CF.Scan) Image, CASE WHEN MIN(CA.CAID) IS NULL THEN 0 ELSE 1 END AS Active, CASE WHEN MIN(CF.Rate) = 1 THEN 1 ELSE 0 END AS IsDefault
                FROM dbo.CustAcctSetupTbl CAS 
                INNER JOIN {DapperHelper.LedgerDbName}BudgetCenter BC ON BC.BudgetSeg = CAS.BranchID
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON CAS.ARAccountNo = AF.AccountNo
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy AND CF.BranchID = CAS.BranchID
                LEFT JOIN {DapperHelper.UtilitiesDbName}CustomerAccountTbl CA ON CA.RefContactId = @ContactID AND CA.CustActId = CAS.CustActId
                WHERE CAS.Active = 1 AND CAS.Default_ = 1 AND CAS.CustAcctType = 1
                AND BC.Active = 1 AND BC.Cancel = 0 
                AND AF.Active = 1 AND AF.Cancel = 0 
                AND CF.Active = 1 AND CF.Cancel = 0
                AND BC.BudgetSeg = @BranchID
                GROUP BY CF.SwiftCode, CF.CcyName
                ORDER BY CF.CcyName", new
            {
                BranchID = WalletApplication.BranchId,
                ContactID = ContactID
            });
        }
        public async Task<IEnumerable<WalletAccountType>> GetWalletAccountTypesAsync(char type='A')
        {
            return await DapperHelper.QueryAsync<WalletAccountType>($@"
                SELECT ID AS Id, SubTypeNameE AS Name, [Prefix] AS [Code], SortCode AS SortCode, MultiCurrency AS MultiCurrency, Active AS Active FROM dbo.CustomerAccountSubTypeTbl WHERE Active = 1 AND [Type] = '{type}' AND ID NOT IN ({CustomerAccountSubType.SAVINGS}) ORDER BY SortCode
            ");
        }
        public async Task<List<WalletAccount>> GetWalletAccounts(int contactId, int CAID = -1, int type = 1, int PaySubType = -1)
        {
            /* var walletAccountsQuery = await DapperHelper.QueryAsync<WalletAccount>($@"
                SELECT CA.CAID AS [CAID], CA.CustAcNameE AS [Name], CA.CustAcCode AS [AccountNumber], SBT.SubTypeNameE AS AccountSubType, ISNULL(CA.StatementBalance, 0) AS [Balance], ISNULL(CA.ADStatementBalance, 0) AS [HoldBalance], CF.CcyCode AS [CcyCode], CF.SwiftCode AS [SwiftCode], CF.CcyName AS [CcyName], CF.[Decimal] AS [NoOfDecimals], CF.Scan AS [Image]
                FROM dbo.CustomerAccountTbl CA
                LEFT JOIN dbo.CustomerAccountSubTypeTbl SBT ON SBT.ID = CA.PaySubType
                INNER JOIN dbo.CustAcctSetupTbl CAS ON CAS.CustActId = CA.CustActId AND CAS.Default_ = 1 AND {(type == -1 ? "CAS.CustAcctType IN (1,2)" : "CAS.CustAcctType = @AccountType")}
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON AF.AccountNo = CA.CAAccountNo
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy
                WHERE CA.RefContactID = @ContactID AND ISNULL(PayType, 0) = 0" + (CAID == -1 ? "" : " AND CA.CAID = @CAID") + " " + (PaySubType == -1 ? "" : " AND CA.PaySubType = @PaySubType"), new
            {
                ContactID = contactId,
                CAID = CAID,
                AccountType = type,
                PaySubType = PaySubType
            }); */
            var walletAccountsQuery = await DapperHelper.QueryAsync<WalletAccount>($@"
                SELECT CA.CAID AS [CAID], CA.CustAcNameE AS [Name], CA.CustAcCode AS [AccountNumber], SBT.SubTypeNameE AS AccountSubType, ISNULL(CA.StatementBalance, 0) AS [Balance], ISNULL(CA.ADStatementBalance, 0) AS [HoldBalance], CF.CcyCode AS [CcyCode], CF.SwiftCode AS [SwiftCode], CF.CcyName AS [CcyName], CF.[Decimal] AS [NoOfDecimals], CF.Scan AS [Image]
                FROM dbo.CustomerAccountTbl CA
                LEFT JOIN dbo.CustomerAccountSubTypeTbl SBT ON SBT.ID = CA.PaySubType
                INNER JOIN dbo.CustAcctSetupTbl CAS ON CAS.CustActId = CA.CustActId AND CAS.Default_ = 1 AND {(type == -1 ? "CAS.CustAcctType IN (1,2)" : "CAS.CustAcctType = @AccountType")}
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON AF.AccountNo = CA.CAAccountNo
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy
                WHERE CA.RefContactID = @ContactID AND ISNULL(PayType, 0) = 0" + (CAID == -1 ? "" : " AND CA.CAID = @CAID") + " " + (PaySubType == -1 ? " AND (CA.PaySubType IS NOT NULL OR CAS.CustAcctType = 2)" : " AND CA.PaySubType = @PaySubType"), new
            {
                ContactID = contactId,
                CAID,
                AccountType = type,
                PaySubType
            });
            var walletAccounts = walletAccountsQuery.ToList();

            // Keep track of a global encrypted CAID to avoid having different encrypted values for the same CAID
            walletAccounts.ForEach(r =>
            {
                r.EncryptedCAID = (r.CAID + "").Encrypt(General.CAIDEncryptPurpose);
                r.HashedCAID = (r.CAID + "").HashMD5();
            });

            return walletAccounts;
        }
        public async Task<List<WalletAccount>> GetCurrencyWallets(int RefContactID, int MainCAID)
        {
            var walletAccountsQuery = await DapperHelper.QueryAsync<WalletAccount>($@"
                SELECT CA.CAID AS [CAID], CA.CustAcNameE AS [Name], CA.CustAcCode AS [AccountNumber], SBT.SubTypeNameE AS AccountSubType, ISNULL(CA.StatementBalance, 0) AS [Balance], ISNULL(CA.ADStatementBalance, 0) AS [HoldBalance], CF.CcyCode AS [CcyCode], CF.SwiftCode AS [SwiftCode], CF.CcyName AS [CcyName], CF.[Decimal] AS [NoOfDecimals], CF.Scan AS [Image]
                FROM dbo.CustomerAccountRelationshipTbl CAREL
	            INNER JOIN dbo.CustomerAccountTbl CA ON CA.CAID=CAREL.RefCAID
                LEFT JOIN dbo.CustomerAccountSubTypeTbl SBT ON SBT.ID = CA.PaySubType
                INNER JOIN dbo.CustAcctSetupTbl CAS ON CAS.CustActId = CA.CustActId AND CAS.Default_ = 1 
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON AF.AccountNo = CA.CAAccountNo
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy
                WHERE CA.RefContactID = @ContactID AND CAREL.MainCAID=@MainCAID ", new
            {
                ContactID = RefContactID,
                MainCAID
            });
            var walletAccounts = walletAccountsQuery.ToList();

            // Keep track of a global encrypted CAID to avoid having different encrypted values for the same CAID
            walletAccounts.ForEach(r =>
            {
                r.EncryptedCAID = (r.CAID + "").Encrypt(General.CAIDEncryptPurpose);
                r.HashedCAID = (r.CAID + "").HashMD5();
            });

            return walletAccounts;
        }
        public async Task<decimal> GetCustomerWalletBalance(int CAID, int contactId)
        {
            var customerWalletBalanceQuery = await DapperHelper.QueryAsync<decimal>(
                " SELECT ISNULL(StatementBalance, 0)" +
                " FROM dbo.CustomerAccountTbl" +
                " WHERE CAID = @CAID ", new
                {
                    CAID
                });
            return customerWalletBalanceQuery.FirstOrDefault();
        }
        private async Task<List<T>> M_GetChildAccounts<T>(int parentContactId, int childContactId, bool isSingleChild)
        {
            //TODO:CONVERT THE SQL TO PROCEDURE IF IT IS NEED

            //Check the request is it for one child or multi by parent contactId
            var contactId = isSingleChild ? childContactId : parentContactId;
            var inContactStmt = isSingleChild ? "@ContactID" : "SELECT ContactId FROM dbo.ContactRelativeRelationTbl WHERE RefContactId=@ContactID";

            var childAccountsQuery = await DapperHelper.QueryAsync<T>($@"SET DATEFORMAT DMY;
                                    --------------------------------------- BEGIN: GET CHILD ACCOUNT ----------------------------------
                                    SELECT CA.StatusID,CT.ContactPhoto Photo,CT.HasCard,CT.CONT_CPR,CT.ContactID,CASE WHEN CT.TitleID=4 AND ISNULL(CT.Title,'')!='' THEN CT.Title ELSE CT.ContactEng END AS [Name],CA.CAID AS [CAID], CA.CustAcNameE, ISNULL(CA.StatementBalance, 0) AS [Balance], CF.CcyCode AS [CcyCode], CF.SwiftCode AS [SwiftCode], CF.CcyName AS [CcyName], CF.[Decimal] AS [NoOfDecimals] 
                                    ,cast(NULL as Money) totalSavings,CT.ContactPhoto,cast(NULL as MONEY) totalGoals ,0 AvailableToSpend,ISNULL(CA.Spent, 0) AS Spent, ISNULL(CA.CRLimit,0) AS [Limit],ISNULL(CA.Hold,0) AS [Hold],cast(NULL as Money)  allowance ,0 allowanceDaysNo,0 ParentCPR,cast(0 as BIT) isSymbolMoney,cast(NULL as Money) SymbolValue
                                    /*,cast(NULL as Money) completedtaskToBePaidAmount,cast(NULL as int) completedtaskToBePaidCount*/
                                    , Number = ROW_NUMBER() OVER (ORDER BY CT.ContactID)                 
                                    INTO #temp
                                    FROM dbo.CustomerAccountTbl CA
                                    INNER JOIN dbo.CustAcctSetupTbl CAS ON CAS.CustActId = CA.CustActId AND CA.CustActId = {CustActId.CHILD_CARD}
                                    INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON AF.AccountNo = CA.CAAccountNo
                                    INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy
                                    INNER JOIN dbo.ContactTbl CT ON CT.ContactID = CA.RefContactID
                                    WHERE CT.ContactId IN({inContactStmt}) AND CT.Active=1 AND ISNULL(CA.PayType,0) =0 
                                    
                                    ALTER TABLE #temp ADD CardClass NVARCHAR(50);
                                    --------------------------------------- END: GET CHILD ACCOUNT ----------------------------------

                                    --------------------------------------- BEGIN: LOOP TO GET THE TOTAL SAVINGS & SPENT ----------------------------------
                                    DECLARE @Count int  
                                    Declare @i int  
                                    Set @i = 1  
                                    Select @Count = Count(*) from #Temp  

                                    DECLARE @allowDaysNo INT;
                                    DECLARE @totalGoals MONEY;
                                    DECLARE @totalSaving MONEY; 
                                    DECLARE @totalAllow MONEY;
                                    DECLARE @totalDR MONEY; 
                                    DECLARE @spendAvailable MONEY; 
                                    DECLARE @classDesign NVARCHAR(50);
                                    DECLARE @ParentCPR NVARCHAR(50);

                                    WHILE @i<= @Count  
                                    BEGIN
                                    SET @allowDaysNo=0;
                                    SET @totalGoals =0;
                                    SET @totalAllow =0;
                                    SET @totalDR=0;
                                    SET @totalSaving=0;
                                    SET @spendAvailable=0;
                                    SET @classDesign=''; 
                                    SET @ParentCPR='';

                                    --begin:Get the total Saving
                                    SELECT @totalSaving=SUM(ISNULL(CA.StatementBalance, 0))
                                    FROM dbo.CustomerAccountTbl CA
                                    INNER JOIN #temp T ON CA.RefContactID=T.ContactID
                                    WHERE CA.PayType=1 AND ISNULL(CA.RefCAID,0)!=0 AND T.Number=@i
									
									UPDATE #temp SET totalSavings= ISNULL(@totalSaving,0) WHERE Number=@i
                                    --end:Get the the total Saving
                                
                                    --begin:Get the total of goals
                                    SELECT  @totalGoals=SUM(ISNULL(CA.CRLimit,0))
                                    FROM dbo.CustomerAccountTbl CA
                                    INNER JOIN #temp T ON CA.RefContactID=T.ContactID
                                    WHERE CA.PayType=1 AND ISNULL(CA.RefCAID,0)!=0 AND T.Number=@i
				
                                    UPDATE #temp SET totalGoals= ISNULL(@totalGoals,0) WHERE Number=@i
                                    --end:Get the total of goals

									 --begin:Get the Avaliable To Spend
									SELECT @totalDR= SUM(ISNULL(GL.Amount,0)) FROM {DapperHelper.LedgerDbName}[General Ledger] GL 
									INNER JOIN #temp T ON T.CAID = GL.CAID
									WHERE T.Number=@i AND GL.Sign='DR'

									UPDATE T SET T.AvailableToSpend= ISNULL(CA.CRLimit,0)- ISNULL(@totalDR,0)
                                    FROM dbo.CustomerAccountTbl CA
                                    INNER JOIN #temp T ON CA.CAID=T.CAID
                                    WHERE T.Number=@i
                                    --end:Get the Avaliable To Spend

									 --begin:Get the allowance
									SELECT TOP 1 @totalAllow= ISNULL(WT.Amount,0) FROM Wallet_TransactionTbl WT 
									INNER JOIN #temp T ON T.CAID = WT.ToCAID
                                    INNER JOIN dbo.AllowanceRetentionDetailsTbl ARD ON ARD.AllowanceTranId= WT.TranId 
									WHERE T.Number=@i AND WT.Active=1 AND WT.TypeId=1 AND ARD.Active=1 AND ARD.processed=0 AND CONVERT(DATE,ARD.NextDate,103)>=CONVERT(DATE,GETDATE(),103) ORDER BY ARD.NextDate 
			
                                    SELECT TOP 1 @allowDaysNo= DATEDIFF(DAY, GETDATE(),ALD.NextDate) FROM AllowanceRetentionDetailsTbl ALD
                                    INNER JOIN dbo.Wallet_TransactionTbl WT ON WT.TranId = ALD.AllowanceTranId
                                    INNER JOIN #temp T ON T.CAID = WT.ToCAID
                                    WHERE WT.TypeId = 1 AND ALD.Active = 1 AND WT.Active = 1 AND ALD.processed=0 AND T.Number=@i AND CONVERT(DATE,ALD.NextDate,103)>=CONVERT(DATE,GETDATE(),103) AND ALD.NoOfTrials=0 ORDER BY ALD.NextDate  

									UPDATE T SET T.allowance= ISNULL(@totalAllow,0),T.allowanceDaysNo= ISNULL(@allowDaysNo,0)
                                    FROM dbo.CustomerAccountTbl CA
                                    INNER JOIN #temp T ON CA.CAID=T.CAID
                                    WHERE T.Number=@i
                                    --end:Get the allowance

                                     --begin:Get card class design
									SELECT @classDesign= ISNULL(CST.SpecValue,'')
                                    FROM dbo.ContactSpecificationsTbl CST
                                    INNER JOIN #temp T ON CST.ContactId=T.ContactId
                                    WHERE T.Number=@i AND CST.SpecKey='CardDesign'
                                    
                                    IF(ISNULL(@classDesign,'')='') BEGIN  SET @classDesign ='child-default-card' END
                                    UPDATE #temp SET CardClass=@classDesign WHERE Number=@i;
                                    --end:Get card class design
                                    
                                    --begin:Get SymbolsMoneyControl AND SymbolsMoney
                                    UPDATE T  SET isSymbolMoney= CASE WHEN ISNULL(CST.SpecValue,'')='true' THEN  1 ELSE 0 END
									FROM dbo.ContactSpecificationsTbl CST
                                    INNER JOIN #temp T ON CST.ContactId=T.ContactId
                                    WHERE T.Number=@i AND CST.SpecKey IN ('SymbolsMoneyControl')

									 UPDATE T  SET SymbolValue=  ISNULL(CST.SpecValue,0)
									FROM dbo.ContactSpecificationsTbl CST
                                    INNER JOIN #temp T ON CST.ContactId=T.ContactId
                                    WHERE T.Number=@i AND CST.SpecKey IN ('SymbolsMoney')
                                    --end:Get card class design


                                    --begin:Get ParentCPR
                                    SELECT @ParentCPR=C.Cont_CPR FROM dbo.ContactTbl C 
                                    INNER JOIN dbo.ContactTbl CT ON CT.Parent = C.ContactID
                                    INNER JOIN #temp T ON CT.ContactId=T.ContactId
                                    WHERE T.Number=@i
                                    UPDATE #temp SET ParentCPR=@ParentCPR WHERE Number=@i;
                                    --end:Get ParentCPR

                                    Set @i = @i + 1  
                                    END
                                    --------------------------------------- END: LOOP TO GET THE TOTAL SAVINGS & SPENT ----------------------------------
                                    SELECT * FROM #temp
                                ", new
            {
                ContactID = contactId
            });
            var childAccounts = childAccountsQuery.ToList();

            return childAccounts;
        }
        public async Task<List<T>> GetChildAccounts<T>(int parentContactId)
        {
            var accounts = await M_GetChildAccounts<T>(parentContactId, 0, false);
            return accounts;
        }
        public async Task<List<ChildAccountDetails>> GetChildAccountsDetails(int parentContactId)
        {
            var childAccountDetails = await GetChildAccounts<ChildAccountDetails>(parentContactId);

            foreach (var item in childAccountDetails)
            {
                item.GoalsAccounts = await GetGoalsAccounts(item.ContactID);
            }

            return childAccountDetails;
        }
        public async Task<List<GoalAccount>> GetGoalsAccounts(int ContactId, int paytype = 1, int PaySubType = 0)
        {
            return await M_GetGoalsAccounts(ContactId + "", -1 + "", paytype: paytype, PaySubType);
        }
        public async Task<List<GoalAccount>> M_GetGoalsAccounts(string ContactIds, string CAIDs, int paytype = 1, int PaySubType = 0)
        {
            var whereSTMT = ContactIds == "-1" || string.IsNullOrWhiteSpace(ContactIds) ? $"CA.CAID IN({CAIDs}) " : $"CA.RefContactID IN({ContactIds})";
            var GoalsAccountsQuery = await DapperHelper.QueryAsync<GoalAccount>($@"
                SELECT CA.RefContactId,CA.CAID AS [CAID], CA.CustAcNameE AS [Name], ISNULL(CA.StatementBalance, 0) AS [Balance], CF.CcyCode AS [CcyCode], CF.SwiftCode AS [SwiftCode], CF.CcyName AS [CcyName], CF.[Decimal] AS [NoOfDecimals] 
                ,CA.EffectiveDate AS [StartDate],CA.CABillingDate AS [DeadLine],CA.CRLimit AS [Limit],CA.RetentionChippingAmt,ISNULL(CA.RetentionFrequency,1) RetentionFrequency,CA.Photo,CA.RefCAID,CASE WHEN ISNUMERIC(CA.PanNo)=1 THEN CA.PanNo ELSE 0 END Sort               
                FROM dbo.CustomerAccountTbl CA
                INNER JOIN dbo.CustAcctSetupTbl CAS ON CAS.CustActId = CA.CustActId AND CAS.Default_ = 1
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON AF.AccountNo = CA.CAAccountNo
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy
                WHERE {whereSTMT} AND PayType={paytype} {(PaySubType > 0 ? $" AND CA.PaySubType={PaySubType}" : "")} ", new
            {
                ContactID = ContactIds,
                CAID = CAIDs
            });
            var GoalsAccounts = GoalsAccountsQuery.OrderBy(s => s.sort).ToList();

            GoalsAccounts.Where(c => c.RetentionFrequency == WalletRetention.DAILY).ToList().ForEach(cc => cc.RetentionFrequencyTitle = "Daily");
            GoalsAccounts.Where(c => c.RetentionFrequency == WalletRetention.WEEKLY).ToList().ForEach(cc => cc.RetentionFrequencyTitle = "Weekly");
            GoalsAccounts.Where(c => c.RetentionFrequency == WalletRetention.MONTHLY).ToList().ForEach(cc => cc.RetentionFrequencyTitle = "Monthly");

            GoalsAccounts.ForEach(a => a.EncryptedCAID = (a.CAID + "").Encrypt("CAID"));
            return GoalsAccounts;

        }        
        public async Task<List<MenuViewModel>> GetWalletMenu(int ContactID, ICardRepository cardRepository)
        {
            var SideMenuQuery =
                await DapperHelper.QueryAsync<MenuViewModel>(
                    $" SELECT O.OID AS 'Id',{LanguageHelper.GetSelectStmt("O.EngName", "Title")},O.ObjectName AS 'Url', O.[Version] AS 'ParentId', O.IconClass AS 'Icon' ,PS.ProductId " +
                    " FROM dbo.ObjectsTbl O" +
                    " LEFT JOIN dbo.ProductServiceTbl PS ON PS.OID = O.OID" +
                    LanguageHelper.GetLeftJoin("ObjectsTbl", "EngName", "O.OID") +
                    " WHERE O.SysID = 50 AND O.[ObjID] = 9 AND O.Show = 1  AND O.[Version]= 500109" +
                    " ORDER BY O.Menu, O.SubMenu1, O.SubMenu2",
                    new
                    {
                        UID = ContactID + ""
                    }
                );
            var SideMenu = SideMenuQuery.ToList();

            SideMenu.Where(r => new List<int> { 500114, 500115, 500112 }.Contains(r.Id)).ToList().ForEach(cc => cc.isComingSoon = true);

            var merchantWalletAccounts = await cardRepository.GetWalletAccounts(ContactID, -1, 2);

            if (merchantWalletAccounts.Count <= 0)
            {
                SideMenu = SideMenu.Where(s => s.Id != 500113).ToList();
            }

            return SideMenu;
        }
        public async Task<ChildAccount> GetSingleAccount<T>(int contactId, int CAID)
        {
            var accountQuery = await M_GetAccounts<ChildAccount>(0, contactId, true, CAID);
            var account = accountQuery.FirstOrDefault();
            return account;
        }
        private async Task<List<T>> M_GetAccounts<T>(int parentContactId, int childContactId, bool isSingleChild, int CAID)
        {
            //TODO:CONVERT THE SQL TO PROCEDURE IF IT IS NEED

            //Check the request is it for one child or multi by parent contactId
            var contactId = isSingleChild ? childContactId : parentContactId;
            var inContactStmt = isSingleChild ? "@ContactID" : "SELECT ContactId FROM dbo.ContactRelativeRelationTbl WHERE RefContactId=@ContactID";

            var childAccountsQuery = await DapperHelper.QueryAsync<T>($@"SET DATEFORMAT DMY;
                                    --------------------------------------- BEGIN: GET CHILD ACCOUNT ----------------------------------
                                    SELECT CA.StatusID,CT.ContactPhoto Photo,CT.HasCard,CT.CONT_CPR,CT.ContactID,CASE WHEN CT.TitleID=4 AND ISNULL(CT.Title,'')!='' THEN CT.Title ELSE CT.ContactEng END AS [Name],CA.CAID AS [CAID], CA.CustAcNameE, ISNULL(CA.StatementBalance, 0) AS [Balance], CF.CcyCode AS [CcyCode], CF.SwiftCode AS [SwiftCode], CF.CcyName AS [CcyName], CF.[Decimal] AS [NoOfDecimals] 
                                    ,cast(NULL as Money) totalSavings,CT.ContactPhoto,cast(NULL as MONEY) totalGoals ,0 AvailableToSpend,ISNULL(CA.Spent, 0) AS Spent, ISNULL(CA.CRLimit,0) AS [Limit],ISNULL(CA.Hold,0) AS [Hold],cast(NULL as Money)  allowance ,0 allowanceDaysNo,0 ParentCPR,cast(0 as BIT) isSymbolMoney,cast(NULL as Money) SymbolValue
                                    /*,cast(NULL as Money) completedtaskToBePaidAmount,cast(NULL as int) completedtaskToBePaidCount*/
                                    , Number = ROW_NUMBER() OVER (ORDER BY CT.ContactID)                 
                                    INTO #temp
                                    FROM dbo.CustomerAccountTbl CA
                                    INNER JOIN dbo.CustAcctSetupTbl CAS ON CAS.CustActId = CA.CustActId 
                                    INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON AF.AccountNo = CA.CAAccountNo
                                    INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy
                                    INNER JOIN dbo.ContactTbl CT ON CT.ContactID = CA.RefContactID
                                    WHERE CT.ContactId IN({inContactStmt}) AND CT.Active=1 AND ISNULL(CA.PayType,0) =0 AND ca.CAID={CAID}
                                    
                                    ALTER TABLE #temp ADD CardClass NVARCHAR(50);
                                    --------------------------------------- END: GET CHILD ACCOUNT ----------------------------------

                                    --------------------------------------- BEGIN: LOOP TO GET THE TOTAL SAVINGS & SPENT ----------------------------------
                                    DECLARE @Count int  
                                    Declare @i int  
                                    Set @i = 1  
                                    Select @Count = Count(*) from #Temp  

                                    DECLARE @allowDaysNo INT;
                                    DECLARE @totalGoals MONEY;
                                    DECLARE @totalSaving MONEY; 
                                    DECLARE @totalAllow MONEY;
                                    DECLARE @totalDR MONEY; 
                                    DECLARE @spendAvailable MONEY; 
                                    DECLARE @classDesign NVARCHAR(50);
                                    DECLARE @ParentCPR NVARCHAR(50);

                                    WHILE @i<= @Count  
                                    BEGIN
                                    SET @allowDaysNo=0;
                                    SET @totalGoals =0;
                                    SET @totalAllow =0;
                                    SET @totalDR=0;
                                    SET @totalSaving=0;
                                    SET @spendAvailable=0;
                                    SET @classDesign=''; 
                                    SET @ParentCPR='';

                                    --begin:Get the total Saving
                                    SELECT @totalSaving=SUM(ISNULL(CA.StatementBalance, 0))
                                    FROM dbo.CustomerAccountTbl CA
                                    INNER JOIN #temp T ON CA.RefContactID=T.ContactID
                                    WHERE CA.PayType=1 AND ISNULL(CA.RefCAID,0)!=0 AND T.Number=@i
									
									UPDATE #temp SET totalSavings= ISNULL(@totalSaving,0) WHERE Number=@i
                                    --end:Get the the total Saving
                                
                                    --begin:Get the total of goals
                                    SELECT  @totalGoals=SUM(ISNULL(CA.CRLimit,0))
                                    FROM dbo.CustomerAccountTbl CA
                                    INNER JOIN #temp T ON CA.RefContactID=T.ContactID
                                    WHERE CA.PayType=1 AND ISNULL(CA.RefCAID,0)!=0 AND T.Number=@i
				
                                    UPDATE #temp SET totalGoals= ISNULL(@totalGoals,0) WHERE Number=@i
                                    --end:Get the total of goals

									 --begin:Get the Avaliable To Spend
									SELECT @totalDR= SUM(ISNULL(GL.Amount,0)) FROM {DapperHelper.LedgerDbName}[General Ledger] GL 
									INNER JOIN #temp T ON T.CAID = GL.CAID
									WHERE T.Number=@i AND GL.Sign='DR'

									UPDATE T SET T.AvailableToSpend= ISNULL(CA.CRLimit,0)- ISNULL(@totalDR,0)
                                    FROM dbo.CustomerAccountTbl CA
                                    INNER JOIN #temp T ON CA.CAID=T.CAID
                                    WHERE T.Number=@i
                                    --end:Get the Avaliable To Spend

									 --begin:Get the allowance
									SELECT TOP 1 @totalAllow= ISNULL(WT.Amount,0) FROM Wallet_TransactionTbl WT 
									INNER JOIN #temp T ON T.CAID = WT.ToCAID
                                    INNER JOIN dbo.AllowanceRetentionDetailsTbl ARD ON ARD.AllowanceTranId= WT.TranId 
									WHERE T.Number=@i AND WT.Active=1 AND WT.TypeId=1 AND ARD.Active=1 AND ARD.processed=0 AND CONVERT(DATE,ARD.NextDate,103)>=CONVERT(DATE,GETDATE(),103) ORDER BY ARD.NextDate 
			
                                    SELECT TOP 1 @allowDaysNo= DATEDIFF(DAY, GETDATE(),ALD.NextDate) FROM AllowanceRetentionDetailsTbl ALD
                                    INNER JOIN dbo.Wallet_TransactionTbl WT ON WT.TranId = ALD.AllowanceTranId
                                    INNER JOIN #temp T ON T.CAID = WT.ToCAID
                                    WHERE WT.TypeId = 1 AND ALD.Active = 1 AND WT.Active = 1 AND ALD.processed=0 AND T.Number=@i AND CONVERT(DATE,ALD.NextDate,103)>=CONVERT(DATE,GETDATE(),103) AND ALD.NoOfTrials=0 ORDER BY ALD.NextDate  

									UPDATE T SET T.allowance= ISNULL(@totalAllow,0),T.allowanceDaysNo= ISNULL(@allowDaysNo,0)
                                    FROM dbo.CustomerAccountTbl CA
                                    INNER JOIN #temp T ON CA.CAID=T.CAID
                                    WHERE T.Number=@i
                                    --end:Get the allowance

                                     --begin:Get card class design
									SELECT @classDesign= ISNULL(CST.SpecValue,'')
                                    FROM dbo.ContactSpecificationsTbl CST
                                    INNER JOIN #temp T ON CST.ContactId=T.ContactId
                                    WHERE T.Number=@i AND CST.SpecKey='CardDesign'
                                    
                                    IF(ISNULL(@classDesign,'')='') BEGIN  SET @classDesign ='child-default-card' END
                                    UPDATE #temp SET CardClass=@classDesign WHERE Number=@i;
                                    --end:Get card class design
                                    
                                    --begin:Get SymbolsMoneyControl AND SymbolsMoney
                                    UPDATE T  SET isSymbolMoney= CASE WHEN ISNULL(CST.SpecValue,'')='true' THEN  1 ELSE 0 END
									FROM dbo.ContactSpecificationsTbl CST
                                    INNER JOIN #temp T ON CST.ContactId=T.ContactId
                                    WHERE T.Number=@i AND CST.SpecKey IN ('SymbolsMoneyControl')

									 UPDATE T  SET SymbolValue=  ISNULL(CST.SpecValue,0)
									FROM dbo.ContactSpecificationsTbl CST
                                    INNER JOIN #temp T ON CST.ContactId=T.ContactId
                                    WHERE T.Number=@i AND CST.SpecKey IN ('SymbolsMoney')
                                    --end:Get card class design


                                    --begin:Get ParentCPR
                                    SELECT @ParentCPR=C.Cont_CPR FROM dbo.ContactTbl C 
                                    INNER JOIN dbo.ContactTbl CT ON CT.Parent = C.ContactID
                                    INNER JOIN #temp T ON CT.ContactId=T.ContactId
                                    WHERE T.Number=@i
                                    UPDATE #temp SET ParentCPR=@ParentCPR WHERE Number=@i;
                                    --end:Get ParentCPR

                                    Set @i = @i + 1  
                                    END
                                    --------------------------------------- END: LOOP TO GET THE TOTAL SAVINGS & SPENT ----------------------------------
                                    SELECT * FROM #temp
                                ", new
            {
                ContactID = contactId
            });

            var childAccounts = childAccountsQuery.ToList();

            return childAccounts;
        }

        public async Task<MerchantData> GetMerchantDataAsync(int CustomerID)
        {
            return (await DapperHelper.QueryAsync<MerchantData>(
              " SELECT CF.BankName,C.CityE City,ACT.ISO18245 " +
              " FROM dbo.CustomerFile CF " +
              " INNER JOIN dbo.CityTbl C ON C.CityID = CF.CityID " +
              " INNER JOIN dbo.ARCustomerTypeTbl ACT ON ACT.CustomerTypeId= CF.CustomerTypeID " +
              " WHERE CF.RecId=@RecId ",
              new
              {
                  RecId = CustomerID
              })).FirstOrDefault();
        }
    }
}
