using System;
using System.Collections.Generic;

namespace Optimum.Wallet.Domain.Entities
{
    public class ChooseListDetailsTbl
    {
        public int CLDtlId { get; set; }
        public int? PageId { get; set; }
        public int? TableId { get; set; }
        public int? CLCatId { get; set; }
        public string CLIId { get; set; }
        public int? SortCode { get; set; }
        public bool? Active { get; set; }
        public string DataID { get; set; }
        public int? TicktTranID { get; set; }
        public int? CustTicktID { get; set; }
    }
}
