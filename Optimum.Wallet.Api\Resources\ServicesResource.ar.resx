﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Card" xml:space="preserve">
    <value>البطاقة</value>
  </data>
  <data name="ContactBody" xml:space="preserve">
    <value>&lt;p&gt;إذا كان لديك المزيد من الاستفسارات ، يرجى الاتصال بنا. نحن في خدمتكم.&lt;/p&gt;
                    &lt;p class="contact-information"&gt;
                        &lt;strong class="color-theme font-16"&gt;ساعات العمل&lt;/strong&gt;
                        &lt;br&gt; الأحد إلى الخميس 08:00 الى 17:00
                    &lt;/p&gt;</value>
  </data>
  <data name="ErrorPayInsertPaymentDetails" xml:space="preserve">
    <value>حدث خطأ أثناء إدخال البيانات. الرجاء المحاولة مرة أخرى.</value>
  </data>
  <data name="ErrorPayProcessTranContactUs" xml:space="preserve">
    <value>فشل في معالجة المعاملة. يرجى التواصل بنا للإستفسار عن معاملتك.</value>
  </data>
  <data name="ErrorPayRetrievePaymentDetails" xml:space="preserve">
    <value>حدث خطأ اثناء محاولة الحصول على البايانات بعملية الدفع. الرجاء المحاولة مرة أخرى.</value>
  </data>
  <data name="ErrorPayRetrieveTranDetailsContactUs" xml:space="preserve">
    <value>فشل في جلب البيانات. يرجى التواصل بنا للإستفسار عن معاملتك.</value>
  </data>
  <data name="ErrorPayRetrieveTranRecordContactUs" xml:space="preserve">
    <value>فشل في جلب سجل المعاملة. يرجى التواصل بنا للإستفسار عن معاملتك.</value>
  </data>
  <data name="ErrorPayRetrieveTransactionIdentifier" xml:space="preserve">
    <value>فشل في استعادة معرف معاملتك. يرجى التواصل بنا للاستفسار عن معاملتك.</value>
  </data>
  <data name="ErrorPaySubmitPaymentDetails" xml:space="preserve">
    <value>حدث خطأ أثناء إدخال البيانات. الرجاء المحاولة مرة أخرى.</value>
  </data>
  <data name="ExceedMaximumDailyTransferLimitError" xml:space="preserve">
    <value>لقد تجاوزت حد التحويل اليومي الأقصى البالغ {0}. يمكنك فقط نقل {1} لهذا اليوم.</value>
    <comment>{0}{1}</comment>
  </data>
  <data name="InactiveUserError" xml:space="preserve">
    <value>نفذت العملية مع مستخدم غير نشط.</value>
  </data>
  <data name="InsufficientCardBalance" xml:space="preserve">
    <value>لديك رصيد بطاقة غير كاف.</value>
  </data>
  <data name="InsufficientWalletBalance" xml:space="preserve">
    <value>لديك رصيد محفظة غير كاف.</value>
  </data>
  <data name="InvalidAmountError" xml:space="preserve">
    <value>المبلغ المحدد غير صحيح.</value>
  </data>
  <data name="InvalidRecipientError" xml:space="preserve">
    <value>لقد أدخلت تفاصيل مستلم غير صحيحة.</value>
  </data>
  <data name="LimitReachError" xml:space="preserve">
    <value>لقد وصلت إلى حد المعاملات {0} في اليوم.</value>
    <comment>{0}</comment>
  </data>
  <data name="MoneyTransfer" xml:space="preserve">
    <value>حوالة مالية</value>
  </data>
  <data name="MoneyTransferRegisteration" xml:space="preserve">
    <value>التسجيل في خدمة تحويل الأموال</value>
  </data>
  <data name="MonTransfer0%VAT" xml:space="preserve">
    <value>0٪ ضريبة القيمة المضافة</value>
  </data>
  <data name="MonTransferAccount/IBANNo" xml:space="preserve">
    <value>رقم الحساب / IBAN</value>
  </data>
  <data name="MonTransferAccountNo" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="MonTransferACNumbera" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="MonTransferAddress" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="MonTransferAgentName" xml:space="preserve">
    <value>اسم الوكيل</value>
  </data>
  <data name="MonTransferAmountAfterVAT" xml:space="preserve">
    <value>المبلغ بعد الضريبة</value>
  </data>
  <data name="MonTransferAmountBeforeVAT" xml:space="preserve">
    <value>المبلغ قبل الضريبة</value>
  </data>
  <data name="MonTransferBank" xml:space="preserve">
    <value>البنك</value>
  </data>
  <data name="MonTransferBankCode" xml:space="preserve">
    <value>الرمز البنكي</value>
  </data>
  <data name="MonTransferBenBankName" xml:space="preserve">
    <value>اسم البنك</value>
  </data>
  <data name="MonTransferBeneficiary" xml:space="preserve">
    <value>المستفيد</value>
  </data>
  <data name="MonTransferBeneficiaryDetails" xml:space="preserve">
    <value>تفاصيل المستفيد</value>
  </data>
  <data name="MonTransferBeneficiaryName" xml:space="preserve">
    <value>أسم المستفيد</value>
  </data>
  <data name="MonTransferBHD" xml:space="preserve">
    <value>د.ب</value>
  </data>
  <data name="MonTransferBranch" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="MonTransferBranchCode" xml:space="preserve">
    <value>رمز الفرع</value>
  </data>
  <data name="MonTransferBranchName" xml:space="preserve">
    <value>اسم الفرع</value>
  </data>
  <data name="MonTransferCardNo" xml:space="preserve">
    <value>رقم البطاقة</value>
  </data>
  <data name="MonTransferCashPickup" xml:space="preserve">
    <value>نقدا</value>
  </data>
  <data name="MonTransferCharge" xml:space="preserve">
    <value>الرسوم</value>
  </data>
  <data name="MonTransferContactPerson" xml:space="preserve">
    <value>الشخص الذي يمكن الاتصال به</value>
  </data>
  <data name="MonTransferCountry" xml:space="preserve">
    <value>البلد</value>
  </data>
  <data name="MonTransferCreditToAccount" xml:space="preserve">
    <value>تحويل للحساب</value>
  </data>
  <data name="MonTransferCurrency" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="MonTransferCustomerAddress" xml:space="preserve">
    <value>عنوان العميل</value>
  </data>
  <data name="MonTransferCustomerDetails" xml:space="preserve">
    <value>تفاصيل العميل</value>
  </data>
  <data name="MonTransferCustomerName" xml:space="preserve">
    <value>اسم العميل</value>
  </data>
  <data name="MonTransferDestination" xml:space="preserve">
    <value>المكان المقصود</value>
  </data>
  <data name="MonTransferFundsource" xml:space="preserve">
    <value>المصدر</value>
  </data>
  <data name="MonTransferFXAmount" xml:space="preserve">
    <value>مبلغ صرف العملة</value>
  </data>
  <data name="MonTransferIDNumber" xml:space="preserve">
    <value>رقم الهوية</value>
  </data>
  <data name="MonTransferLocalAmount" xml:space="preserve">
    <value>المبلغ المحلي</value>
  </data>
  <data name="MonTransferLocalAmountBHD" xml:space="preserve">
    <value>المبلغ المحلي (د.ب)</value>
  </data>
  <data name="MonTransferPaymentDetails" xml:space="preserve">
    <value>بيانات الدفع</value>
  </data>
  <data name="MonTransferPaymentOption" xml:space="preserve">
    <value>خيار الدفع</value>
  </data>
  <data name="MonTransferProductDetails" xml:space="preserve">
    <value>تفاصيل المنتج</value>
  </data>
  <data name="MonTransferProductIssuerNumber" xml:space="preserve">
    <value>رقم مصدر المنتج</value>
  </data>
  <data name="MonTransferPurpose" xml:space="preserve">
    <value>الغرض </value>
  </data>
  <data name="MonTransferQouteFees" xml:space="preserve">
    <value>الرسوم</value>
  </data>
  <data name="MonTransferQouteFeesWithoutVAT" xml:space="preserve">
    <value>الرسوم بدون الضريبة</value>
  </data>
  <data name="MonTransferQouteFeeswithVAT" xml:space="preserve">
    <value>الرسوم مع الضريبة</value>
  </data>
  <data name="MonTransferQouteRate" xml:space="preserve">
    <value>النسبة</value>
  </data>
  <data name="MonTransferQouteSendAmount" xml:space="preserve">
    <value>المبلغ المرسل</value>
  </data>
  <data name="MonTransferQouteSubsidy" xml:space="preserve">
    <value>الدعم</value>
  </data>
  <data name="MonTransferQouteTotalToPay" xml:space="preserve">
    <value>المجموع للدفع</value>
  </data>
  <data name="MonTransferQouteVAT" xml:space="preserve">
    <value>الضريبة</value>
  </data>
  <data name="MonTransferRate" xml:space="preserve">
    <value>المعدل</value>
  </data>
  <data name="MonTransferRecipientName" xml:space="preserve">
    <value>اسم المستلم</value>
  </data>
  <data name="MonTransferSECNO" xml:space="preserve">
    <value>رقم التسلسل</value>
  </data>
  <data name="MonTransferSelectBeneficiary" xml:space="preserve">
    <value>اختر المستفيد</value>
  </data>
  <data name="MonTransferSelectCard" xml:space="preserve">
    <value>اختر بطاقة</value>
  </data>
  <data name="MonTransferSelectMethod" xml:space="preserve">
    <value>اختر طريقة التحويل</value>
  </data>
  <data name="MonTransferSelectPayOption" xml:space="preserve">
    <value>اختر خيار الدفع</value>
  </data>
  <data name="MonTransferSelectPayOptionCard" xml:space="preserve">
    <value>بطاقات الدفع</value>
  </data>
  <data name="MonTransferSelectPayOptionDebit" xml:space="preserve">
    <value>بطاقة الصراف الآلي</value>
  </data>
  <data name="MonTransferSelectPayOptionWallet" xml:space="preserve">
    <value>المحفظة</value>
  </data>
  <data name="MonTransferSelectPurpose" xml:space="preserve">
    <value>اختر الغرض</value>
  </data>
  <data name="MonTransferSelectSource" xml:space="preserve">
    <value>اختر المصدر</value>
  </data>
  <data name="MonTransferSource" xml:space="preserve">
    <value>المصدر</value>
  </data>
  <data name="MonTransferSubsidy" xml:space="preserve">
    <value>دعم مالي</value>
  </data>
  <data name="MonTransferTaxInvoiceNo" xml:space="preserve">
    <value>رقم الفاتورة الضريبية</value>
  </data>
  <data name="MonTransferTelephoneNo" xml:space="preserve">
    <value>رقم الهاتف</value>
  </data>
  <data name="MonTransferTelephoneNumber" xml:space="preserve">
    <value>رقم الهاتف</value>
  </data>
  <data name="MonTransferTotalAmount" xml:space="preserve">
    <value>المبلغ الإجمالي</value>
  </data>
  <data name="MonTransferTransactionDate" xml:space="preserve">
    <value>تاريخ المعاملة</value>
  </data>
  <data name="MonTransferTransactionDetails" xml:space="preserve">
    <value>تفاصيل المعاملة</value>
  </data>
  <data name="MonTransferTransactionReference's" xml:space="preserve">
    <value>مرجع المعاملة</value>
  </data>
  <data name="MonTransferTransactionType" xml:space="preserve">
    <value>نوع المعاملة</value>
  </data>
  <data name="MonTransferTransferType" xml:space="preserve">
    <value>طريقة التحويل</value>
  </data>
  <data name="NotMatchInOurRecordError" xml:space="preserve">
    <value>المعلومات التي قدمتها لا تتطابق مع أي حساب في سجلاتنا.</value>
  </data>
  <data name="PageTitleMyCards" xml:space="preserve">
    <value>بطاقاتي</value>
  </data>
  <data name="PageTitleMyWallet" xml:space="preserve">
    <value>محفظتي</value>
  </data>
  <data name="PayApprovedMsg" xml:space="preserve">
    <value>شكرا لك على دفع {0}.</value>
    <comment>{0}</comment>
  </data>
  <data name="PayApprovedStatus" xml:space="preserve">
    <value>تم اعتماد الدفع</value>
  </data>
  <data name="PayAuthorizationNumber" xml:space="preserve">
    <value>رقم الترخيص</value>
  </data>
  <data name="PayDeclinedMsg" xml:space="preserve">
    <value>تم رفض معاملتك! يرجى الرجوع إلى التفاصيل أدناه.</value>
  </data>
  <data name="PayDeclinedStatus" xml:space="preserve">
    <value>تم رفض الدفع</value>
  </data>
  <data name="PayGatewayProcessedMsg" xml:space="preserve">
    <value>لقد تم معالجة عملية الدفع عن طريق بوابة الدفع. يمكن الإطلاع على تفاصيل العملية في الأسفل.</value>
  </data>
  <data name="PaymentStatus" xml:space="preserve">
    <value>حالة الدفع</value>
  </data>
  <data name="PayReferenceNumber" xml:space="preserve">
    <value>رقم المرجع</value>
  </data>
  <data name="PayResponseMessage" xml:space="preserve">
    <value>الرسالة</value>
  </data>
  <data name="PayTransactionNumber" xml:space="preserve">
    <value>رقم المعاملة</value>
  </data>
  <data name="ReachMaximumDailyTransferLimitError" xml:space="preserve">
    <value>لقد وصلت إلى الحد الأقصى للنقل اليومي الخاص بك وهو {0}.</value>
    <comment>{0}</comment>
  </data>
  <data name="RegisterNow" xml:space="preserve">
    <value>سجل اللآن</value>
  </data>
  <data name="RequestFromYourSelfError" xml:space="preserve">
    <value>لا يمكنك طلب المال من نفسك.</value>
  </data>
  <data name="RequestMoreThanError" xml:space="preserve">
    <value>لا يمكنك طلب أكثر من {0} في معاملة واحدة.</value>
    <comment>{0}</comment>
  </data>
  <data name="SenViewModelYourSelfError" xml:space="preserve">
    <value>لا يمكنك إرسال الأموال إلى نفسك.</value>
  </data>
  <data name="SmartMoneyCPRExpired" xml:space="preserve">
    <value>بطاقة الهوية الخاصة بك منتهية، الرجاء توفير نسخة حديثة قبل إتمام عملية التسجيل.</value>
  </data>
  <data name="SmartMoneyNotRegistered" xml:space="preserve">
    <value>أنت غير مسجل في خدمة الأموال الذكية</value>
  </data>
  <data name="SmartMoneyRegisteration" xml:space="preserve">
    <value>التسحيل للأموال الذكية</value>
  </data>
  <data name="Statement" xml:space="preserve">
    <value>كشف حساب</value>
  </data>
  <data name="StmtBillingAmount" xml:space="preserve">
    <value>مبلغ الفاتورة</value>
  </data>
  <data name="StmtChooseStatement" xml:space="preserve">
    <value>من فضلك اختر خيار</value>
  </data>
  <data name="StmtClosingBalance" xml:space="preserve">
    <value>الرصيد الختامي</value>
  </data>
  <data name="StmtCurrentMonthTransactions" xml:space="preserve">
    <value>كشف الشهر الحالي</value>
  </data>
  <data name="StmtDescription" xml:space="preserve">
    <value>الوصف</value>
  </data>
  <data name="StmtDueDate" xml:space="preserve">
    <value>تاريخ الاستحقاق</value>
  </data>
  <data name="StmtExportAs" xml:space="preserve">
    <value>إظهار بصيغة</value>
  </data>
  <data name="StmtMinimumPayment" xml:space="preserve">
    <value>الحد الأدنى للدفع</value>
  </data>
  <data name="StmtPdfBtnText" xml:space="preserve">
    <value>بي دي اف</value>
  </data>
  <data name="StmtPostingDate" xml:space="preserve">
    <value>تاريخ النشر</value>
  </data>
  <data name="StmtPreviousMonthsStatements" xml:space="preserve">
    <value>كشوفات الأشهر السابقة</value>
  </data>
  <data name="StmtTotalCredits" xml:space="preserve">
    <value>مجموع الدائن</value>
  </data>
  <data name="StmtTotalDebit" xml:space="preserve">
    <value>مجموع المدين</value>
  </data>
  <data name="StmtTransactionAmount" xml:space="preserve">
    <value>مبلغ المعاملة</value>
  </data>
  <data name="StmtTransactionDate" xml:space="preserve">
    <value>تاريخ المعاملة</value>
  </data>
  <data name="StmtWallet" xml:space="preserve">
    <value>محفظة</value>
  </data>
  <data name="StopCardFail" xml:space="preserve">
    <value>لم نتمكن من إيقاف البطاقة. الرجاء معاودة المحاولة في وقت لاحق</value>
  </data>
  <data name="StopCardSuccess" xml:space="preserve">
    <value>تم إلغاء بطاقتك {0}، لطلب الاستبدال ، يرجى الاتصال بخدمة عملاء BFC Payments على 1771 1775</value>
  </data>
  <data name="StopCardSuccessWithNumber" xml:space="preserve">
    <value>تم إلغاء بطاقتك {0}، لطلب الاستبدال ، يرجى الاتصال بخدمة عملاء BFC Payments على 1771 1775</value>
  </data>
  <data name="SuccessSmartMoneyRegister" xml:space="preserve">
    <value>تم التسحيل لخدمة الأموال الذكية بنجاح</value>
  </data>
  <data name="TransactionProcessedFail" xml:space="preserve">
    <value>فشل في إرسال تفاصيل معاملتك! الرجاء معاودة المحاولة في وقت لاحق.</value>
  </data>
  <data name="TransactionProcessedSuccess" xml:space="preserve">
    <value>تمت المعاملة بنجاح</value>
  </data>
  <data name="TransactionRejected" xml:space="preserve">
    <value>تم رفض معاملتك</value>
  </data>
  <data name="TransferButton" xml:space="preserve">
    <value>تحويل</value>
  </data>
  <data name="TransferButtonLoading" xml:space="preserve">
    <value>جاري التحويل</value>
  </data>
  <data name="TransferMoreThanError" xml:space="preserve">
    <value>لا يمكنك نقل أكثر من {0} في معاملة واحدة.</value>
    <comment>{0}</comment>
  </data>
</root>