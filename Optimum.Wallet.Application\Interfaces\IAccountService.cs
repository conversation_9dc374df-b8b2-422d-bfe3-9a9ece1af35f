using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Optimum.Wallet.Domain.Entities;

namespace Optimum.Wallet.Application.Interfaces
{
    public interface IAccountService
    {
        public Task<Tuple<bool, ContactTbl, string>> ValidateCustomUser(string username, string password, string message, bool isFingerprintLogin = false, string currentDeviceId = "", bool isMallatsLogin = false);
        public Task<string> GenerateJWTTokenSignIn(string username, bool isMobile = false, HttpContext httpContext = null);
        public Task<string> GetRefreshToken(string username, string token, int ContactID);
        public Task<ContactTbl> GetContact(string Username, string MobileNumber = "");
        public Task<Tuple<bool, string>> SetUserLogin(string cpr, string exception, bool enableAccount = false, string password = "");
        public Task<bool> ResetUserPasswordByCpr(string cpr, string newPassword);
        public Task<bool> ResetUserPasswordByMobile(string mobile, string newPassword);
        public Task<Tuple<string, string, string>> CreatePasswordHash(string password);
        public ClaimsPrincipal ValidateJwtToken(string token, bool validatelifetime = false);
        public Task<Tuple<bool, string>> ChangePassword(string username, string oldPassword, string newPassword);
    }
}
