# Security Code Templates
## Ready-to-Use Security Implementations for Phase 3

---

## 🔐 1. Secure Configuration Management

### **Azure Key Vault Integration**
```csharp
// Services/SecureConfigurationService.cs
using Azure.Security.KeyVault.Secrets;
using Azure.Identity;

public class SecureConfigurationService
{
    private readonly SecretClient _secretClient;
    private readonly IMemoryCache _cache;
    private readonly ILogger<SecureConfigurationService> _logger;

    public SecureConfigurationService(IConfiguration configuration, IMemoryCache cache, ILogger<SecureConfigurationService> logger)
    {
        var keyVaultUrl = configuration["KeyVault:VaultUrl"];
        _secretClient = new SecretClient(new Uri(keyVaultUrl), new DefaultAzureCredential());
        _cache = cache;
        _logger = logger;
    }

    public async Task<string> GetSecretAsync(string secretName)
    {
        var cacheKey = $"secret_{secretName}";
        
        if (_cache.TryGetValue(cacheKey, out string cachedValue))
        {
            return cachedValue;
        }

        try
        {
            var secret = await _secretClient.GetSecretAsync(secretName);
            _cache.Set(cacheKey, secret.Value.Value, TimeSpan.FromMinutes(15));
            return secret.Value.Value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve secret {SecretName}", secretName);
            throw new SecurityException($"Unable to retrieve configuration secret: {secretName}");
        }
    }
}

// Program.cs update
public static async Task Main(string[] args)
{
    var builder = WebApplication.CreateBuilder(args);
    
    // Register secure configuration service
    builder.Services.AddSingleton<SecureConfigurationService>();
    
    // Use secure data protection
    var serviceProvider = builder.Services.BuildServiceProvider();
    var secureConfig = serviceProvider.GetRequiredService<SecureConfigurationService>();
    
    builder.Services.AddDataProtection()
        .PersistKeysToAzureKeyVault(
            new Uri(await secureConfig.GetSecretAsync("DataProtection-KeyVaultUrl")),
            await secureConfig.GetSecretAsync("DataProtection-KeyName"),
            new DefaultAzureCredential());
    
    var app = builder.Build();
    await app.RunAsync();
}
```

---

## 🔒 2. Enhanced Authentication Service

### **Secure Password Service**
```csharp
// Services/SecurePasswordService.cs
using System.Security.Cryptography;

public class SecurePasswordService : ISecurePasswordService
{
    private const int PBKDF2_ITERATIONS = 100000;
    private const int SALT_SIZE = 32;
    private const int HASH_SIZE = 32;
    private readonly ILogger<SecurePasswordService> _logger;

    public SecurePasswordService(ILogger<SecurePasswordService> logger)
    {
        _logger = logger;
    }

    public async Task<PasswordHash> CreatePasswordHashAsync(string password)
    {
        var salt = RandomNumberGenerator.GetBytes(SALT_SIZE);
        var hash = await Task.Run(() => 
            Rfc2898DeriveBytes.Pbkdf2(password, salt, PBKDF2_ITERATIONS, HashAlgorithmName.SHA256, HASH_SIZE));
        
        return new PasswordHash
        {
            Hash = Convert.ToBase64String(hash),
            Salt = Convert.ToBase64String(salt),
            Iterations = PBKDF2_ITERATIONS,
            Algorithm = "PBKDF2-SHA256"
        };
    }

    public async Task<bool> VerifyPasswordAsync(string password, PasswordHash storedHash)
    {
        try
        {
            var salt = Convert.FromBase64String(storedHash.Salt);
            var storedHashBytes = Convert.FromBase64String(storedHash.Hash);
            
            var computedHash = await Task.Run(() => 
                Rfc2898DeriveBytes.Pbkdf2(password, salt, storedHash.Iterations, HashAlgorithmName.SHA256, HASH_SIZE));
            
            return CryptographicOperations.FixedTimeEquals(storedHashBytes, computedHash);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Password verification failed");
            return false;
        }
    }
}

public class PasswordHash
{
    public string Hash { get; set; }
    public string Salt { get; set; }
    public int Iterations { get; set; }
    public string Algorithm { get; set; }
}
```

### **Account Lockout Service**
```csharp
// Services/AccountLockoutService.cs
public class AccountLockoutService : IAccountLockoutService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<AccountLockoutService> _logger;
    private readonly SecurityAuditService _auditService;

    private const int MAX_FAILED_ATTEMPTS = 5;
    private static readonly TimeSpan[] LockoutDurations = {
        TimeSpan.FromMinutes(1),   // 1st lockout
        TimeSpan.FromMinutes(5),   // 2nd lockout
        TimeSpan.FromMinutes(15),  // 3rd lockout
        TimeSpan.FromHours(1),     // 4th lockout
        TimeSpan.FromHours(24)     // 5th+ lockout
    };

    public async Task<LockoutResult> ValidateLoginAttemptAsync(string userId, string ipAddress)
    {
        var lockoutKey = $"lockout_{userId}";
        var attemptKey = $"attempts_{userId}";
        
        var lockoutInfo = _cache.Get<LockoutInfo>(lockoutKey);
        if (lockoutInfo != null && lockoutInfo.LockedUntil > DateTime.UtcNow)
        {
            await _auditService.LogSecurityEventAsync("LOGIN_ATTEMPT_WHILE_LOCKED", userId, new
            {
                IpAddress = ipAddress,
                LockedUntil = lockoutInfo.LockedUntil,
                AttemptNumber = lockoutInfo.AttemptCount
            });
            
            return new LockoutResult
            {
                IsLocked = true,
                LockedUntil = lockoutInfo.LockedUntil,
                RemainingTime = lockoutInfo.LockedUntil - DateTime.UtcNow
            };
        }

        return new LockoutResult { IsLocked = false };
    }

    public async Task RecordFailedAttemptAsync(string userId, string ipAddress)
    {
        var attemptKey = $"attempts_{userId}";
        var lockoutKey = $"lockout_{userId}";
        
        var currentAttempts = _cache.Get<int>(attemptKey);
        currentAttempts++;
        
        _cache.Set(attemptKey, currentAttempts, TimeSpan.FromHours(1));

        if (currentAttempts >= MAX_FAILED_ATTEMPTS)
        {
            var lockoutIndex = Math.Min(currentAttempts - MAX_FAILED_ATTEMPTS, LockoutDurations.Length - 1);
            var lockoutDuration = LockoutDurations[lockoutIndex];
            var lockedUntil = DateTime.UtcNow.Add(lockoutDuration);
            
            var lockoutInfo = new LockoutInfo
            {
                LockedUntil = lockedUntil,
                AttemptCount = currentAttempts,
                LockoutDuration = lockoutDuration
            };
            
            _cache.Set(lockoutKey, lockoutInfo, lockoutDuration);
            
            await _auditService.LogSecurityEventAsync("ACCOUNT_LOCKED", userId, new
            {
                IpAddress = ipAddress,
                AttemptCount = currentAttempts,
                LockoutDuration = lockoutDuration,
                LockedUntil = lockedUntil
            });
        }
    }

    public async Task ClearFailedAttemptsAsync(string userId)
    {
        var attemptKey = $"attempts_{userId}";
        var lockoutKey = $"lockout_{userId}";
        
        _cache.Remove(attemptKey);
        _cache.Remove(lockoutKey);
    }
}
```

---

## 🛡️ 3. Input Validation & Security Middleware

### **Input Sanitization Middleware**
```csharp
// Middlewares/InputSanitizationMiddleware.cs
public class InputSanitizationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<InputSanitizationMiddleware> _logger;
    private readonly SecurityAuditService _auditService;

    private static readonly string[] SQL_INJECTION_PATTERNS = {
        "--", "/*", "*/", "xp_", "sp_", "DROP TABLE", "DELETE FROM", 
        "TRUNCATE", "INSERT INTO", "UPDATE SET", "UNION SELECT",
        "EXEC(", "EXECUTE(", "@@", "CHAR(", "NCHAR(", "VARCHAR("
    };

    private static readonly string[] XSS_PATTERNS = {
        "<script", "</script>", "javascript:", "vbscript:", "onload=",
        "onerror=", "onclick=", "onmouseover=", "alert(", "eval(",
        "expression(", "url(javascript:", "url(data:"
    };

    public async Task InvokeAsync(HttpContext context)
    {
        if (ShouldValidateRequest(context))
        {
            await ValidateRequestAsync(context);
        }

        await _next(context);
    }

    private bool ShouldValidateRequest(HttpContext context)
    {
        return context.Request.Method == "POST" || context.Request.Method == "PUT" ||
               context.Request.ContentType?.Contains("application/json") == true;
    }

    private async Task ValidateRequestAsync(HttpContext context)
    {
        context.Request.EnableBuffering();
        
        using var reader = new StreamReader(context.Request.Body, leaveOpen: true);
        var body = await reader.ReadToEndAsync();
        context.Request.Body.Position = 0;

        var violations = new List<string>();

        // Check for SQL injection patterns
        foreach (var pattern in SQL_INJECTION_PATTERNS)
        {
            if (body.Contains(pattern, StringComparison.OrdinalIgnoreCase))
            {
                violations.Add($"SQL injection pattern detected: {pattern}");
            }
        }

        // Check for XSS patterns
        foreach (var pattern in XSS_PATTERNS)
        {
            if (body.Contains(pattern, StringComparison.OrdinalIgnoreCase))
            {
                violations.Add($"XSS pattern detected: {pattern}");
            }
        }

        if (violations.Any())
        {
            await _auditService.LogSecurityEventAsync("MALICIOUS_INPUT_DETECTED", 
                context.User?.Identity?.Name ?? "anonymous", new
            {
                IpAddress = context.Connection.RemoteIpAddress?.ToString(),
                UserAgent = context.Request.Headers["User-Agent"].ToString(),
                Violations = violations,
                RequestPath = context.Request.Path,
                Method = context.Request.Method
            });

            context.Response.StatusCode = 400;
            await context.Response.WriteAsync("Request contains potentially malicious content");
            return;
        }
    }
}
```

### **Enhanced Validation Attributes**
```csharp
// Attributes/SecureValidationAttributes.cs
public class SecureAmountAttribute : ValidationAttribute
{
    private readonly decimal _minValue;
    private readonly decimal _maxValue;
    private readonly int _maxDecimalPlaces;

    public SecureAmountAttribute(double minValue = 0.01, double maxValue = 999999.99, int maxDecimalPlaces = 2)
    {
        _minValue = (decimal)minValue;
        _maxValue = (decimal)maxValue;
        _maxDecimalPlaces = maxDecimalPlaces;
    }

    public override bool IsValid(object value)
    {
        if (value is decimal amount)
        {
            // Check range
            if (amount < _minValue || amount > _maxValue)
                return false;

            // Check decimal places to prevent precision attacks
            var decimalPlaces = BitConverter.GetBytes(decimal.GetBits(amount)[3])[2];
            if (decimalPlaces > _maxDecimalPlaces)
                return false;

            // Ensure the amount is exactly what was specified (no rounding issues)
            var rounded = Math.Round(amount, _maxDecimalPlaces);
            return amount == rounded;
        }

        return false;
    }

    public override string FormatErrorMessage(string name)
    {
        return $"{name} must be between {_minValue} and {_maxValue} with maximum {_maxDecimalPlaces} decimal places.";
    }
}

public class SecureStringAttribute : ValidationAttribute
{
    private readonly int _maxLength;
    private readonly bool _allowHtml;

    public SecureStringAttribute(int maxLength = 255, bool allowHtml = false)
    {
        _maxLength = maxLength;
        _allowHtml = allowHtml;
    }

    public override bool IsValid(object value)
    {
        if (value is string str)
        {
            if (string.IsNullOrEmpty(str))
                return false;

            if (str.Length > _maxLength)
                return false;

            if (!_allowHtml && ContainsHtmlTags(str))
                return false;

            // Check for common injection patterns
            if (ContainsSqlInjectionPatterns(str))
                return false;

            return true;
        }

        return false;
    }

    private bool ContainsHtmlTags(string input)
    {
        return input.Contains('<') || input.Contains('>') || input.Contains("javascript:");
    }

    private bool ContainsSqlInjectionPatterns(string input)
    {
        var patterns = new[] { "--", "/*", "*/", "DROP", "DELETE", "INSERT", "UPDATE", "UNION" };
        return patterns.Any(pattern => input.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }
}
```

---

## 💰 4. Payment Security Service

### **Secure Payment Processing**
```csharp
// Services/SecurePaymentService.cs
public class SecurePaymentService : ISecurePaymentService
{
    private readonly IPaymentRepository _paymentRepository;
    private readonly IFraudDetectionService _fraudDetection;
    private readonly SecurityAuditService _auditService;
    private readonly ILogger<SecurePaymentService> _logger;

    public async Task<PaymentResult> ProcessPaymentAsync(SecurePaymentRequest request)
    {
        // Validate idempotency
        var existingTransaction = await _paymentRepository.GetByIdempotencyKeyAsync(request.IdempotencyKey);
        if (existingTransaction != null)
        {
            _logger.LogInformation("Duplicate payment request detected: {IdempotencyKey}", request.IdempotencyKey);
            return existingTransaction.Result;
        }

        // Verify digital signature
        if (!await VerifyTransactionSignatureAsync(request))
        {
            await _auditService.LogSecurityEventAsync("INVALID_PAYMENT_SIGNATURE", request.UserId, new
            {
                TransactionId = request.TransactionId,
                Amount = request.Amount,
                Currency = request.Currency
            });
            throw new SecurityException("Invalid transaction signature");
        }

        // Fraud detection
        var fraudScore = await _fraudDetection.CalculateRiskScoreAsync(request);
        if (fraudScore > 0.8m)
        {
            await _auditService.LogSecurityEventAsync("HIGH_RISK_TRANSACTION", request.UserId, new
            {
                TransactionId = request.TransactionId,
                FraudScore = fraudScore,
                Amount = request.Amount,
                Currency = request.Currency
            });
            
            throw new FraudDetectedException($"Transaction flagged for manual review. Risk score: {fraudScore}");
        }

        // Validate payment limits
        if (!await ValidatePaymentLimitsAsync(request))
        {
            throw new PaymentLimitExceededException("Payment exceeds allowed limits");
        }

        // Process payment
        var result = await ExecutePaymentAsync(request);
        
        // Log successful transaction
        await _auditService.LogSecurityEventAsync("PAYMENT_PROCESSED", request.UserId, new
        {
            TransactionId = request.TransactionId,
            Amount = request.Amount,
            Currency = request.Currency,
            PaymentMethod = request.PaymentMethod,
            ProcessingTime = result.ProcessingTime
        });

        return result;
    }

    private async Task<bool> VerifyTransactionSignatureAsync(SecurePaymentRequest request)
    {
        try
        {
            var payload = $"{request.TransactionId}|{request.Amount}|{request.Currency}|{request.Timestamp:O}";
            var expectedSignature = await ComputeHmacSha256Async(payload, request.ApiKey);
            
            return CryptographicOperations.FixedTimeEquals(
                Convert.FromBase64String(request.Signature),
                Convert.FromBase64String(expectedSignature)
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Signature verification failed for transaction {TransactionId}", request.TransactionId);
            return false;
        }
    }

    private async Task<string> ComputeHmacSha256Async(string data, string key)
    {
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(key));
        var hash = await Task.Run(() => hmac.ComputeHash(Encoding.UTF8.GetBytes(data)));
        return Convert.ToBase64String(hash);
    }
}

public class SecurePaymentRequest
{
    public string TransactionId { get; set; }
    public string IdempotencyKey { get; set; }
    public string UserId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; }
    public string PaymentMethod { get; set; }
    public DateTime Timestamp { get; set; }
    public string Signature { get; set; }
    public string ApiKey { get; set; }
}
```

### **Fraud Detection Service**
```csharp
// Services/FraudDetectionService.cs
public class FraudDetectionService : IFraudDetectionService
{
    private readonly IPaymentRepository _paymentRepository;
    private readonly IUserRepository _userRepository;
    private readonly ILogger<FraudDetectionService> _logger;

    public async Task<decimal> CalculateRiskScoreAsync(SecurePaymentRequest request)
    {
        var riskFactors = new List<RiskFactor>();

        // Velocity checks
        var recentTransactions = await _paymentRepository.GetRecentTransactionsAsync(
            request.UserId, TimeSpan.FromMinutes(10));
        
        if (recentTransactions.Count >= 5)
        {
            riskFactors.Add(new RiskFactor("HIGH_VELOCITY", 0.4m, 
                $"5+ transactions in 10 minutes"));
        }

        // Amount anomaly detection
        var userAverage = await _paymentRepository.GetAverageTransactionAmountAsync(
            request.UserId, TimeSpan.FromDays(30));
        
        if (request.Amount > userAverage * 10)
        {
            riskFactors.Add(new RiskFactor("AMOUNT_ANOMALY", 0.3m, 
                $"Amount {request.Amount} is 10x average {userAverage}"));
        }

        // Geographic anomaly (if IP geolocation available)
        var userLocation = await GetUserLocationAsync(request.UserId);
        var currentLocation = await GetCurrentLocationAsync(); // From IP
        
        if (userLocation != null && currentLocation != null)
        {
            var distance = CalculateDistance(userLocation, currentLocation);
            if (distance > 1000) // 1000km threshold
            {
                riskFactors.Add(new RiskFactor("GEOGRAPHIC_ANOMALY", 0.2m, 
                    $"Transaction from {distance}km away"));
            }
        }

        // Time-based anomaly
        var userTypicalHours = await GetUserTypicalTransactionHoursAsync(request.UserId);
        var currentHour = DateTime.UtcNow.Hour;
        
        if (!userTypicalHours.Contains(currentHour))
        {
            riskFactors.Add(new RiskFactor("TIME_ANOMALY", 0.1m, 
                $"Transaction at unusual hour {currentHour}"));
        }

        var totalRiskScore = riskFactors.Sum(rf => rf.Score);
        
        _logger.LogInformation("Risk assessment for transaction {TransactionId}: Score {RiskScore}, Factors: {@RiskFactors}", 
            request.TransactionId, totalRiskScore, riskFactors);

        return Math.Min(totalRiskScore, 1.0m); // Cap at 1.0
    }
}

public class RiskFactor
{
    public string Type { get; set; }
    public decimal Score { get; set; }
    public string Description { get; set; }

    public RiskFactor(string type, decimal score, string description)
    {
        Type = type;
        Score = score;
        Description = description;
    }
}
```

---

## 📊 5. Security Monitoring & Audit

### **Security Audit Service**
```csharp
// Services/SecurityAuditService.cs
public class SecurityAuditService : ISecurityAuditService
{
    private readonly IAuditRepository _auditRepository;
    private readonly IAlertingService _alertingService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<SecurityAuditService> _logger;

    public async Task LogSecurityEventAsync(string eventType, string userId, object eventData = null)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        
        var auditEntry = new SecurityAuditEntry
        {
            Id = Guid.NewGuid(),
            EventType = eventType,
            UserId = userId ?? "anonymous",
            IpAddress = httpContext?.Connection?.RemoteIpAddress?.ToString(),
            UserAgent = httpContext?.Request?.Headers["User-Agent"].FirstOrDefault(),
            RequestPath = httpContext?.Request?.Path,
            Method = httpContext?.Request?.Method,
            Timestamp = DateTime.UtcNow,
            EventData = eventData != null ? JsonSerializer.Serialize(eventData) : null,
            CorrelationId = Activity.Current?.Id,
            SessionId = httpContext?.Session?.Id,
            RiskScore = await CalculateEventRiskScore(eventType, eventData)
        };

        try
        {
            await _auditRepository.SaveAsync(auditEntry);
            
            // Real-time alerting for high-risk events
            if (auditEntry.RiskScore >= 0.7m || IsCriticalEvent(eventType))
            {
                await _alertingService.SendSecurityAlertAsync(auditEntry);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log security audit event {EventType} for user {UserId}", 
                eventType, userId);
            
            // Fallback logging to prevent security event loss
            _logger.LogWarning("SECURITY_EVENT: {EventType} | User: {UserId} | Data: {@EventData}", 
                eventType, userId, eventData);
        }
    }

    private async Task<decimal> CalculateEventRiskScore(string eventType, object eventData)
    {
        return eventType switch
        {
            "LOGIN_FAILED" => 0.2m,
            "ACCOUNT_LOCKED" => 0.8m,
            "PASSWORD_CHANGED" => 0.4m,
            "MALICIOUS_INPUT_DETECTED" => 0.9m,
            "HIGH_RISK_TRANSACTION" => 0.8m,
            "INVALID_PAYMENT_SIGNATURE" => 1.0m,
            "RATE_LIMIT_EXCEEDED" => 0.3m,
            _ => 0.1m
        };
    }

    private bool IsCriticalEvent(string eventType)
    {
        var criticalEvents = new[]
        {
            "MALICIOUS_INPUT_DETECTED",
            "INVALID_PAYMENT_SIGNATURE",
            "UNAUTHORIZED_ACCESS_ATTEMPT",
            "DATA_BREACH_DETECTED",
            "SYSTEM_COMPROMISE_DETECTED"
        };

        return criticalEvents.Contains(eventType);
    }
}

public class SecurityAuditEntry
{
    public Guid Id { get; set; }
    public string EventType { get; set; }
    public string UserId { get; set; }
    public string IpAddress { get; set; }
    public string UserAgent { get; set; }
    public string RequestPath { get; set; }
    public string Method { get; set; }
    public DateTime Timestamp { get; set; }
    public string EventData { get; set; }
    public string CorrelationId { get; set; }
    public string SessionId { get; set; }
    public decimal RiskScore { get; set; }
}
```

### **Enhanced Program.cs Security Configuration**
```csharp
// Updated Program.cs with all security enhancements
public static async Task Main(string[] args)
{
    var builder = WebApplication.CreateBuilder(args);

    // Configure Serilog with security context
    Log.Logger = new LoggerConfiguration()
        .ReadFrom.Configuration(builder.Configuration)
        .Enrich.FromLogContext()
        .Enrich.WithProperty("Application", "OptimumWallet")
        .WriteTo.Console()
        .WriteTo.File("../logs/security-.log", 
            rollingInterval: RollingInterval.Day,
            restrictedToMinimumLevel: LogEventLevel.Warning)
        .CreateLogger();

    builder.Host.UseSerilog();

    // Security services
    builder.Services.AddSingleton<SecureConfigurationService>();
    builder.Services.AddScoped<SecurePasswordService>();
    builder.Services.AddScoped<AccountLockoutService>();
    builder.Services.AddScoped<SecurityAuditService>();
    builder.Services.AddScoped<SecurePaymentService>();
    builder.Services.AddScoped<FraudDetectionService>();

    // Enhanced rate limiting
    builder.Services.AddRateLimiter(options =>
    {
        options.RejectionStatusCode = 429;
        options.OnRejected = async (context, cancellationToken) =>
        {
            var auditService = context.HttpContext.RequestServices.GetRequiredService<SecurityAuditService>();
            await auditService.LogSecurityEventAsync("RATE_LIMIT_EXCEEDED", 
                context.HttpContext.User?.Identity?.Name, new
                {
                    Endpoint = context.HttpContext.Request.Path,
                    IpAddress = context.HttpContext.Connection.RemoteIpAddress?.ToString()
                });
                
            context.HttpContext.Response.StatusCode = StatusCodes.Status429TooManyRequests;
            await context.HttpContext.Response.WriteAsync("Rate limit exceeded. Please try again later.");
        };

        // Authentication endpoints - strict limits
        options.AddPolicy("Auth", httpContext =>
            RateLimitPartition.GetFixedWindowLimiter(
                httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown",
                partition => new FixedWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = 3,
                    Window = TimeSpan.FromMinutes(15)
                }));

        // Payment endpoints - moderate limits
        options.AddPolicy("Payment", httpContext =>
            RateLimitPartition.GetSlidingWindowLimiter(
                httpContext.User?.Identity?.Name ?? httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown",
                partition => new SlidingWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = 10,
                    QueueLimit = 2,
                    Window = TimeSpan.FromMinutes(1),
                    SegmentsPerWindow = 6
                }));

        // General API endpoints
        options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(
            httpContext => RateLimitPartition.GetTokenBucketLimiter(
                httpContext.User?.Identity?.Name ?? httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown",
                partition => new TokenBucketRateLimiterOptions
                {
                    TokenLimit = 100,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 10,
                    ReplenishmentPeriod = TimeSpan.FromSeconds(10),
                    TokensPerPeriod = 20,
                    AutoReplenishment = true
                }));
    });

    var app = builder.Build();

    // Security middleware pipeline
    app.UseMiddleware<InputSanitizationMiddleware>();
    
    // Security headers
    app.Use(async (context, next) =>
    {
        var headers = context.Response.Headers;
        headers.Add("X-Content-Type-Options", "nosniff");
        headers.Add("X-Frame-Options", "DENY");
        headers.Add("X-XSS-Protection", "1; mode=block");
        headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
        headers.Add("Content-Security-Policy", 
            "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';");
        headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
        headers.Add("Permissions-Policy", "geolocation=(), microphone=(), camera=(), payment=()");
        
        // Remove server information leakage
        headers.Remove("Server");
        headers.Add("Server", "OptimumWallet");
        
        await next();
    });

    // Standard middleware
    app.UseHttpsRedirection();
    app.UseRouting();
    app.UseAuthentication();
    app.UseAuthorization();
    app.UseRateLimiter();

    // API endpoints
    app.MapControllers();
    app.MapHealthChecks("/health");

    await app.RunAsync();
}
```

This comprehensive set of security code templates provides immediate, production-ready implementations that can be integrated during Phase 3. Each component includes proper error handling, logging, and follows security best practices.
