using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Application.Interfaces;
using Optimum.Wallet.Infrastructure.CrediAppWS;
using Optimum.Wallet.Application.Common;

namespace Optimum.Wallet.Infrastructure.Services
{
    public class WebServices : IWebServices
{
    // Define a static logger variable so that it references the
    // Logger instance.
    private readonly ILogger<WebServices> _logger;

    //private static readonly ServicesGpsClient Service = new ServicesGpsClient();
    //private static readonly BFCService.BFCSoapClient Service = new BFCService.BFCSoapClient();

    private readonly CrediAppServiceSoapClient Service =
        new CrediAppServiceSoapClient(CrediAppServiceSoapClient.EndpointConfiguration.CrediAppServiceSoap);


    public WebServices(ILogger<WebServices> logger)
    {
        _logger = logger;
    }


    public async Task<List<CardDetailsViewModel>> F4_GetCardList(string pCpr)
    {
        _logger.LogDebug($"[WebServicesHelper/F4_GetCardList/{pCpr}] Entered.");
        var result = new List<CardDetailsViewModel>();

        try
        {
            if (WalletApplication.IsDummyData)
            {
                return DapperHelper.Query<CardDetailsViewModel>($@"
                    SELECT CA.CAID AS [CardId], CA.CustAcCode AS [CardNumber], CA.CustAcNameE AS [CardHolderName], '02/20' AS [CardExpiry], '*********' AS [CardHolderCpr], '1' AS [Status], '1' AS [CardType]
                    FROM dbo.CustomerAccountTbl CA
                    INNER JOIN dbo.CustAcctSetupTbl CAS ON CAS.CustActId = CA.CustActId AND CAS.Default_ = 1
                    INNER JOIN dbo.ContactTbl CT ON CT.ContactID = CA.RefContactID
                    INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON AF.AccountNo = CA.CAAccountNo
                    INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy
                    WHERE CT.Cont_CPR = @CPR AND ISNULL(PayType, 0) = 2
                ",
                new
                {
                    CPR = pCpr
                }).ToList();
            }

            var ws = await Service.F4_GetCardListAsync(pCpr);

            result = JsonConvert.DeserializeObject<List<CardDetailsViewModel>>(ws.Body.F4_GetCardListResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F4_GetCardList/{pCpr}] Failed to get the card List.", ex);
        }
        return result;
    }

    public async Task<CardBalance> F5_GetBalance(string pCpr, string pCardNumber, CardBalance mainCardBalance = null)
    {
        _logger.LogDebug($"[WebServicesHelper/F5_GetBalance/{pCpr}/[{pCardNumber}]] Entered.");
        var balance = new CardBalance();

        try
        {
            var ws = await Service.F5_GetBalanceAsync(pCpr, pCardNumber, JsonConvert.SerializeObject(mainCardBalance));
            balance = JsonConvert.DeserializeObject<CardBalance>(ws.Body.F5_GetBalanceResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F5_GetBalance/{pCpr}/[{pCardNumber}]] Failed to get the balance.", ex);
        }

        // Nullify the cards data (PCI Requirement)
        pCardNumber = null; GC.Collect();

        return balance;
    }

    public async Task<Dictionary<string, string>> F6_GetStatementList(string pCpr, string pCardNumber)
    {
        _logger.LogDebug($"[WebServicesHelper/F6_GetStatementList/{pCpr}/{pCardNumber}] Enter");

        // Keep track of the result to be returned
        var result = new Dictionary<string, string>();

        try
        {
            var data = await Service.F6_GetStatementListAsync(pCpr, pCardNumber);
            result = JsonConvert.DeserializeObject<Dictionary<string, string>>(data.Body.F6_GetStatementListResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F6_GetStatementList/{pCpr}/{pCardNumber}] Failed to get the statement Lists.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<List<CardStatementModelView>> F8_GetCurrentStatementTransaction(string pCpr, string pCardNumber)
    {
        _logger.LogDebug($"[WebServicesHelper/F8_GetCurrentStatementTransaction/{pCpr}/{pCardNumber}] Entered");

        // Keep track of the result to be returned
        var result = new List<CardStatementModelView>();

        try
        {
            var data = await Service.F8_GetCurrentStatementTransactionAsync(pCpr, pCardNumber);
            result = JsonConvert.DeserializeObject<List<CardStatementModelView>>(data.Body.F8_GetCurrentStatementTransactionResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F8_GetCurrentStatementTransaction/{pCpr}/{pCardNumber}] Failed to get the current statement transactions.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<List<CardStatementModelView>> GetStatementTransaction(string pCpr, string pCardNumber, string type = "C", string statementDate = "", Dictionary<string, string> suppCards = null, bool onlySupp = false)
    {
        _logger.LogDebug($"[WebServicesHelper/GetStatementTransaction]/ {pCpr} || {pCardNumber} || {type} || {statementDate}] Entered");

        // Keep track of the result to be returned
        var result = new List<CardStatementModelView>();

        try
        {
            var data = await Service.GetStatementTransactionAsync(pCpr, pCardNumber, type, statementDate, JsonConvert.SerializeObject(suppCards), onlySupp.ToString());
            result = JsonConvert.DeserializeObject<List<CardStatementModelView>>(data.Body.GetStatementTransactionResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/GetStatementTransaction]/ {pCpr} || {pCardNumber} || {type} || {statementDate}] Failed to get the statement Lists.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<string> F10_StopCard_Raw(string pCpr, string pCardNumber)
    {
        _logger.LogDebug($"[WebServicesHelper/F10_StopCard_Raw/{pCpr} || {pCardNumber}] Entered.");
        var result = "";

        try
        {
            var data = await Service.F10_StopCard_RawAsync(pCpr, pCardNumber);
            result = JsonConvert.DeserializeObject<string>(data.Body.F10_StopCard_RawResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F10_StopCard_Raw/{pCpr} || {pCardNumber}] Failed to stop the card.", ex);
        }
        return result;
    }

    public async Task<string> F13_PayMyCardPayment_Raw(string pCardNumber, string pTrxAmount, string pCpr, string pReferenceNbr, string fromCAID, string transaction_source)
    {
        _logger.LogDebug($"[WebServicesHelper/F13_PayMyCardPayment_Raw/{pCardNumber} || {pTrxAmount} || {pCpr} || {pReferenceNbr} || {fromCAID} || {transaction_source}] Entered.");
        var result = "";

        try
        {
            var data = await Service.F13_PayMyCardPayment_RawAsync(pCardNumber, pTrxAmount, pCpr, pReferenceNbr, fromCAID, transaction_source);
            result = JsonConvert.DeserializeObject<string>(data.Body.F13_PayMyCardPayment_RawResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F13_PayMyCardPayment_Raw/{pCardNumber} || {pTrxAmount} || {pCpr} || {pReferenceNbr} || {fromCAID} || {transaction_source}] Failed to pay the card.", ex);
        }
        return result;
    }

    public async Task<string> External_Transfer_Funds_Raw(string fromCardNumber, string pTrxAmount, string fromCpr, string toCpr, string toAccount, string targetDetination)
    {
        _logger.LogDebug($"[WebServicesHelper/External_Transfer_Funds_Raw/{fromCardNumber} || {pTrxAmount} || {fromCpr} || {toCpr} || {toAccount} || {targetDetination}] Entered.");
        var result = "";

        try
        {
            var data = await Service.External_Transfer_Funds_RawAsync(fromCardNumber, pTrxAmount, fromCpr, toCpr, toAccount, targetDetination);
            result = JsonConvert.DeserializeObject<string>(data.Body.External_Transfer_Funds_RawResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/External_Transfer_Funds_Raw/{fromCardNumber} || {pTrxAmount} || {fromCpr} || {toCpr} || {toAccount} || {targetDetination}] Failed external transfer funds.", ex);
        }
        return result;
    }

    public async Task<string> F16A_ActivatEstatement_Raw(string pCpr, string pEmailAddress)
    {
        _logger.LogDebug($"[WebServicesHelper/F16A_ActivatEstatement_Raw/{pCpr} || {pEmailAddress}] Entered.");
        var result = "";

        try
        {
            var data = await Service.F16A_ActivatEstatement_RawAsync(pCpr, pEmailAddress);
            result = JsonConvert.DeserializeObject<string>(data.Body.F16A_ActivatEstatement_RawResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F16A_ActivatEstatement_Raw/{pCpr} || {pEmailAddress}] Failed to pay the card.", ex);
        }
        return result;
    }

    public async Task<string> F18_UpdateMobileNumber_Raw(string pCpr, string pMobile, string pHome)
    {
        _logger.LogDebug($"[WebServicesHelper/F18_UpdateMobileNumber_Raw/{pCpr} || {pMobile} || {pHome}] Entered.");
        var result = "";

        try
        {
            var data = await Service.F18_UpdateMobileNumber_RawAsync(pCpr, pMobile, pHome);
            result = JsonConvert.DeserializeObject<string>(data.Body.F18_UpdateMobileNumber_RawResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F18_UpdateMobileNumber_Raw/{pCpr} || {pMobile} || {pHome}] Failed to pay the card.", ex);
        }
        return result;
    }

    public async Task<string> F19_UpdateEmail_Raw(string pCpr, string pEmail)
    {
        _logger.LogDebug($"[WebServicesHelper/F19_UpdateEmail_Raw/{pCpr} || {pEmail}] Entered.");
        var result = "";

        try
        {
            var data = await Service.F19_UpdateEmail_RawAsync(pCpr, pEmail);
            result = JsonConvert.DeserializeObject<string>(data.Body.F19_UpdateEmail_RawResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F19_UpdateEmail_Raw/{pCpr} || {pEmail}] Failed to pay the card.", ex);
        }
        return result;
    }

    public async Task<PersonalData> F25_GetPersonalData_Raw(string pCpr)
    {
        _logger.LogDebug($"[WebServicesHelper/F25_GetPersonalData_Raw/{pCpr}] Entered");

        // Keep track of the result to be returned
        PersonalData result = new PersonalData();
        try
        {
            var data = await Service.F25_GetPersonalData_RawAsync(pCpr);
            result = JsonConvert.DeserializeObject<PersonalData>(data.Body.F25_GetPersonalData_RawResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F25_GetPersonalData_Raw/{pCpr}] Failed to get personal data.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<VerifyUser> F27_CardHolderVerification_Raw(string _sCardEmbossedName, string _sCardLast4Digit, string pCpr, string pMobile, string valdationFlag = "N")
    {
        _logger.LogDebug($"[WebServicesHelper/F27_CardHolderVerification_Raw/{_sCardEmbossedName} || {_sCardLast4Digit} || {pCpr} || {pMobile} || {valdationFlag}] Entered");

        // Keep track of the result to be returned
        VerifyUser result = new VerifyUser();
        try
        {
            var data = await Service.F27_CardHolderVerification_RawAsync(_sCardEmbossedName, _sCardLast4Digit, pCpr, pMobile, valdationFlag);
            result = JsonConvert.DeserializeObject<VerifyUser>(data.Body.F27_CardHolderVerification_RawResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F27_CardHolderVerification_Raw/{_sCardEmbossedName} || {_sCardLast4Digit} || {pCpr} || {pMobile} || {valdationFlag}] Failed to verify user.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<List<Application.Common.Models.Wallet>> F29_GetWalletsBalances(string pCpr, string pCardNumber, CardBalance mainCardBalance = null)
    {

        _logger.LogDebug($"[WebServicesHelper/F29_GetWalletsBalances/{pCpr} || {pCardNumber} || {JsonConvert.SerializeObject(mainCardBalance)}] Entered");

        // Keep track of the result to be returned
        List<Application.Common.Models.Wallet> result = new List<Application.Common.Models.Wallet>();
        try
        {
            var data = await Service.F29_GetWalletsBalancesAsync(pCpr, pCardNumber, JsonConvert.SerializeObject(mainCardBalance));
            result = JsonConvert.DeserializeObject<List<Application.Common.Models.Wallet>>(data.Body.F29_GetWalletsBalancesResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F29_GetWalletsBalances/{pCpr} || {pCardNumber} || {JsonConvert.SerializeObject(mainCardBalance)} Failed to get wallet balance.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<WalletTransferResult> F30_VerifyTransferAmount(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency)
    {

        _logger.LogDebug($"[WebServicesHelper/F30_VerifyTransferAmount/{pCpr} || {pCardNumber} || {sourceWallet} || {destinationWallet} || {transferAmount}] Entered");

        // Keep track of the result to be returned
        WalletTransferResult result = new WalletTransferResult();
        try
        {
            var data = await Service.F31_ConfirmTransferAmountAsync(pCpr, pCardNumber, sourceWallet, destinationWallet, transferAmount, transferCurrency);
            result = JsonConvert.DeserializeObject<WalletTransferResult>(data.Body.F31_ConfirmTransferAmountResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F30_VerifyTransferAmount/{pCpr} || {pCardNumber} || {sourceWallet} || {destinationWallet} || {transferAmount}] Failed to verify transfer amount.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<WalletTransferResult> F31_ConfirmTransferAmount(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency)
    {

        _logger.LogDebug($"[WebServicesHelper/F31_ConfirmTransferAmount/{pCpr} || {pCardNumber} || {sourceWallet} || {destinationWallet} || {transferAmount}] Entered");

        // Keep track of the result to be returned
        WalletTransferResult result = new WalletTransferResult();
        try
        {
            var data = await Service.F31_ConfirmTransferAmountAsync(pCpr, pCardNumber, sourceWallet, destinationWallet, transferAmount, transferCurrency);
            result = JsonConvert.DeserializeObject<WalletTransferResult>(data.Body.F31_ConfirmTransferAmountResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F31_ConfirmTransferAmount/{pCpr} || {pCardNumber} || {sourceWallet} || {destinationWallet} || {transferAmount}] Failed to confirm transfer amount.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<List<CardStatementModelView>> GetWalletStatementTransaction(string pCpr, string pCardNumber, string type = "C", string statementDate = "", string Currency = "")
    {

        _logger.LogDebug($"[WebServicesHelper/GetWalletStatementTransaction/{pCpr} || {pCardNumber} || {type} || {statementDate} || {Currency}] Entered");

        // Keep track of the result to be returned
        List<CardStatementModelView> result = new List<CardStatementModelView>();
        try
        {
            var data = await Service.GetWalletStatementTransactionAsync(pCpr, pCardNumber, type, statementDate, Currency);
            result = JsonConvert.DeserializeObject<List<CardStatementModelView>>(data.Body.GetWalletStatementTransactionResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/GetWalletStatementTransaction/{pCpr} || {pCardNumber} || {type} || {statementDate} || {Currency}] Failed to get wallet statement transaction.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<Dictionary<int, string>> F23_LoyFppProg(string pCpr)
    {
        _logger.LogDebug($"[WebServicesHelper/F23_LoyFppProg/{pCpr}] Entered");

        // Keep track of the result to be returned
        Dictionary<int, string> result = new Dictionary<int, string>();
        try
        {
            var data = await Service.F23_LoyFppProgAsync(pCpr);
            result = JsonConvert.DeserializeObject<Dictionary<int, string>>(data.Body.F23_LoyFppProgResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F23_LoyFppProg/{pCpr}] Failed to get LoyFppProg.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<List<Dictionary<int, string>>> F24_LoyThameen(string pCpr)
    {
        _logger.LogDebug($"[WebServicesHelper/F24_LoyThameen/{pCpr}] Entered");

        // Keep track of the result to be returned
        List<Dictionary<int, string>> result = new List<Dictionary<int, string>>();
        try
        {
            var data = await Service.F24_LoyThameenAsync(pCpr);
            result = JsonConvert.DeserializeObject<List<Dictionary<int, string>>>(data.Body.F24_LoyThameenResult);
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/F24_LoyThameen/{pCpr}] Failed to get LoyThameen.", ex);
        }

        // Return the final result
        return result;
    }

    public async Task<Tuple<bool, string>> RunWebService(string functionName, string parameters)
    {
        _logger.LogDebug($"[WebServicesHelper/RunWebService/{functionName} || {parameters}] Entered");

        // Keep track of the result to be returned
        bool success = false;
        var result = "";
        var dic = new Dictionary<string, string>();
        try
        {
            var data = await Service.RunWebServiceAsync(functionName, parameters);
            dic = JsonConvert.DeserializeObject<Dictionary<string, string>>(data.Body.RunWebServiceResult);
            success = (dic["success"] + "").ToLower() == "true";
        }
        catch (Exception ex)
        {
            _logger.LogError($"[WebServicesHelper/RunWebService/{functionName} || {parameters}] Failed to run the web service.", ex);
        }

        // Return the final result
        return new Tuple<bool, string>(success, result);
    }

    // Format the given card number to return only the first 6 and last 4 digits
    public string GetCardNumber(string source)
    {
        return source?.Length != 10 ? source?.Replace(" ", "").GetStringPortion(6, 4) : source;
    }

    private static int GetCardStatus(string sExpiryDate, string status)
    {
        DateTime expiryDate = new DateTime();
        DateTime.TryParseExact(sExpiryDate, WalletApplication.WebServiceDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out expiryDate);

        // Get today's date
        var date = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day);

        // Check if the card is expired (Adding 1 month since the expiry date is inclusive)
        return expiryDate.AddMonths(1) <= date || (status + "").ToUpper() == "N" ? StatusValues.EXPIRED : StatusValues.ACTIVE;
    }

    private static DateTime ParseDate(string expiryDateStr)
    {
        // Try to parse the date
        var expiryDate = DateTime.MinValue;
        DateTime.TryParse(expiryDateStr, out expiryDate);

        // Return the parsed value
        return expiryDate;
    }
}
}
