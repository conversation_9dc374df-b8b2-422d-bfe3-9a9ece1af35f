﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ACNO" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="AddingTheItem" xml:space="preserve">
    <value>جارٍ الإضافة</value>
  </data>
  <data name="AddReview" xml:space="preserve">
    <value>اضف تقييم</value>
  </data>
  <data name="AdViewModelCart" xml:space="preserve">
    <value>أضف إلى عربة التسوق</value>
  </data>
  <data name="AllShops" xml:space="preserve">
    <value>جميع المتاجر</value>
  </data>
  <data name="AllYourShops" xml:space="preserve">
    <value>جميع متاجرك!</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>السعر</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>تطبيق</value>
  </data>
  <data name="AreYouSureYouWantToRemoveThisProduct" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد إزالة هذا المنتج؟</value>
  </data>
  <data name="BasketFullOf" xml:space="preserve">
    <value>سلة مليئة</value>
  </data>
  <data name="BestMatch" xml:space="preserve">
    <value>أفضل نتيجة</value>
  </data>
  <data name="BiometricAuthentication" xml:space="preserve">
    <value>المصادقة البيومترية</value>
  </data>
  <data name="by" xml:space="preserve">
    <value>بواسطة</value>
  </data>
  <data name="CANCEL" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>التصنيفات</value>
  </data>
  <data name="Checkout" xml:space="preserve">
    <value>الشروع في الخروج</value>
  </data>
  <data name="CLEAR" xml:space="preserve">
    <value>مسح</value>
  </data>
  <data name="ClearSignature" xml:space="preserve">
    <value>مسح التوقيع</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>مكتمل</value>
  </data>
  <data name="Confirmation" xml:space="preserve">
    <value>تأكيد</value>
  </data>
  <data name="ConfirmDelivery" xml:space="preserve">
    <value>تأكيد التوصيل</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>البلد</value>
  </data>
  <data name="CustomerDelivery" xml:space="preserve">
    <value>توصيل للزبائن</value>
  </data>
  <data name="DailyOffers" xml:space="preserve">
    <value>العروض اليومية</value>
  </data>
  <data name="Dated" xml:space="preserve">
    <value>بتاريخ</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Delivery" xml:space="preserve">
    <value>التوصيل</value>
  </data>
  <data name="DeliveryAddress" xml:space="preserve">
    <value>عنوان التوصيل</value>
  </data>
  <data name="DeliveryConfirmation" xml:space="preserve">
    <value>تأكيد الإستلام</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>تاريخ التوصيل</value>
  </data>
  <data name="DESCRIPTION" xml:space="preserve">
    <value>وصف</value>
  </data>
  <data name="DifferentSellerMessage" xml:space="preserve">
    <value>المتجر الذي تحاول الطلب منه بائع مختلف ، هل ترغب في مسح سلة التسوق الخاصة بك ومتابعة إضافة هذا المنتج؟</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>المسافة</value>
  </data>
  <data name="Favourite" xml:space="preserve">
    <value>المفضلة</value>
  </data>
  <data name="Favourites" xml:space="preserve">
    <value>المفضلة</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>المرشحات</value>
  </data>
  <data name="Gems&amp;Goodies" xml:space="preserve">
    <value>بالمجوهرات و السلع المميزة</value>
  </data>
  <data name="GranViewModeltal" xml:space="preserve">
    <value>المبلغ الإجمالي</value>
  </data>
  <data name="Guest" xml:space="preserve">
    <value>زائر</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>البضائع</value>
  </data>
  <data name="Languages" xml:space="preserve">
    <value>اللغات</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>تسجيل دخول</value>
  </data>
  <data name="LogisticOperations" xml:space="preserve">
    <value>العمليات اللوجستية</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>تسجيل خروج</value>
  </data>
  <data name="MallatsProduct" xml:space="preserve">
    <value>منتج مولات</value>
  </data>
  <data name="Marketplace" xml:space="preserve">
    <value>السوق التجاري</value>
  </data>
  <data name="Menu" xml:space="preserve">
    <value>القائمة</value>
  </data>
  <data name="MerchantPickup" xml:space="preserve">
    <value>إستلام من التاجر</value>
  </data>
  <data name="Min" xml:space="preserve">
    <value>الحد الأدنى</value>
  </data>
  <data name="MyFavourites" xml:space="preserve">
    <value>المفضلة</value>
  </data>
  <data name="MyOrder" xml:space="preserve">
    <value>طلبي</value>
  </data>
  <data name="MyOrders" xml:space="preserve">
    <value>طلباتي</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>الإسم</value>
  </data>
  <data name="Net" xml:space="preserve">
    <value>الصافي</value>
  </data>
  <data name="Newest" xml:space="preserve">
    <value>الأجدد</value>
  </data>
  <data name="NoCategoriesWereFound" xml:space="preserve">
    <value>لم يتم الحصول على اي تصنيف!</value>
  </data>
  <data name="NoFavouritesWereAdded!" xml:space="preserve">
    <value>لم تتم إضافة مفضلات!</value>
  </data>
  <data name="NoFiltersWereFound" xml:space="preserve">
    <value>لم يتم العثور على اي مرشح!</value>
  </data>
  <data name="NoOrdersFound" xml:space="preserve">
    <value>لم يتم العثور على أي طلب!</value>
  </data>
  <data name="NoReviewsFound" xml:space="preserve">
    <value>لم يتم العثور على أي تقييم</value>
  </data>
  <data name="OnePlaceOneMarket" xml:space="preserve">
    <value>مكان واحد لسوق واحد.</value>
  </data>
  <data name="OrderDate" xml:space="preserve">
    <value>تاريخ الطلب</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>رقم الطلب</value>
  </data>
  <data name="OrderReceipt" xml:space="preserve">
    <value>إيصال الطلب</value>
  </data>
  <data name="OrderRequiresAction" xml:space="preserve">
    <value>الطلب يتطلب تدخل</value>
  </data>
  <data name="Others" xml:space="preserve">
    <value>اَخرى</value>
  </data>
  <data name="OutOfStock" xml:space="preserve">
    <value>غير متوفر</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>قيد الانتظار</value>
  </data>
  <data name="PleaseSelectOneOfTheFollowingOptions" xml:space="preserve">
    <value>الرجاء اختيار واحد من الخيارات التالية</value>
  </data>
  <data name="PleaseTakeActionBelow" xml:space="preserve">
    <value>يرجى اتخاذ الإجراءات أدناه</value>
  </data>
  <data name="PriceHighToLow" xml:space="preserve">
    <value>السعر- الأقل فالأكثر</value>
  </data>
  <data name="PriceLowToHigh" xml:space="preserve">
    <value>السعر- الأكبر فالأقل</value>
  </data>
  <data name="PrintTime" xml:space="preserve">
    <value>تاريخ الطباعة</value>
  </data>
  <data name="ProductDescription" xml:space="preserve">
    <value>وصف المنتج</value>
  </data>
  <data name="ProductSpecification" xml:space="preserve">
    <value>مواصفات المنتج</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>الملف الشخصي</value>
  </data>
  <data name="Qty" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="RATE" xml:space="preserve">
    <value>معدل</value>
  </data>
  <data name="Receipt" xml:space="preserve">
    <value>الإيصال</value>
  </data>
  <data name="ReceiverName" xml:space="preserve">
    <value>إسم المستلم</value>
  </data>
  <data name="ReceiverSignature" xml:space="preserve">
    <value>توقيع المستلم</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="RemoveProduct" xml:space="preserve">
    <value>حذف المنتج</value>
  </data>
  <data name="Reorder" xml:space="preserve">
    <value>إعادة طلب</value>
  </data>
  <data name="Reviews" xml:space="preserve">
    <value>التقييمات</value>
  </data>
  <data name="Search..." xml:space="preserve">
    <value>بحث...</value>
  </data>
  <data name="SelectAlternative" xml:space="preserve">
    <value>حدد البديل</value>
  </data>
  <data name="shareMessage" xml:space="preserve">
    <value>الق نظرة على هذا المنتج الذي وجدته في مولات.</value>
  </data>
  <data name="Shops" xml:space="preserve">
    <value>المتاجر</value>
  </data>
  <data name="SimilarProducts" xml:space="preserve">
    <value>منتجات مشابهة</value>
  </data>
  <data name="SoldBy" xml:space="preserve">
    <value>بواسطة</value>
  </data>
  <data name="SomeItemsNotAvailable" xml:space="preserve">
    <value>بعض البضائع غير متوفرة ، يرجى الاختيار من بين البدائل التي تم توفيرها.</value>
  </data>
  <data name="Sort" xml:space="preserve">
    <value>فرز</value>
  </data>
  <data name="SortBy" xml:space="preserve">
    <value>فرز حسب</value>
  </data>
  <data name="TakeAction" xml:space="preserve">
    <value>يتطلب تدخل</value>
  </data>
  <data name="TOTAL" xml:space="preserve">
    <value>الإجمالي</value>
  </data>
  <data name="TotalInclVAT&amp;Delivery" xml:space="preserve">
    <value>الإجمالي (شامل الضريبة والتوصيل)</value>
  </data>
  <data name="TypeTheNameHere" xml:space="preserve">
    <value>اكتب الاسم هنا ..</value>
  </data>
  <data name="VAT" xml:space="preserve">
    <value>الضريبة</value>
  </data>
  <data name="VATNO" xml:space="preserve">
    <value>رقم الضريبة</value>
  </data>
  <data name="ViewAll" xml:space="preserve">
    <value>مشاهدة الكل</value>
  </data>
  <data name="ViewAllShops" xml:space="preserve">
    <value>عرض كل المتاجر</value>
  </data>
  <data name="ViewAlternative" xml:space="preserve">
    <value>عرض البدائل</value>
  </data>
  <data name="ViewMarketplace" xml:space="preserve">
    <value>عرض السوق التجاري</value>
  </data>
  <data name="ViewShops" xml:space="preserve">
    <value>عرض المتاجر</value>
  </data>
</root>