using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;

namespace Optimum.Wallet.Application.Common.Helpers
{
    public class CacheHelper
    {
        private readonly ILogger<CacheHelper> _logger;
        private readonly IDistributedCache _distributedCache;

        public CacheHelper(IDistributedCache distributedCache, ILogger<CacheHelper> logger)
        {
            _logger = logger;
            _distributedCache = distributedCache;
        }

        public async Task SetCacheAsync<T>(string key, T value)
        {
            _logger.LogInformation("Distributed Cache Provider -- SetCacheAsync " + key);
            var cacheEntryOptions = new DistributedCacheEntryOptions()
                .SetSlidingExpiration(TimeSpan.FromMinutes(1))
                .SetAbsoluteExpiration(TimeSpan.FromMinutes(1));
            await _distributedCache.SetStringAsync(key, JsonConvert.SerializeObject(value, Formatting.None, new JsonSerializerSettings()
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            }), cacheEntryOptions);
        }

        public async Task SetCacheAsync<T>(string key, T value, TimeSpan timeSpan)
        {
            _logger.LogInformation("Distributed Cache Provider -- SetCacheAsync " + key);
            var cacheEntryOptions = new DistributedCacheEntryOptions()
                .SetSlidingExpiration(timeSpan)
                .SetAbsoluteExpiration(timeSpan);
            await _distributedCache.SetStringAsync(key, JsonConvert.SerializeObject(value, Formatting.None, new JsonSerializerSettings()
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            }), cacheEntryOptions);
        }

        public async Task<T> GetCacheAsync<T>(string key)
        {
            _logger.LogInformation("Distributed Cache Provider -- GetCacheAsync " + key);
            var value = await _distributedCache.GetStringAsync(key);
            if (value == null)
            {
                return default;
            }
            return JsonConvert.DeserializeObject<T>(value);
        }
    }
}
