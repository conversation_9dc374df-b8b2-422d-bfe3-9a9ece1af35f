using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Optimum.Wallet.Domain.Entities
{
    public class TokenRequests
    {
        [Key]
        public int ID { get; set; }
        public string UName { get; set; }
        public int ContactID { get; set; }
        public string Token { get; set; }
        public string RefreshToken { get; set; }
        public bool revoked { get; set; }
        public DateTime RequestTime { get; set; }
        public DateTime RefreshTokenExpiryTime { get; set; }
    }
}
