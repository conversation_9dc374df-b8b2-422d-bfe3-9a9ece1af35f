﻿using Microsoft.EntityFrameworkCore;
using System.Data;
using Optimum.Wallet.Domain.Entities;

namespace Optimum.Wallet.Infrastructure.Data.Context
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            foreach (var property in modelBuilder.Model.GetEntityTypes()
           .SelectMany(t => t.GetProperties())
           .Where(p => p.ClrType == typeof(decimal) || p.ClrType == typeof(decimal?)))
            {
                property.SetColumnType("decimal(18,6)");
            }
            base.OnModelCreating(modelBuilder);
        }

        public DbSet<TokenRequests> TokenRequests { get; set; }
        public DbSet<ContactTbl> ContactTbl { get; set; }
        public DbSet<CustomerFile> CustomerFile { get; set; }
        public DbSet<CustomerServicesTbl> CustomerServicesTbl { get; set; }

        public DbSet<PAYMENT_GATEWAY_TRAN_RESPONSE> PAYMENT_GATEWAY_TRAN_RESPONSE { get; set; }
    }
}
