<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Optimum.Wallet.Api</RootNamespace>
	  <TargetLatestRuntimePatch>true</TargetLatestRuntimePatch>
	  <GenerateRuntimeConfigurationFiles>True</GenerateRuntimeConfigurationFiles>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.17">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.17" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
  </ItemGroup>

	<ItemGroup>
		<Compile Remove="Logs\**" />
		<Content Remove="Logs\**" />
		<EmbeddedResource Remove="Logs\**" />
		<None Remove="Logs\**" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="wwwroot\" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Optimum.Wallet.Core\Optimum.Wallet.Core.csproj" />
	  <ProjectReference Include="..\Optimum.Wallet.Application\Optimum.Wallet.Application.csproj" />
	  <ProjectReference Include="..\Optimum.Wallet.Infrastructure\Optimum.Wallet.Infrastructure.csproj" />
	</ItemGroup>

</Project>
