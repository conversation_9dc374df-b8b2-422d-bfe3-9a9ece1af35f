using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Optimum.Wallet.Application.Common;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Application.Interfaces;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Extensions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;


namespace Optimum.Wallet.Application.Services
{
    public class PaymentService : IPaymentService
    {
        private readonly ILogger<PaymentService> Logger;
        private readonly IFormRepository _formRepository;

        public PaymentService(ILogger<PaymentService> logger,IFormRepository formRepository) 
        { 
            Logger = logger;
            _formRepository = formRepository;
        }


        public async Task ProcessPayment(int transactionId,
        string responseCode, decimal amount,
        int statusCode, string exceptionMessage,
        int formId, int formTableId, bool processTransaction, 
        string paymentId, string tranId, string refNo, string tranType = "", string cardNumber = "", int caid = -1, decimal targetAmount = 0)
        {
            Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] Entered");

            var model = new ResponseViewModel
            {
                TransactionNumber = paymentId,
                AuthorizationNumber = tranId,
                ReferenceNumber = refNo,
                TransactionMessage = PaymentCodesHelper.GetTxnResponseCodeDescriptionBenefit(responseCode)
            };

            // Keep track of the values that will be used to update the payment form
            var paymentStatus = StatusValues.DEFAULT;

            Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] Status: {statusCode}");
            var declined = "0";
            var approved = "0";
            switch (statusCode)
            {
                case PaymentGatewayStatuses.APPROVED:
                    model.Status = "PayApprovedStatus";
                    model.Message = string.Format("PayApprovedMsg", amount.FormatAmount());
                    declined = "0";
                    approved = "1";
                    paymentStatus = StatusValues.AWAITING; // Payment approved, awaiting PowerCard update
                    if (processTransaction)
                    {
                        var response = await PaymentSucessProcess(formId, formTableId, amount, refNo, tranType, cardNumber, caid, targetAmount == 0 ? amount : targetAmount);
                        Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] PaymentSucessProcess Response: {JsonConvert.SerializeObject(response)}");
                        model.Fields = response.Fields;
                    }
                    break;
                case PaymentGatewayStatuses.DECLINED:
                case PaymentGatewayStatuses.UNKNOWN:
                    model.Status = "PayDeclinedStatus";
                    model.Message = "PayDeclinedMsg";
                    declined = "1";
                    approved = "0";
                    paymentStatus = StatusValues.DECLINED; // Payment declined
                    //Update the status
                    DapperHelper.Exceute($"UPDATE ChooseListDetailsTbl SET CLIId='{StatusValues.DECLINED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS}", null);
                    break;
            }

            // Check if we have to do any processing for this transaction.
            // The transaction must be processed only once after the initial payment is done.
            // If the user loads this page multiple times, it must not process transaction more than once!            
            Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] processTransaction: {processTransaction}");
            if (processTransaction)
            {
                Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] updating transaction details.");
                await DapperHelper.ExceuteAsync(
                    " UPDATE PAYMENT_GATEWAY_TRANSACTIONS" +
                    " SET PG_RES_TIME = @PG_RES_TIME," +
                    "     PG_TRNO = null," +
                    "     PG_RRNO = @PG_RRNO," +
                    "     PG_AUTHID = @PG_AUTHID," +
                    "     ReceiptProcessed = @ReceiptProcessed," +
                    "     Approved = @Approved," +
                    "     Declined = @Declined " +
                    " WHERE ID = @ID",
                    new
                    {
                        PG_RES_TIME = DateTime.Now,
                        PG_RRNO = model.TransactionNumber,
                        PG_AUTHID = model.AuthorizationNumber,
                        ReceiptProcessed = "1",
                        Approved = approved,
                        Declined = declined,
                        ID = transactionId
                    }, "Service"
                );
                Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] updated transaction details.");

                // Build a dictionary of fields that needs to be updated
                var fieldToUpdate = new Dictionary<int, string>
                {
                    {ControlTypes.BALANCE_TODAY, "-=" + amount}, // Subtract the paid amount from the current balance
                    {ControlTypes.PAYMENT_REF_NO, model.ReferenceNumber},
                    {ControlTypes.STATUS_MESSAGE, model.TransactionMessage},
                    {ControlTypes.STATUS, paymentStatus + ""}
                };

                // Update the form data
                Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] updating form data.");
                var fieldsUpdated = await _formRepository.UpdateRequest(formTableId, fieldToUpdate);
                Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] updated form data - status: {fieldsUpdated}.");

                // Send a notification email
                Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] Sending email.");
                var emailSent = await _formRepository.ProcessEmailSms(formTableId, formId, 0, null);
                Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] Sent email - {emailSent}. Saving changes.");
                DapperHelper.Exceute(
                    " UPDATE PAYMENT_GATEWAY_TRANSACTIONS" +
                    " SET EmailSend = @EmailSend" +
                    " WHERE ID = @ID",
                    new
                    {
                        EmailSend = emailSent > 0,
                        ID = transactionId
                    }, "Service"
                );
                Logger.LogInformation($"[PaymentService/ShowTransactionStatus{formTableId}] Saved changes.");
            }

            // Fetch the updated form data
            var isLogRequest = new List<int> { PageTypes.WALLET_ADD_MONEY, PageTypes.CARD_ADD_MONEY, PageTypes.SERVICE_SMART_MONEY }.Contains(formId);
            var formData = await _formRepository.GetFormById(formId, 0, 0, formTableId, true, null, isLogRequest);
            //Because may the response already pass fields if it its pass it don't override it
            if (model.Fields == null || model.Fields.Count <= 0)
            {
                model.Fields = formData.ToList();
            }

            model.approved = approved == "1";

            Logger.LogInformation($"[PaymentService/ShowTransactionStatus] Exited - Model: {JsonConvert.SerializeObject(model)}");
            // return View("~/Views/Home/Index.cshtml", model);
        }
        
        
        private async Task<ResponseViewModel> PaymentSucessProcess(int formId, int formTableId, decimal amount, string refNo, string tranType, string cardNumber = "", int caid = -1, decimal targetAmount = 0)
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogInformation($"[PaymentService/PaymentSucessProcess/{runId}] CAID:{caid} | formId:{formId} | formTableId:{formTableId} | amount:{amount} | refNo:{refNo} | tranType:{tranType}");

            ResponseViewModel Response = new ResponseViewModel()
            {
                Status = NotificationType.ERROR,
                Message = "ErrorOccurred"
            };

            //GET BFC CAID from environment variables
            var fromCAID = 0;
            int.TryParse(WalletApplication.GetEnvVariable("MAIN-CAID"), out fromCAID);

            var typeId = -1;
            switch (tranType)
            {
                case "CARD-ADD-MONEY":
                    Logger.LogInformation($"[PaymentService/PaymentSucessProcess/CARD-ADD-MONEY/{runId}] Parameters: cardNumber:{cardNumber} | amount:{amount} | amount:{amount} | refNo:{refNo} | fromCAID:{fromCAID}");
                    typeId = 7; // PG to Card
                    break;
                case "WALLET-ADD-MONEY":
                    Logger.LogInformation($"[PaymentService/PaymentSucessProcess/WALLET-ADD-MONEY/{runId}] Parameters: fromCAID:{fromCAID} | amount:{amount} | ApprovalCode:{refNo} | FormID: {formId} | CSID: {formTableId}");
                    typeId = 6; // PG to Wallet
                    break;
            }

            if (typeId == 6 || typeId == 7)
            {
                //Call Procedure to add money to wallet
                #region Exec usp_CreateGeneralReceiptBenfit     
                Logger.LogInformation($"[PaymentService/PaymentSucessProcess/{tranType}/{runId}] Parameters: Type:{typeId} | ToCAID:{caid} | FromAmount:{amount} | ApprovalCode:{refNo} | FormID: {formId} | CSID: {formTableId} | ToAmount: {targetAmount}");
                var result = await DapperHelper.ExecuteScalarAsync<string>(
                    $" EXEC dbo.usp_CreateGeneralReceiptBenfit @Type = {typeId}, @ToCAID = {caid}, @FromAmount = {amount}, @ApprovalCode = {refNo}, @BatchNo = 0, @FormID = {formId}, @CSID = {formTableId}, @ToAmount = {(targetAmount == 0 ? amount : targetAmount)}, @ExchangeRate = {(targetAmount == 0 ? 1 : amount / targetAmount)}"
                );
                Logger.LogInformation($"[PaymentService/PaymentSucessProcess/{tranType}/{runId}] result of usp_CreateGeneralReceiptBenfit :{result}");
                #endregion


                #region Validate the ReceiptId
                int ReceiptId = 0;
                int.TryParse(result, out ReceiptId);
                //Executed successfully
                if (ReceiptId > 0)
                {
                    await DapperHelper.ExceuteAsync($"UPDATE ChooseListDetailsLogTbl SET CLIId='{StatusValues.APPROVED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS};"
                        + $"UPDATE ChooseListDetailsLogTbl SET CLIId='{result}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.RECEIPT_ID};"
                        );
                    Response.Status = NotificationType.SUCCESS;
                    Response.TransactionNumber = ReceiptId + "";
                }
                else
                {
                    await DapperHelper.ExceuteAsync($"UPDATE ChooseListDetailsLogTbl SET CLIId='{StatusValues.DECLINED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS}");
                }
                #endregion
            }

            return Response;
        }

    }
}
