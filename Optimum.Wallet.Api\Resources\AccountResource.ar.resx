﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddAddress" xml:space="preserve">
    <value>إضافة عنوان جديد</value>
  </data>
  <data name="Addresses" xml:space="preserve">
    <value>العنواين</value>
  </data>
  <data name="AddressName" xml:space="preserve">
    <value>إسم العنوان</value>
  </data>
  <data name="AlreadyRegistered" xml:space="preserve">
    <value>مسجل بالفعل؟</value>
  </data>
  <data name="and" xml:space="preserve">
    <value>و</value>
  </data>
  <data name="AndTheGoogle" xml:space="preserve">
    <value>وتنطبق</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>تطبيق</value>
  </data>
  <data name="ApplyNow" xml:space="preserve">
    <value>سجل الآن</value>
  </data>
  <data name="AvailableSlots" xml:space="preserve">
    <value>الفترات المتوفرة</value>
  </data>
  <data name="BiometricAuthentication" xml:space="preserve">
    <value>الدخول عن طريق البصمة</value>
  </data>
  <data name="BiometricAuthenticationMsg" xml:space="preserve">
    <value>يرجى تسجيل الدخول باستخدام بيانات الاعتماد الخاصة بك لتفعيل البصمة.</value>
  </data>
  <data name="Block" xml:space="preserve">
    <value>المجمع</value>
  </data>
  <data name="BlockNumber" xml:space="preserve">
    <value>رقم المجمع</value>
  </data>
  <data name="Box" xml:space="preserve">
    <value>صندوق البريد</value>
  </data>
  <data name="BoxNumber" xml:space="preserve">
    <value>رقم صندوق البريد</value>
  </data>
  <data name="Building" xml:space="preserve">
    <value>المبنى</value>
  </data>
  <data name="BuildingNameNumber" xml:space="preserve">
    <value>رقم او إسم المبنى</value>
  </data>
  <data name="CardNumber" xml:space="preserve">
    <value>رقم البطاقة</value>
  </data>
  <data name="Cart" xml:space="preserve">
    <value>عربة التسوق</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>غَيّر</value>
  </data>
  <data name="ChangePIN" xml:space="preserve">
    <value>تغيير كلمة المرور</value>
  </data>
  <data name="ChangePIN_ConfirmPIN" xml:space="preserve">
    <value>تأكيد كلمة المرور</value>
  </data>
  <data name="ChangePIN_CurrentPIN" xml:space="preserve">
    <value>كلمة المرور الحالية</value>
  </data>
  <data name="ChangePIN_NewPIN" xml:space="preserve">
    <value>كلمة المرور الجديدة</value>
  </data>
  <data name="Checkout" xml:space="preserve">
    <value>الدفع</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>المدينة</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>الخروج</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>رمز التحقق</value>
  </data>
  <data name="CodeEnter" xml:space="preserve">
    <value>أدخل الرمز المكون من 4 أرقام الذي تم إرساله إلى رقم هاتفك المحمول</value>
  </data>
  <data name="CodeFailedHint" xml:space="preserve">
    <value>لم نتمكن من التحقق من رقم هاتفك المحمول! يرجى إدخال الرمز المكون من 4 أرقام الذي تم إرساله إلى رقم هاتفك المحمول:</value>
  </data>
  <data name="CodeMobileHint" xml:space="preserve">
    <value>انتظر بينما نتحقق من رقم هاتفك المحمول</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>تأكيد كلمة السر</value>
  </data>
  <data name="ConfirmYourPassword" xml:space="preserve">
    <value>تأكيد كلمة السر الخاصة بك</value>
  </data>
  <data name="ContinueasaGuest" xml:space="preserve">
    <value>المواصلة كزائر</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>البلد</value>
  </data>
  <data name="CreateAccountApply" xml:space="preserve">
    <value>الخاصة بقوقل</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>إسم الزبون</value>
  </data>
  <data name="data" xml:space="preserve">
    <value>بيانات</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="DeliveryChargeExclVAT" xml:space="preserve">
    <value>التوصيل دون الضريبة</value>
  </data>
  <data name="DeliveryChargeVAT" xml:space="preserve">
    <value>التوصيل مع الضريبة</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>تاريخ التوصيل</value>
  </data>
  <data name="DeliveryMethod" xml:space="preserve">
    <value>طريقة التوصيل</value>
  </data>
  <data name="EditAddress" xml:space="preserve">
    <value>تعديل العنوان</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>الإيميل</value>
  </data>
  <data name="EmailAddress" xml:space="preserve">
    <value>عنوان البريد الالكترونى</value>
  </data>
  <data name="EmbossingName" xml:space="preserve">
    <value>الإسم في بطاقتك</value>
  </data>
  <data name="Enter6digitemailverificationcode" xml:space="preserve">
    <value>أدخل رمز التحقق المكون من 6 أرقام في البريد الإلكتروني</value>
  </data>
  <data name="EnterEmailCode" xml:space="preserve">
    <value>أدخل رمز التحقق</value>
  </data>
  <data name="EnterNewPassword" xml:space="preserve">
    <value>أدخل كلمة مرور جديدة</value>
  </data>
  <data name="EnterSMSCode" xml:space="preserve">
    <value>أدخل رمز التحقق</value>
  </data>
  <data name="EnterSMSVerificationCode" xml:space="preserve">
    <value>أدخل رمز التحقق المكون من 6 أرقام</value>
  </data>
  <data name="ErrorPrivacySettings" xml:space="preserve">
    <value>فشل تحديث بعض إعدادات الخصوصية الخاصة بك! الرجاء معاودة المحاولة في وقت لاحق.</value>
  </data>
  <data name="Favourite" xml:space="preserve">
    <value>المفضلة</value>
  </data>
  <data name="Filltheformtologin" xml:space="preserve">
    <value>املأ النموذج لتسجيل الدخول</value>
  </data>
  <data name="FillTheFormToResetYourPassword" xml:space="preserve">
    <value>املأ الإستمارة لإعادة تعيين كلمة المرور الخاصة بك</value>
  </data>
  <data name="FillTheFormToSignUp" xml:space="preserve">
    <value>املأ الإستمارة للتسجيل</value>
  </data>
  <data name="FingerprintUnlock" xml:space="preserve">
    <value>تمكين الدخول عن طريق البصمة</value>
  </data>
  <data name="Flat" xml:space="preserve">
    <value>الشقة</value>
  </data>
  <data name="Flat Number" xml:space="preserve">
    <value>رقم الشقة</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>هل نسيت كلمة السر</value>
  </data>
  <data name="ForgotPIN" xml:space="preserve">
    <value>نسيت كلمة المرور؟</value>
  </data>
  <data name="GranViewModeltalExcl.VAT" xml:space="preserve">
    <value>المجموع الكلي باستثناء ضريبة القيمة المضافة</value>
  </data>
  <data name="GranViewModeltalIncl.VAT" xml:space="preserve">
    <value>المجموع الكلي شامل ضريبة القيمة المضافة</value>
  </data>
  <data name="Guest" xml:space="preserve">
    <value>زائر</value>
  </data>
  <data name="HintResetPin" xml:space="preserve">
    <value>يرجى إدخال كلمة المرور لإعادة تعيين الوصول إلى حسابك</value>
  </data>
  <data name="InvalidVerificationCodeError" xml:space="preserve">
    <value>رمز التحقق غير صالح!</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>اللغة</value>
  </data>
  <data name="LanguageSettings" xml:space="preserve">
    <value>إعدادات اللغة</value>
  </data>
  <data name="Last4Digits" xml:space="preserve">
    <value>الأرقام الأربعة الأخيرة للبطاقة</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>تحميل...</value>
  </data>
  <data name="LoggingIn" xml:space="preserve">
    <value>جاري تسجيل الدخول</value>
  </data>
  <data name="LogIn" xml:space="preserve">
    <value>تسجيل الدخول</value>
  </data>
  <data name="LoginTitle" xml:space="preserve">
    <value>تسجيل الدخول</value>
  </data>
  <data name="MobileAlreadyRegisteredError" xml:space="preserve">
    <value>رقم الهاتف المحمول المقدم مسجل بالفعل!</value>
  </data>
  <data name="MobileNumber" xml:space="preserve">
    <value>رقم الهاتف</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>الإسم</value>
  </data>
  <data name="NewAddress" xml:space="preserve">
    <value>عنوان جديد</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>كلمة السر الجديدة</value>
  </data>
  <data name="NewUser" xml:space="preserve">
    <value>مستخدم جديد؟</value>
  </data>
  <data name="Newuser?Registernow" xml:space="preserve">
    <value>مستخدم جديد؟ سجل الآن</value>
  </data>
  <data name="NonMessage" xml:space="preserve">
    <value>لا يوجد</value>
  </data>
  <data name="Notifications" xml:space="preserve">
    <value>الإشعارات</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>كلمة المرور</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>طريقة الدفع</value>
  </data>
  <data name="PaymentSummary" xml:space="preserve">
    <value>ملخص الدفع</value>
  </data>
  <data name="pending" xml:space="preserve">
    <value>{0} قيد الانتظار</value>
    <comment>{0}</comment>
  </data>
  <data name="PersonalID" xml:space="preserve">
    <value>رقم الهوية</value>
  </data>
  <data name="PersonalInformation" xml:space="preserve">
    <value>البيانات الشخصية</value>
  </data>
  <data name="PersonalInformation_Address" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="PersonalInformation_CPR" xml:space="preserve">
    <value>الرقم الشخصي</value>
  </data>
  <data name="PersonalInformation_DOB" xml:space="preserve">
    <value>تاريخ الميلاد</value>
  </data>
  <data name="PersonalInformation_Email" xml:space="preserve">
    <value>البريد الإلكتروني</value>
  </data>
  <data name="PersonalInformation_MobileNumber" xml:space="preserve">
    <value>رقم الهاتف</value>
  </data>
  <data name="PersonalInformation_Name" xml:space="preserve">
    <value>الأسم</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>رقم الهاتف</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>رقم صندوق البريد</value>
  </data>
  <data name="PIN" xml:space="preserve">
    <value>كلمة المرور</value>
  </data>
  <data name="PinChangeFail" xml:space="preserve">
    <value>فشل في تغيير كلمة المرور. الرجاء معاودة المحاولة في وقت لاحق.</value>
  </data>
  <data name="PinChangeSuccess" xml:space="preserve">
    <value>تم تغيير رقم كلمة المرور الخاص بك بنجاح!</value>
  </data>
  <data name="PinExistingError" xml:space="preserve">
    <value>لا يمكنك استخدام كلمة المرور الحالية.</value>
  </data>
  <data name="PinInvalidError" xml:space="preserve">
    <value>لقد أدخلت كلمة مرور غير صحيحة.</value>
  </data>
  <data name="PinSMSFail" xml:space="preserve">
    <value>فشل في إرسال كلمة المرور الخاصة بك! الرجاء معاودة المحاولة في وقت لاحق.</value>
  </data>
  <data name="PinSMSSuccess" xml:space="preserve">
    <value>ستتلقى كلمة المرور قريبًا عبر الرسائل القصيرة. يرجى تغييره من خلال خدمة تغيير كلمة المرور.</value>
  </data>
  <data name="PinUpdatedSuccess" xml:space="preserve">
    <value>تم تحديث كلمة المرور الخاصة بك بنجاح. الرجاء استخدام كلمة المرور الجديدة لتسجيل الدخول.</value>
  </data>
  <data name="Please" xml:space="preserve">
    <value>من فضلك</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>سياسة الخصوصية</value>
  </data>
  <data name="PrivacySettings" xml:space="preserve">
    <value>إعدادات الخصوصية</value>
  </data>
  <data name="Proceed" xml:space="preserve">
    <value>المتابعة</value>
  </data>
  <data name="ProceeViewModelCheckout" xml:space="preserve">
    <value>الشروع في الخروج</value>
  </data>
  <data name="ProceeViewModelCheckoutasaGuest" xml:space="preserve">
    <value>الشروع في الخروج كزائر</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>الملف الشخصي</value>
  </data>
  <data name="ProfileChangeSuccess" xml:space="preserve">
    <value>تم تحديث بيانات ملفك الشخصي بنجاح</value>
  </data>
  <data name="ProfileDataError" xml:space="preserve">
    <value>فشل في استعادة تفاصيل ملفك الشخصي. الرجاء معاودة المحاولة في وقت لاحق.</value>
  </data>
  <data name="ProfileNoChangeInfo" xml:space="preserve">
    <value>لم تجرِ أي تغييرات على ملفك الشخصي</value>
  </data>
  <data name="ProfileSaveButton" xml:space="preserve">
    <value>حفظ التغييرات</value>
  </data>
  <data name="ProfileSaveButtonLoading" xml:space="preserve">
    <value>جاري الحفظ</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>سجل</value>
  </data>
  <data name="RegisterNow" xml:space="preserve">
    <value>سجل الان</value>
  </data>
  <data name="RemeberMe" xml:space="preserve">
    <value>تذكرني؟</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>إعادة تعيين كلمة المرور</value>
  </data>
  <data name="ResetPIN" xml:space="preserve">
    <value>إعادة تعيين كلمة المرور</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>أعد المحاولة</value>
  </data>
  <data name="Road" xml:space="preserve">
    <value>الطريق</value>
  </data>
  <data name="RoadNumber" xml:space="preserve">
    <value>رقم الطريق</value>
  </data>
  <data name="Schedule" xml:space="preserve">
    <value>جدول المواعيد</value>
  </data>
  <data name="SearchTerm" xml:space="preserve">
    <value>مصطلح البحث</value>
  </data>
  <data name="SelectCity" xml:space="preserve">
    <value>إختر المدينة</value>
  </data>
  <data name="SelectCountry" xml:space="preserve">
    <value>إختر البلد</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>إرسال</value>
  </data>
  <data name="SubTotal" xml:space="preserve">
    <value>المجموع الفرعي</value>
  </data>
  <data name="SuccessPrivacySettings" xml:space="preserve">
    <value>تم تحديث إعدادات الخصوصية الخاصة بك بنجاح!</value>
  </data>
  <data name="TermsAndConditions" xml:space="preserve">
    <value>الأحكام والشروط</value>
  </data>
  <data name="TermsOfService" xml:space="preserve">
    <value>شروط الخدمة</value>
  </data>
  <data name="ThereNoItemsInCart" xml:space="preserve">
    <value>لا توجد أي منتجات في سلة التسوق الخاصة بك!</value>
  </data>
  <data name="ThisSiteIsProtectedBy" xml:space="preserve">
    <value>هذا الموقع محمي بواسطة</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>الرقم الشخصي</value>
  </data>
  <data name="VATAmount" xml:space="preserve">
    <value>قيمة الضريبة</value>
  </data>
  <data name="Verification" xml:space="preserve">
    <value>التحقق</value>
  </data>
  <data name="Verify" xml:space="preserve">
    <value>تأكد</value>
  </data>
  <data name="VerifyButton" xml:space="preserve">
    <value>تحقق</value>
  </data>
  <data name="Verifying" xml:space="preserve">
    <value>جاري التحقق</value>
  </data>
  <data name="YourEmailAddress" xml:space="preserve">
    <value>عنوان بريدك  الإلكتروني</value>
  </data>
  <data name="YourFirstandLastName" xml:space="preserve">
    <value>إسمك الأول و الأخير</value>
  </data>
  <data name="YourPassword" xml:space="preserve">
    <value>كلمة السر خاصتك</value>
  </data>
  <data name="YourPersonalID" xml:space="preserve">
    <value>رقم هويتك</value>
  </data>
  <data name="YourPhoneNumber" xml:space="preserve">
    <value>رقم الهاتف الخاص بك</value>
  </data>
</root>