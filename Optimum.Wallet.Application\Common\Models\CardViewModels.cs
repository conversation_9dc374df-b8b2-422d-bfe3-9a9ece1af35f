using Microsoft.AspNetCore.Http;
using Microsoft.VisualBasic.FileIO;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Optimum.Wallet.Application.Common.Models
{
    public class CardTbl
    {
        public int ID { get; set; }
        public bool MultiCurrency { get; set; }
        public int Product { get; set; }
        public string Category { get; set; }
        public string CardNumber { get; set; }
        public string NameOnCard { get; set; }
        public string ExpiryDate { get; set; }
        public decimal CreditLimit { get; set; }
        public decimal CreditLimitUtilised { get; set; }
        public decimal CashLimit { get; set; }
        public decimal CashLimitUsed { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal BalanceAsOfToday { get; set; }
        public decimal HoldAmount { get; set; }
        public decimal AvailableBalance { get; set; }
        public decimal MinimumBalance { get; set; }
        public int Status { get; set; }
        public string CPR { get; set; }
        public bool IsSupplementary { get; set; }
        public string SuppCardNo { get; set; }
        public string SuppName { get; set; }
        public string SuppCPR { get; set; }
        public string SuppEmbossingName { get; set; }
        public string SuppRelationship { get; set; }
        public int ParentCardId { get; set; }
        public string UserModified { get; set; }
        public DateTime DateModified { get; set; }
        public int CustomerId { get; set; }
        public int ContactId { get; set; }
        public bool Active { get; set; }
        public string ProductName { get; set; }
        public string CategoryName { get; set; }
    }

    public class CardViewModel
    {
        public int CardId { get; set; }
        public int ParentCardId { get; set; }
        public string CardHolderName { get; set; }
        public string CardHolderCpr { get; set; }
        public string CardNumber { get; set; }
        public string CardType { get; set; }
        public int CardTypeId { get; set; }
        public string CardClass { get; set; }
        public int CardClassId { get; set; }
        public string CardExpiry { get; set; }
        public List<int> PossibleUpgrades { get; set; }
        public CardBalance Balance { get; set; }
        public string Status { get; set; }
        public int StatusId { get; set; }
        public bool IsMultiCurrency { get; set; }
        public string EncryptedCAID { get; set; }
        public string HashedCAID { get; set; }
        public string SwiftCode { get; set; }
        public int NoOfDecimals { get; set; }
        public string CprId { get; set; }
        public string CardAccountTypeId { get; set; }
    }

    public class CardDetailsViewModel : CardViewModel
    {
        public List<SuppCardDetailsViewModel> SupplementaryCards { get; set; }
    }

    public class SuppCardDetailsViewModel : CardViewModel
    {
        public string SuppCardNumber { get; set; }
        public string SuppRelationship { get; set; }
    }

    public class CardBalance
    {
        public decimal CreditLimit { get; set; }
        public decimal CreditLimitUsed { get; set; }
        public decimal ParentCreditLimit { get; set; }
        public decimal CashLimit { get; set; }
        public decimal CashLimitUsed { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal TodayBalance { get; set; }
        public decimal HoldAmount { get; set; }
        public decimal AvailableBalance { get; set; }
        public decimal MinimumBalance { get; set; }
        public DateTime DueDate { get; set; }
    }

    public class suppCardWithTransaction
    {
        public SuppCardDetailsViewModel SuppCardDetail { get; set; }
        public List<CardStatementModelView> SuppCardTransactions { get; set; }
    }


    public class CardStatementModelView
    {
        public DateTime Date { get; set; }
        public DateTime PostingDate { get; set; }
        public string Description { get; set; }
        public decimal TransactionAmount { get; set; }
        public string TransactionCurrency { get; set; }
        public decimal BillingAmount { get; set; }
        public string BillingCurrency { get; set; }
        public string MerchantName { get; set; }
        public string CardNumber { get; set; }
        public bool IsSupplementary { get; set; }
        public string SuppCardHolderName { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal MinimumDue { get; set; }
        public DateTime DueDate { get; set; }
        public string TransactionType { get; set; }
        public string TransactionCode { get; set; }
        public string VatType { get; set; }
        public decimal VatAmount { get; set; }
        public string MCCId { get; set; }
        public string MCCName { get; set; }
        public string MicrofilmNumber { get; set; }
        public bool IsHasInstallment { get; set; }
        public int NoOfDecimals { get; set; }
    }

    public class WalletAccount
    {
        public int CAID { get; set; }
        public string EncryptedCAID { get; set; }
        public string HashedCAID { get; set; }
        public string Name { get; set; }
        public decimal Balance { get; set; }
        public decimal HoldBalance { get; set; }
        public int CcyCode { get; set; }
        public string SwiftCode { get; set; }
        public string CcyName { get; set; }
        public int NoOfDecimals { get; set; }
        public string Image { get; set; }
        public string AccountNumber { get; set; }
        public string AccountSubType { get; set; }
        public int StatusID { get; set; }
        public bool isLinked { get; set; } = false;
    }

    public class CurrencyWallet
    {
        public string Currency { get; set; }
        public string Icon { get; set; }
        public decimal AvailableBalance { get; set; }
        public decimal HoldAmount { get; set; }
        public string Title { get; set; }
        public int NoOfDecimals { get; set; }
        public bool IsHome { get; set; }
    }


    public class WalletExchangeQuote
    {
        public decimal CalculatedAmount { get; set; }
        public decimal ExchangeRate { get; set; }
        public string SourceSwiftCode { get; set; }
        public int SourceDecimal { get; set; }
        public int SourceCcyCode { get; set; }
        public string TargetSwiftCode { get; set; }
        public int TargetDecimal { get; set; }
        public int TargetCcyCode { get; set; }
        public decimal Fees { get; set; }
        public decimal Vat { get; set; }
        public bool IsSourceAmount { get; set; }
        public int FromCAID { get; set; }
        public int ToCAID { get; set; }
    }

    public class GoalAccount : WalletAccount
    {
        public DateTime DeadLine { get; set; }
        public decimal WeeklySavings { get; set; }
        public decimal Limit { get; set; }
        public string Photo { get; set; }
        public int RefCAID { get; set; }
        public DateTime StartDate { get; set; }
        public decimal RetentionChippingAmt { get; set; }
        public int RetentionFrequency { get; set; }
        public string RetentionFrequencyTitle { get; set; }
        public int RefContactId { get; set; }
        public int sort { get; set; }
        public int GoalDefaultCAID { get; set; }
    }


    public class ChildAccount : WalletAccount
    {
        public int ContactID { get; set; }
        public string CONT_CPR { get; set; }
        public decimal AvailableToSpend { get; set; }
        public decimal spent { get; set; }
        public decimal totalSavings { get; set; }
        public decimal totalGoals { get; set; }
        public string Photo { get; set; }
        public decimal Limit { get; set; }
        public decimal Hold { get; set; }
        public decimal allowance { get; set; }
        public int allowanceDaysNo { get; set; }
        public decimal incompletedtaskAmount { get; set; }
        public int incompletedtaskCount { get; set; }
        public string cardClass { get; set; }
        public string ParentCPR { get; set; }
        public bool isSymbolMoney { get; set; } = false;
        public decimal SymbolValue { get; set; }
        public decimal completedtaskToBePaidAmount { get; set; }
        public int completedtaskToBePaidCount { get; set; }
        public bool hasCard { get; set; }
        public string SAvailableToSpend { get; set; }
        public string sSpent { get; set; }
        public string sTotalSavings { get; set; }
        public string sTotalGoals { get; set; }
        public decimal ExceptionalPayments { get; set; }
    }


    public class EarningModelView
    {
        public int contactId { get; set; }
        //TODO:may I will need to remove the name attribute and put WalletAccount class
        //if I did I need to modify the proc.
        public string ContactName { get; set; }
        public decimal done { get; set; }
        public decimal UnderApproval { get; set; }
        public decimal toDo { get; set; }
        public decimal allowance { get; set; }
        public string currentAllowanceRetention { get; set; }
        public decimal bonuses { get; set; }
        public List<TaskModelView> tasksCompleted { get; set; }
        public List<TaskModelView> tasksForApproval { get; set; }
        public List<TaskModelView> tasksIncompleted { get; set; }
        public bool isSymbolMoney { get; set; } = false;
        public decimal SymbolValue { get; set; }

    }

    public class EarningBarModelView
    {
        public int contactId { get; set; }
        public int CAID { get; set; }

        public decimal Amount { get; set; }
        public int statusId { get; set; }
        public string status { get; set; }

    }

    public class TaskModelView
    {
        public int Id { get; set; }
        public string taskName { get; set; }
        public decimal taskAmount { get; set; }
        public string taskFrequency { get; set; }
        public DateTime inputDate { get; set; }
        public string Status { get; set; }
        public int StatusId { get; set; }
        public int CAID { get; set; }
        public string taskAddedBy { get; set; }
        public string taskAddedBy_UserName { get; set; }

    }

    public class TaskCommentsModelView
    {
        public int Id { get; set; }
        public string comment { get; set; }
        public string Photo { get; set; }
        public string fromName { get; set; }/*writer,Sender*/
        public int FromContactId { get; set; }
        public string FromContactPhoto { get; set; }
        public DateTime date { get; set; }
        public bool isChild { get; set; }

    }



    public class SpendModelView
    {
        public int Id { get; set; }
        public string SpenViewModelName { get; set; }
        public decimal Amount { get; set; }
        public DateTime Date { get; set; }
        public string Icon { get; set; }
        public int contactId { get; set; }
    }
    //Allowances && Bonuses
    public class BenefitModelView
    {
        public int tranId { get; set; }
        public string benefitTypeName { get; set; }
        public int benefitTypeId { get; set; }
        public decimal Amount { get; set; }
        public DateTime creditedDate { get; set; }
        public string retentionName { get; set; }
        public int retentionId { get; set; }
        public int toContactId { get; set; }
        public int fromContactId { get; set; }
        public string fromRelationName { get; set; }
        public string FromCardNumber { get; set; }
        public int FromCAID { get; set; }
        public bool isActive { get; set; }
    }


    public class ChildAccountDetails : ChildAccount
    {
        public List<GoalAccount> GoalsAccounts { get; set; }
    }



    public class ChildCardAccountViewModel
    {
        public List<ChildAccountDetails> AccountDetails { get; set; }
        public List<CardDetailsViewModel> CardDetails { get; set; }
        public List<BatchCardsStatementsModelView> CardStatements { get; set; }
    }
    public class BatchCardsStatementsModelView
    {
        public string CardNumber { get; set; }
        public List<CardStatementModelView> CardStatements { get; set; }
        public Dictionary<string, string> StatementList { get; set; }
    }

    public class SpendingLimit
    {
        public decimal weeklySpendLimit { get; set; }
        public int weeklyTransactionsCount { get; set; }
        public decimal SingleSpendLimit { get; set; }
        public decimal ATMDailyLimit { get; set; }

    }

    public class SpendingChannels
    {
        public SpendingChannel Stores { get; set; }
        public SpendingChannel ATMs { get; set; }
        public SpendingChannel Online { get; set; }
    }
    public class SpendingChannel
    {
        public bool IsBAH { get; set; }
        public bool IsGlobal { get; set; }
    }


    public class CurrencyControl
    {
        public string CurrencyCode { get; set; }
        public string ReasonCode { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class CountryControl
    {
        public string CountryCode { get; set; }
        public string ReasonCode { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }
    public class SpendingControls
    {
        public SpendingChannels SpendingChannels { get; set; }
        public SpendingLimit SpendingLimit { get; set; }
        public List<CurrencyControl> CurrencyControls { get; set; }
        public List<CountryControl> CountryControls { get; set; }
    }

    public class childParentCPR
    {
        public string childCPR { get; set; }
        public string parentCPR { get; set; }
    }

    public class WalletAccountType
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public int SortCode { get; set; }
        public bool Active { get; set; }
        public bool MultiCurrency { get; set; }
    }

    public class WalletCurrency
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Image { get; set; }
        public bool Active { get; set; }
        public bool IsDefault { get; set; }
    }

    public class Wallet
    {
        public string Currency { get; set; }
        public string Icon { get; set; }
        public decimal AvailableBalance { get; set; }
        public decimal HoldAmount { get; set; }
        public string Title { get; set; }
        public int NoOfDecimals { get; set; }
        public bool IsHome { get; set; }
    }

    public class WalletTransferResult
    {
        public bool Success { get; set; }
        public string ErrorCode { get; set; }
        public decimal SourceAmount { get; set; }
        public string SourceAmountFormat { get; set; }
        public decimal DestinationAmount { get; set; }
        public string DestinationAmountFormat { get; set; }
        public decimal TransferFee { get; set; }
        public decimal ExchangeRate { get; set; }
        public string ExchangeRateFormat { get; set; }
        public string FeeTransactionCode { get; set; }
        public decimal MarkupAmount { get; set; }
        public string TransferCurrency { get; set; }
        public decimal TransferAmount { get; set; }

    }


    public class PersonalData
    {
        public string ReturnStatus;
        public string ResponseCode;
        public string ResponseStatus;
        public string Name;
        public string BirthDate;
        public string Address;
        public string City;
        public string Country;
        public string Nationality;
        public string EmailAddress;
        public string MobilePhone;
        public string HomePhone;
        public string MaritalStatus;
        public string Gender;
        public string Corporate;
        public string Fax;
        public string Phone1;
        public string Phone2;
    }

    public class VerifyUser
    {
        public string Response;
        public string ReturnStatus;
        public string ResponseCode;
        public string ResponseStatus;
        public string SessionID;
        public string OTP;
    }

    public class SMSData
    {
        public DateTime DateCreate;
        public DateTime DateModify;
        public string EntityCode;
        public string EntityId;
        public string EntityPhone1;
        public string EntityPhone2;
        public string PrivateData1;
        public string PrivateData10;
        public string PrivateData2;
        public string PrivateData3;
        public string PrivateData4;
        public string PrivateData5;
        public string PrivateData6;
        public string PrivateData7;
        public string PrivateData8;
        public string PrivateData9;
        public DateTime ProcessingDate;
        public string ProcessingFlag;
        public string SMSCode;
        public string SMSWording;
        public string UserCreate;
        public string UserModify;
    }

    public class AddWalletRequest
    {
        [Required]
        public int id { get; set; }
        [Required]
        public int type { get; set; }
        [Required]
        public string name { get; set; }
    }

    public class AddWalletCardRequest : AddWalletRequest
    {
        [Required]
        public string brand { get; set; }
    }

    public class AddGoalRequest
    {
        [Required]
        public string Name { get; set; }
        [Required]
        public decimal Amount { get; set; }
        [Required]
        public DateTime Deadline { get; set; }
        [Required]
        public DateTime StartDate { get; set; }
        [Required]
        public decimal ChippingSavings { get; set; }
        [Required]
        public int RetentionFrequency { get; set; }
        [Required]
        public int currencyId { get; set; }
        [Required]
        public string sourceCAID { get; set; }
        [Required]
        public IFormFile picture { get; set; }
    }

    public class EditGoalRequest : AddGoalRequest
    {
        [Required]
        public string CAID { get; set; }
    }

    public class TransferGoalRequest
    {
        [Required]
        public string fromCAID { get; set; }
        [Required]
        public string toCAID { get; set; }
        [Required]
        public decimal amount { get; set; }
    }

    public class WalletTransferRequest
    {
        [Required]
        public int fromCAID { get; set; }
        [Required]
        public int toCAID { get; set; }
        [Required]
        public decimal amount { get; set; }
    }

    public class WalletStatementRequest
    {
        public string type { get; set; }
        public string card { get; set; }
        public string date { get; set; } = "0";
        public string suppCards { get; set; }
        public bool onlySupp  { get; set; } = false;
        public string page { get; set; }
        public bool IsWalletStatement { get; set; } = false;
        public string sCAID { get; set; }
        public string StmtType { get; set; }
    }

    public class WalletStatementBalanceResponse
    {
        public string HashedCAID { get; set; }
        public string Balance { get; set; }
    }

    public class WalletServiceRequest
    {
        [Required]
        public string sId { get; set; }
        [Required]
        public string type { get; set; }
        public string sCard { get; set; } = "-1";
        [Required]
        public string sCAID { get; set; }
        public string sRequestId { get; set; } = "";
        public bool isMerchantWallet { get; set; } = false;
        public bool IsLogRequest { get; set; } = false;
    }

    public class WalletServiceResponse
    {
        public string Title { get; set; }
        public List<RequestFormField> formFields { get; set; }
        public string FormRouteName { get; set; }
        public string FormRouteValues { get; set; }
        public string SelectedCardId { get; set; }
        public int FormId { get; set; }
        public string CAID { get; set; }        
        public string RequestID { get; set; }        
        public string CAIDHash { get; set; }
        public Dictionary<int,string> FormValues { get; set; }
    }
}
