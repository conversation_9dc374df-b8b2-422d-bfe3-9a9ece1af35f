﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Accept" xml:space="preserve">
    <value>قبول</value>
  </data>
  <data name="Agree" xml:space="preserve">
    <value>أوافق</value>
  </data>
  <data name="AnErrorOccured" xml:space="preserve">
    <value>An error occured.</value>
  </data>
  <data name="AnotherDeviceLogged" xml:space="preserve">
    <value>حسابك مسجل الدخول حاليًا إلى جهاز آخر.</value>
  </data>
  <data name="AppName" xml:space="preserve">
    <value>B2C Wolke Wallet</value>
  </data>
  <data name="AtLeast" xml:space="preserve">
    <value>على الأقل</value>
  </data>
  <data name="AtMost" xml:space="preserve">
    <value>على الأكثر</value>
  </data>
  <data name="AvailableBalance" xml:space="preserve">
    <value>الرصيد المتوفر</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>رجوع</value>
  </data>
  <data name="BackOnline" xml:space="preserve">
    <value>تم الإتصال بالإنترنت. مرحباً بك!</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>تغيير</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>خروج</value>
  </data>
  <data name="Col_ChooseListCategoryTbl_CLCatNameE" xml:space="preserve">
    <value>CLCatNameA</value>
  </data>
  <data name="Col_ChooseListItemTbl_CLNameE" xml:space="preserve">
    <value>CLNameA</value>
  </data>
  <data name="Col_CurrencyFile_SwiftCode" xml:space="preserve">
    <value>SwiftArb</value>
  </data>
  <data name="Col_ObjectsTbl_EngName" xml:space="preserve">
    <value>ArbName</value>
  </data>
  <data name="Col_ProductServiceTbl_ProductNameE" xml:space="preserve">
    <value>ProductNameA</value>
  </data>
  <data name="Col_Wallet_RelationsTbl_Relation" xml:space="preserve">
    <value>RelationA</value>
  </data>
  <data name="Col_Wallet_RetentionTbl_RetentionNameE" xml:space="preserve">
    <value>RetentionNameA</value>
  </data>
  <data name="Col_Wallet_TransactionTypeTbl_TypeNameE" xml:space="preserve">
    <value>TypeNameA</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>تأكيد العملية</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>تعديل</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>حدث خطأ</value>
  </data>
  <data name="ErrorDataSubmitted" xml:space="preserve">
    <value>يوجد خطأ في البيانات ! الرجاء معاودة المحاولة في وقت لاحق.</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>حدث خطأ أثناء معالجة طلبك.</value>
  </data>
  <data name="Exactly" xml:space="preserve">
    <value>تماما</value>
  </data>
  <data name="IncorrectFingerprintHash" xml:space="preserve">
    <value>البصمة غير مطابقة.</value>
  </data>
  <data name="IncorrectLoginDetails" xml:space="preserve">
    <value>البيانات المدخلة غير صحيحة.</value>
  </data>
  <data name="IncorrectLoginFormDetails" xml:space="preserve">
    <value>البيانات المدخلة غير صحيحة.</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>للعلم</value>
  </data>
  <data name="InternalError" xml:space="preserve">
    <value>حدث خطأ داخلي. الرجاء معاودة المحاولة في وقت لاحق.</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>جار التحميل</value>
  </data>
  <data name="LoginLocked" xml:space="preserve">
    <value>تم حظر تسجيل الدخول ،يرجى الإتصال بالدعم الفني</value>
  </data>
  <data name="More" xml:space="preserve">
    <value>المزيد</value>
  </data>
  <data name="MsgNotEligible" xml:space="preserve">
    <value>لا توجد أي من بطاقاتك مؤهلة لهذه الخدمة</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>لا</value>
  </data>
  <data name="NoDataFound" xml:space="preserve">
    <value>لا توجد بيانات</value>
  </data>
  <data name="NoInternetConnection" xml:space="preserve">
    <value>لم يتم اكتشاف اتصال بالإنترنت</value>
  </data>
  <data name="OffersDesc" xml:space="preserve">
    <value>للمزيد من الاستفسارات ، يرجى الاتصال بنا. نحن في خدمتكم</value>
  </data>
  <data name="Open" xml:space="preserve">
    <value>مفتوح</value>
  </data>
  <data name="ParametersError" xml:space="preserve">
    <value>يرجى تقديم جميع المعلمات المطلوبة.</value>
  </data>
  <data name="ProceedButton" xml:space="preserve">
    <value>متابعة</value>
  </data>
  <data name="Reject" xml:space="preserve">
    <value>رفض</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>إلزامي</value>
  </data>
  <data name="SaveChanges" xml:space="preserve">
    <value>حفظ التغييرات</value>
  </data>
  <data name="Saving" xml:space="preserve">
    <value>جاري الحفظ...</value>
  </data>
  <data name="SelectCaption" xml:space="preserve">
    <value>اختر من فضلك</value>
  </data>
  <data name="ServicesTitle" xml:space="preserve">
    <value>الخدمات</value>
  </data>
  <data name="SignOutConfirm" xml:space="preserve">
    <value>هل أنت متأكد أنك تود تسجيل الخروج؟</value>
  </data>
  <data name="SubmitButton" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="SubmitButtonLoading" xml:space="preserve">
    <value>جاري الحفظ</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>تم بنجاح</value>
  </data>
  <data name="TitleNotEligible" xml:space="preserve">
    <value>الخدمة غير متاحة</value>
  </data>
  <data name="UnexpectedError" xml:space="preserve">
    <value>لقد حدث خطأ غير متوقع. الرجاء معاودة المحاولة في وقت لاحق</value>
  </data>
  <data name="UploaderFieldLimitDesc" xml:space="preserve">
    <value>The file size must be less than 2 MB.</value>
  </data>
  <data name="ValidationRuleCPRLong" xml:space="preserve">
    <value>يجب أن يكون طول الحقل 9 تماما</value>
  </data>
  <data name="ValidationRuleDecimalPlaces" xml:space="preserve">
    <value>يجب أن يتضمن هذا الحقل ثلاثة منازل عشرية.</value>
    <comment>{0}</comment>
  </data>
  <data name="ValidationRuleEmail" xml:space="preserve">
    <value>الرجاء إدخال عنوان بريد إلكتروني صحيح.</value>
    <comment>{0}</comment>
  </data>
  <data name="ValidationRuleExactlyLength" xml:space="preserve">
    <value>يجب أن يكون طول الحقل {2} تماما</value>
    <comment>fieldTitle {0} | length {2}</comment>
  </data>
  <data name="ValidationRuleLength" xml:space="preserve">
    <value>يجب أن يكون هذا الحقل {1} {2}.</value>
    <comment>fieldTitle {0} | term {1} | length {2}</comment>
  </data>
  <data name="ValidationRuleNumber" xml:space="preserve">
    <value>يجب أن يتكون هذا الحقل من أرقام فقط.</value>
    <comment>{0}</comment>
  </data>
  <data name="ValidationRuleOnlyText" xml:space="preserve">
    <value>يجب أن يكون الحقل من دون أرقام </value>
  </data>
  <data name="ValidationRulePhoneNumber" xml:space="preserve">
    <value>الرجاء إدخال رقم هاتف صحيح.</value>
    <comment>{0}</comment>
  </data>
  <data name="ValidationRuleRange" xml:space="preserve">
    <value>هذا الحقل يجب أن يكون بين {1} و {2}.</value>
    <comment>{0} {1}</comment>
  </data>
  <data name="ValidationRuleRequired" xml:space="preserve">
    <value>هذا الحقل إلزامي.</value>
    <comment>{0}</comment>
  </data>
  <data name="ValidationRuleSpecialCharacter" xml:space="preserve">
    <value>يجب أن يكون الحقل خال من الحروف المميزة</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>نعم</value>
  </data>
</root>