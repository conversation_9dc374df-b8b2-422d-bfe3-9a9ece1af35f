﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddAddress" xml:space="preserve">
    <value>Add an Address</value>
  </data>
  <data name="AddNewAccount" xml:space="preserve">
    <value>Add New Account</value>
  </data>
  <data name="Addresses" xml:space="preserve">
    <value>Addresses</value>
  </data>
  <data name="AddressName" xml:space="preserve">
    <value>Address Name</value>
  </data>
  <data name="AlreadyRegistered" xml:space="preserve">
    <value>Already registered?</value>
  </data>
  <data name="and" xml:space="preserve">
    <value>and</value>
  </data>
  <data name="AndTheGoogle" xml:space="preserve">
    <value>and the Google</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="ApplyNow" xml:space="preserve">
    <value>Register now</value>
  </data>
  <data name="AvailableSlots" xml:space="preserve">
    <value>Available Slots</value>
  </data>
  <data name="BiometricAuthentication" xml:space="preserve">
    <value>Biometric Authentication</value>
  </data>
  <data name="BiometricAuthenticationMsg" xml:space="preserve">
    <value>Please log in with your credentials in order to activate the biometric authentication.</value>
  </data>
  <data name="Block" xml:space="preserve">
    <value>Block</value>
  </data>
  <data name="BlockNumber" xml:space="preserve">
    <value>Block Number</value>
  </data>
  <data name="Box" xml:space="preserve">
    <value>Box</value>
  </data>
  <data name="BoxNumber" xml:space="preserve">
    <value>Box Number</value>
  </data>
  <data name="Building" xml:space="preserve">
    <value>Building</value>
  </data>
  <data name="BuildingNameNumber" xml:space="preserve">
    <value>Building Name/Number</value>
  </data>
  <data name="CardNumber" xml:space="preserve">
    <value>Card Number</value>
  </data>
  <data name="Cart" xml:space="preserve">
    <value>Cart</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>Change</value>
  </data>
  <data name="ChangePIN" xml:space="preserve">
    <value>Change PIN</value>
  </data>
  <data name="ChangePIN_ConfirmPIN" xml:space="preserve">
    <value>Confirm PIN</value>
  </data>
  <data name="ChangePIN_CurrentPIN" xml:space="preserve">
    <value>Current PIN</value>
  </data>
  <data name="ChangePIN_NewPIN" xml:space="preserve">
    <value>New PIN</value>
  </data>
  <data name="Checkout" xml:space="preserve">
    <value>Checkout</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="CodeEnter" xml:space="preserve">
    <value>Please enter the 6-digit OTP sent to &lt;br&gt;your mobile number {0}.</value>
  </data>
  <data name="CodeFailedHint" xml:space="preserve">
    <value>We couldn't verify your mobile number! Please enter the 6-digit OTP sent to &lt;br&gt;your mobile number {0}.</value>
  </data>
  <data name="CodeMobileHint" xml:space="preserve">
    <value>wait while we verify your mobile number</value>
  </data>
  <data name="CompleteOnboardingPageTitle" xml:space="preserve">
    <value>Complete Onboarding</value>
  </data>
  <data name="ConfirmNewPassword" xml:space="preserve">
    <value>Confirm New Password</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="ConfirmYourPassword" xml:space="preserve">
    <value>Confirm your Password</value>
  </data>
  <data name="ContinueasaGuest" xml:space="preserve">
    <value>Continue as a Guest</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="CreateAccountApply" xml:space="preserve">
    <value>apply.</value>
  </data>
  <data name="CreateAccountPageTitle" xml:space="preserve">
    <value>Create Account</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="data" xml:space="preserve">
    <value>data</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="DeliveryChargeExclVAT" xml:space="preserve">
    <value>Delivery Charge Excl. VAT</value>
  </data>
  <data name="DeliveryChargeVAT" xml:space="preserve">
    <value>Delivery Charge VAT</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="DeliveryMethod" xml:space="preserve">
    <value>Delivery Method</value>
  </data>
  <data name="EditAddress" xml:space="preserve">
    <value>Edit Address</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="EmailAddress" xml:space="preserve">
    <value>Email Address</value>
  </data>
  <data name="EmbossingName" xml:space="preserve">
    <value>Name on your Card</value>
  </data>
  <data name="Enter6digitemailverificationcode" xml:space="preserve">
    <value>Enter 6 digit email verification code</value>
  </data>
  <data name="EnterEmailCode" xml:space="preserve">
    <value>Enter Email Code</value>
  </data>
  <data name="EnterNewPassword" xml:space="preserve">
    <value>Enter a New Password</value>
  </data>
  <data name="EnterSMSCode" xml:space="preserve">
    <value>Enter SMS Code</value>
  </data>
  <data name="EnterSMSVerificationCode" xml:space="preserve">
    <value>Enter 6 digit sms verification code</value>
  </data>
  <data name="ErrorPrivacySettings" xml:space="preserve">
    <value>Failed to update some of your privacy settings! Please try again later.</value>
  </data>
  <data name="ExistingUser" xml:space="preserve">
    <value>Already have an account?</value>
  </data>
  <data name="Favourite" xml:space="preserve">
    <value>Favourite</value>
  </data>
  <data name="Filltheformtologin" xml:space="preserve">
    <value>Fill the form to log in</value>
  </data>
  <data name="FillTheFormToResetYourPassword" xml:space="preserve">
    <value>Fill the form to reset your password</value>
  </data>
  <data name="FillTheFormToSignUp" xml:space="preserve">
    <value>Fill the form to sign up</value>
  </data>
  <data name="FingerprintUnlock" xml:space="preserve">
    <value>Enable Biometric Authentication</value>
  </data>
  <data name="Flat" xml:space="preserve">
    <value>Flat</value>
  </data>
  <data name="FlatNumber" xml:space="preserve">
    <value>Flat Number</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>Forgot Password</value>
  </data>
  <data name="ForgotPIN" xml:space="preserve">
    <value>Forgot PIN?</value>
  </data>
  <data name="GranViewModeltalExcl.VAT" xml:space="preserve">
    <value>Grand Total Excl. VAT</value>
  </data>
  <data name="GranViewModeltalIncl.VAT" xml:space="preserve">
    <value>Grand Total Incl. VAT</value>
  </data>
  <data name="Guest" xml:space="preserve">
    <value>Guest</value>
  </data>
  <data name="HintResetPin" xml:space="preserve">
    <value>Please enter a PIN to reset access to your account</value>
  </data>
  <data name="InvalidVerificationCodeError" xml:space="preserve">
    <value>Invalid verification code!</value>
  </data>
  <data name="KYCAgreeTerms" xml:space="preserve">
    <value>I agree to the terms and conditions</value>
  </data>
  <data name="KYCOTPSMSMessage" xml:space="preserve">
    <value>Your 6-digit verification code is: {0}. PLEASE DO NOT SHARE THIS WITH ANYONE.</value>
  </data>
  <data name="KYCResultStatusDetails" xml:space="preserve">
    <value>We will update you regarding the status of your application.</value>
  </data>
  <data name="KYCResultStatusTitle" xml:space="preserve">
    <value>Your request is under review.</value>
  </data>
  <data name="KYCRetailSectionEmployment" xml:space="preserve">
    <value>Employment Details</value>
  </data>
  <data name="KYCRetailSectionPersonal" xml:space="preserve">
    <value>Personal Details</value>
  </data>
  <data name="KYCRetailSectionProduct" xml:space="preserve">
    <value>Product Details</value>
  </data>
  <data name="KYCStep1Hint" xml:space="preserve">
    <value>Please start by enterning your CPR number in the textbox below:</value>
  </data>
  <data name="KYCStep2Hint" xml:space="preserve">
    <value>Please review and complete the sections below</value>
  </data>
  <data name="KYCStep5Hint" xml:space="preserve">
    <value>Please enter your mobile number in the textbox below:</value>
  </data>
  <data name="KYCStepPasswordHint" xml:space="preserve">
    <value>Please choose a PIN to complete the registration process</value>
  </data>
  <data name="KYCSubmitResultDetails" xml:space="preserve">
    <value>We will update you regarding the status of your application.</value>
  </data>
  <data name="KYCSubmitResultTitle" xml:space="preserve">
    <value>Your request has been submitted successfully and is under review.</value>
  </data>
  <data name="KYCUploadMissingDocuments" xml:space="preserve">
    <value>Please submit the below documents in order to complete your onboarding process:</value>
  </data>
  <data name="KYCVerifyFormDetails" xml:space="preserve">
    <value>Please confirm that your details below are correct before proceeding:</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="LanguageSettings" xml:space="preserve">
    <value>Language Settings</value>
  </data>
  <data name="Last4Digits" xml:space="preserve">
    <value>Card Last 4 Digits</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="LoggingIn" xml:space="preserve">
    <value>Logging in</value>
  </data>
  <data name="LogIn" xml:space="preserve">
    <value>Log In</value>
  </data>
  <data name="LoginTitle" xml:space="preserve">
    <value>Account Login</value>
  </data>
  <data name="MobileAlreadyRegisteredError" xml:space="preserve">
    <value>The provided mobile number is already registered!</value>
  </data>
  <data name="MobileNumber" xml:space="preserve">
    <value>Mobile Number</value>
  </data>
  <data name="MobileNumberNoMatch" xml:space="preserve">
    <value>The provided mobile number doesn't match with our records. Please contact us to update it.</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="NewAddress" xml:space="preserve">
    <value>New Address</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="NewUser" xml:space="preserve">
    <value>New user?</value>
  </data>
  <data name="Newuser?Registernow" xml:space="preserve">
    <value>New user? Register now</value>
  </data>
  <data name="NonMessage" xml:space="preserve">
    <value>There are no</value>
  </data>
  <data name="Notifications" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="OTPExpired" xml:space="preserve">
    <value>Your verification code has expired.</value>
  </data>
  <data name="OTPInvalid" xml:space="preserve">
    <value>Incorrect verification code.</value>
  </data>
  <data name="OTPMaxAttemptsReached" xml:space="preserve">
    <value>You have exceeded the maximum allowed number of OTP tries. Your account will be locked for 24 hours.</value>
  </data>
  <data name="OTPMaxAttemptsReachedSMSMessage" xml:space="preserve">
    <value>Dear Customer, You have exceeded the maximum allowed number of OTP tries. Your account will be locked for 24 hours.</value>
  </data>
  <data name="OTPMaxResendAttemptsReached" xml:space="preserve">
    <value>You have exceeded the maximum allowed number of re-sends.</value>
  </data>
  <data name="OTPModalDescription" xml:space="preserve">
    <value>Please enter the 6-digit OTP sent to your&lt;br&gt;registered mobile number {0}.</value>
  </data>
  <data name="OTPModalResend" xml:space="preserve">
    <value>Didn't receive OTP? &lt;strong&gt;&lt;u&gt;Resend&lt;/u&gt;&lt;/strong&gt;&lt;span class='resend-timer'&gt; in {0} secs&lt;/span&gt;</value>
  </data>
  <data name="OTPModalTitle" xml:space="preserve">
    <value>Enter OTP</value>
  </data>
  <data name="OTPPurposeFundTransfer" xml:space="preserve">
    <value>Fund Transfer</value>
  </data>
  <data name="OTPPurposeRemittance" xml:space="preserve">
    <value>Intl Remittance</value>
  </data>
  <data name="OTPSendError" xml:space="preserve">
    <value>Failed to send your verifcation code.</value>
  </data>
  <data name="OTPSendSuccess" xml:space="preserve">
    <value>Your verification code has been sent successfully.</value>
  </data>
  <data name="OTPSMSMessage" xml:space="preserve">
    <value>Dear Customer, Use OTP {0} on your app for {1} for {2}. OTP is valid for 5 mins. PLEASE DO NOT SHARE THIS WITH ANYONE.</value>
  </data>
  <data name="OTPValid" xml:space="preserve">
    <value>Correct verification code.</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>Payment Method</value>
  </data>
  <data name="PaymentSummary" xml:space="preserve">
    <value>Payment Summary</value>
  </data>
  <data name="pending" xml:space="preserve">
    <value>pending {0}</value>
    <comment>{0}</comment>
  </data>
  <data name="PersonalID" xml:space="preserve">
    <value>Personal ID</value>
  </data>
  <data name="PersonalInformation" xml:space="preserve">
    <value>Personal Information</value>
  </data>
  <data name="PersonalInformation_Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="PersonalInformation_CPR" xml:space="preserve">
    <value>CPR</value>
  </data>
  <data name="PersonalInformation_DOB" xml:space="preserve">
    <value>Date of Birth</value>
  </data>
  <data name="PersonalInformation_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="PersonalInformation_MobileNumber" xml:space="preserve">
    <value>Mobile Number</value>
  </data>
  <data name="PersonalInformation_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Phone Number</value>
  </data>
  <data name="PIN" xml:space="preserve">
    <value>PIN</value>
  </data>
  <data name="PinChangeFail" xml:space="preserve">
    <value>Your PIN could not be changed, please try again.</value>
  </data>
  <data name="PinChangeSuccess" xml:space="preserve">
    <value>Your PIN has been successfully changed</value>
  </data>
  <data name="PinExistingError" xml:space="preserve">
    <value>You cannot use your existing PIN.</value>
  </data>
  <data name="PinInvalidError" xml:space="preserve">
    <value>You have entered an invalid current PIN.</value>
  </data>
  <data name="PinSMSFail" xml:space="preserve">
    <value>Your PIN could not be reset, please try again.</value>
  </data>
  <data name="PinSMSSuccess" xml:space="preserve">
    <value>Your PIN has been successfully reset.</value>
  </data>
  <data name="PinUpdatedSuccess" xml:space="preserve">
    <value>Your PIN has been updated successfully. Please use your new PIN to login.</value>
  </data>
  <data name="Please" xml:space="preserve">
    <value>Please </value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="PrivacySettings" xml:space="preserve">
    <value>Privacy Settings</value>
  </data>
  <data name="Proceed" xml:space="preserve">
    <value>Proceed</value>
  </data>
  <data name="ProceeViewModelCheckout" xml:space="preserve">
    <value>Proceed to Checkout</value>
  </data>
  <data name="ProceeViewModelCheckoutasaGuest" xml:space="preserve">
    <value>Proceed to Checkout as a Guest</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="ProfileChangeSuccess" xml:space="preserve">
    <value>Your profile data has been updated successfully</value>
  </data>
  <data name="ProfileDataError" xml:space="preserve">
    <value>Failed to retrieve your profile details. Please try again later.</value>
  </data>
  <data name="ProfileNoChangeInfo" xml:space="preserve">
    <value>You have not made any changes to your profile</value>
  </data>
  <data name="ProfileSaveButton" xml:space="preserve">
    <value>Save Changes</value>
  </data>
  <data name="ProfileSaveButtonLoading" xml:space="preserve">
    <value>Saving</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="RegisterNow" xml:space="preserve">
    <value>Register now</value>
  </data>
  <data name="RemeberMe" xml:space="preserve">
    <value>Remember Me?</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="ResetPIN" xml:space="preserve">
    <value>Reset PIN</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>Retry</value>
  </data>
  <data name="Road" xml:space="preserve">
    <value>Road</value>
  </data>
  <data name="RoadNumber" xml:space="preserve">
    <value>Road Number</value>
  </data>
  <data name="Schedule" xml:space="preserve">
    <value>Schedule</value>
  </data>
  <data name="SearchTerm" xml:space="preserve">
    <value>Search Term</value>
  </data>
  <data name="SelectCity" xml:space="preserve">
    <value>Select a city</value>
  </data>
  <data name="SelectCountry" xml:space="preserve">
    <value>Select a country</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value />
  </data>
  <data name="Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="SubTotal" xml:space="preserve">
    <value>Sub Total</value>
  </data>
  <data name="SuccessPrivacySettings" xml:space="preserve">
    <value>Your privacy settings have been successfully updated!</value>
  </data>
  <data name="TermsAndConditions" xml:space="preserve">
    <value>Terms and Conditions</value>
  </data>
  <data name="TermsOfService" xml:space="preserve">
    <value>Terms of Service</value>
  </data>
  <data name="ThereNoItemsInCart" xml:space="preserve">
    <value>There are no items in your cart!</value>
  </data>
  <data name="ThisSiteIsProtectedBy" xml:space="preserve">
    <value>This site is protected by</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>CPR</value>
  </data>
  <data name="VATAmount" xml:space="preserve">
    <value>VAT Amount</value>
  </data>
  <data name="Verification" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="Verify" xml:space="preserve">
    <value>Verify</value>
  </data>
  <data name="VerifyButton" xml:space="preserve">
    <value>Verify</value>
  </data>
  <data name="Verifying" xml:space="preserve">
    <value>Verifying</value>
  </data>
  <data name="YourEmailAddress" xml:space="preserve">
    <value>Your Email Address</value>
  </data>
  <data name="YourFirstandLastName" xml:space="preserve">
    <value>Your First and Last Name</value>
  </data>
  <data name="YourPassword" xml:space="preserve">
    <value>Your Password</value>
  </data>
  <data name="YourPersonalID" xml:space="preserve">
    <value>Your Personal ID</value>
  </data>
  <data name="YourPhoneNumber" xml:space="preserve">
    <value>Your Phone Number</value>
  </data>
</root>