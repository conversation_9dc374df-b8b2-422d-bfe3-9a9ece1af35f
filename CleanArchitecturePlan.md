# Optimum Wallet Core API - Clean Architecture Enhancement Plan

## Current Architecture Assessment

The current Optimum Wallet Core API follows a traditional layered architecture with some aspects of separation of concerns, but lacks the full benefits of clean architecture. The system is divided into two main projects:

1. **Optimum.Wallet.Api**: Contains controllers, services, middleware, and configuration
2. **Optimum.Wallet.Core**: Contains DTOs, database entities, and settings

### Current Issues Identified

- **Mixed Responsibilities**: Business logic is mixed with infrastructure concerns
- **Direct Database Dependencies**: Controllers and services directly depend on database entities
- **Tight Coupling**: High coupling between layers makes testing difficult
- **Dependency Direction**: Dependencies don't always point inward as per clean architecture principles
- **Lack of Domain Model**: No clear separation between domain entities and database entities
- **Scattered Business Rules**: Business rules are scattered across services and controllers

## Target Clean Architecture

We propose refactoring the project to follow clean architecture principles with the following layers:

### 1. Domain Layer (Optimum.Wallet.Domain)
- Core business entities and logic
- Domain events and exceptions
- Value objects
- Business rules and validation
- No dependencies on other layers or external frameworks

### 2. Application Layer (Optimum.Wallet.Application)
- Service interfaces and implementations
- DTOs (Data Transfer Objects)
- Interfaces for infrastructure services
- Validation logic
- Only depends on Domain layer

### 3. Infrastructure Layer (Optimum.Wallet.Infrastructure)
- Dapper data access and SQL queries
- Repository implementations
- External service integrations (payment gateways, etc.)
- Logging, caching implementations
- Email, SMS, and notification services
- Depends on Domain and Application layers

### 4. Presentation Layer (Optimum.Wallet.Api)
- API controllers
- Middleware components
- API models and mappings
- Authentication and authorization
- Depends on Application layer only

## Implementation Strategy

### Phase 1: Project Structure and Domain Layer
1. Create new project structure
2. Define core domain entities and value objects
3. **Physically move** business entities from Optimum.Wallet.Core to Domain project
4. Delete original files from Optimum.Wallet.Core after moving if needed.
5. Implement domain events and exceptions
6. Compile and fix any errors

### Phase 2: Application Layer ✅ **COMPLETED**
1. ✅ Define interfaces for repositories and services
2. ✅ **Physically move** all DTOs from Optimum.Wallet.Core to Application project use file copy and adjust namespace after that.
3. ✅ **Physically move** repository interfaces (ICardRepository, IWalletRepository, IFormRepository, etc.) from Optimum.Wallet.Core to Application project to avoid circular dependencies
4. ✅ **Physically move** all service implementations to Application project use file copy and adjust namespace after that.
5. ✅ **Physically move** mapping profiles from api project (Mapper/ModelToViewMapper.cs) to Application project (Mappings folder)
6. ✅ Delete original files from Optimum.Wallet.Core and Optimum.Wallet.Api after moving if needed.
7. ⏭️ Add validation using FluentValidation - not needed now.
8. ✅ Update all namespace references throughout the solution to point to new locations
9. ✅ Compile and fix any errors

#### **Phase 2 Status Summary:**
- **Major Achievement**: Successfully implemented Clean Architecture principles
- **Solution Builds Successfully**: ✅ All errors resolved, only minor warnings remain
- **Circular Dependencies Resolved**: ✅ Clean dependency flow established
- **WebServices Properly Located**: ✅ Moved from Application to Infrastructure layer
- **AutoMapper Configuration**: ✅ Centralized in Application layer
- **DTOs and Repository Interfaces**: ✅ Successfully moved to Application layer
- **Mapping Profiles**: ✅ Consolidated in Application layer
- **Namespace References**: ✅ All updated to new locations

#### **Phase 2 Complete - Ready for Phase 3**
All critical components have been properly relocated according to Clean Architecture principles. The solution builds successfully with proper dependency direction. Core project decomposition has been planned and documented in Phase 3 for execution.

### Phase 3: Infrastructure Layer & Core Decomposition 🔄 **PLANNED - DO NOT START EXECUTION YET**

### Phase 3: Infrastructure Layer & Core Decomposition 🔄 **PLANNED - DO NOT START EXECUTION YET**

**Part 1: Repository Infrastructure Migration**
1. ⏭️ Create repository implementations in Infrastructure project
2. ⏭️ **Physically move** repository implementations from API/Data/Repository to Infrastructure project
3. ⏭️ **Physically move** Dapper data access code to Infrastructure project  
4. ⏭️ Delete original files from Optimum.Wallet.Api after moving
5. ⏭️ Configure dependency injection to register Infrastructure repositories
6. ⏭️ Update API controllers to use Infrastructure services through Application interfaces

**Part 2: Core Project Decomposition**
Based on analysis of remaining files in Optimum.Wallet.Core:

**Files to move to Domain layer:**
- `Settings/SupportedCultures.cs` → `Domain/Entities/SupportedCultures.cs` (domain entity with [Key] attribute)

**Files to move to Application layer:**
- `Configurations/Models/JWTSettings.cs` → `Application/Configurations/JWTSettings.cs` (application-specific auth config)
- `Interfaces/IGenericRepository.cs` → `Application/Interfaces/Repositories/IGenericRepository.cs`
- `Interfaces/ITokenRequest.cs` → `Application/Interfaces/Repositories/ITokenRequest.cs`

**Files to move to API layer:**
- `Settings/RateLimitOptions.cs` → `API/Configurations/RateLimitOptions.cs` (API-specific infrastructure setting)
- `Extensions/AppUser.cs` → `API/Extensions/AppUser.cs` (authentication extension)
- `Extensions/LanguageRouteConstraint.cs` → `API/Extensions/LanguageRouteConstraint.cs` (routing concern)

**Files to move to Infrastructure layer:**
- `Extensions/JsonStringLocalizer.cs` → `Infrastructure/Extensions/JsonStringLocalizer.cs` (localization implementation)
- `Extensions/JsonStringLocalizerFactory.cs` → `Infrastructure/Extensions/JsonStringLocalizerFactory.cs`
- `Extensions/NotificationExtensions.cs` → `Infrastructure/Extensions/NotificationExtensions.cs`
- `Extensions/RouteDataRequestCultureProvider.cs` → `Infrastructure/Extensions/RouteDataRequestCultureProvider.cs`

**Files to decompose by concern (Constants.cs - 912 lines):**
- Business constants → `Domain/Constants/BusinessConstants.cs`
- API/UI constants → `API/Constants/ApiConstants.cs`  
- Infrastructure constants → `Infrastructure/Constants/InfrastructureConstants.cs`

**Steps for Part 2:**
7. ⏭️ Move SupportedCultures.cs to Domain/Entities/
8. ⏭️ Move JWTSettings.cs to Application/Configurations/
9. ⏭️ Move repository interfaces to Application/Interfaces/Repositories/
10. ⏭️ Move RateLimitOptions.cs to API/Configurations/
11. ⏭️ Move API-related extensions to API/Extensions/
12. ⏭️ Move Infrastructure-related extensions to Infrastructure/Extensions/
13. ⏭️ Analyze and split Constants.cs by concern into appropriate layers
14. ⏭️ Update all namespace references throughout the solution
15. ⏭️ Remove empty folders from Core project
16. ⏭️ Evaluate if Core project can be completely removed
17. ⏭️ Compile and fix any errors

### Phase 4: Presentation Layer Refactoring
1. Update controllers to reference services from Application layer
2. Remove any direct dependencies on Infrastructure or Domain layers
3. Implement API models and mappings
4. Configure authentication and authorization
5. Update middleware components
6. Ensure all references point to the new project structure
7. Compile and fix any errors

### Phase 5: Testing and Documentation
1. Add unit tests for domain and application layers
2. Add integration tests for infrastructure layer
3. Update API documentation
4. Update README and developer documentation

## Benefits of Clean Architecture

1. **Separation of Concerns**: Clear boundaries between different parts of the application
2. **Testability**: Easier to write unit tests with proper abstractions
3. **Maintainability**: Changes in one layer don't affect others
4. **Flexibility**: Easier to swap out infrastructure components
5. **Domain-Centric**: Focus on business rules rather than technical details
6. **Independence of Frameworks**: Core business logic doesn't depend on external frameworks

## Technical Recommendations

1. **Dependency Injection**: Use built-in ASP.NET Core DI container
2. **Controller-Service-Repository Pattern**: Simple and familiar architecture
3. **FluentValidation**: For input validation
4. **AutoMapper**: For object-to-object mapping
5. **Dapper**: For data access (continue with existing implementation)
6. **Repository Pattern**: For data access abstraction
7. **Unit of Work**: For transaction management

## Migration Path

To ensure a smooth transition without disrupting existing functionality:

1. Start with creating the new project structure
2. Implement core domain entities and interfaces
3. **Physically move files** from old projects to new projects (no duplicates)
4. Delete original files after moving to avoid code duplication
5. Update references across the solution to point to new locations
6. Use adapter pattern only when necessary for backward compatibility
7. Add comprehensive tests before and after migration
8. Perform incremental deployments

## Timeline Estimation

- **Phase 1**: 2-3 weeks
- **Phase 2**: 3-4 weeks
- **Phase 3**: 2-3 weeks
- **Phase 4**: 2-3 weeks
- **Phase 5**: 1-2 weeks

Total estimated time: 10-15 weeks depending on project complexity and team availability.

## Risk Assessment

1. **Regression Risks**: Mitigate with comprehensive test coverage
2. **Performance Impact**: Monitor and optimize as needed
3. **Learning Curve**: Provide training for the team on clean architecture principles
4. **Timeline Slippage**: Regular progress tracking and adjustments

## Conclusion

Implementing clean architecture for the Optimum Wallet Core API will significantly improve code quality, maintainability, and testability. While the refactoring requires substantial effort, the long-term benefits justify the investment, especially for a financial application where reliability and maintainability are crucial.
