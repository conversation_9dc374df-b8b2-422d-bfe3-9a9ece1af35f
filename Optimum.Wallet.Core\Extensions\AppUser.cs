﻿using System.Security.Claims;

namespace Optimum.Wallet.Core.Extensions
{
    public class AppUser : ClaimsPrincipal
    {
        public AppUser(ClaimsPrincipal principal) : base(principal)
        {

        }
        public string BankCode => this.FindFirst("BankCode")?.Value;
        public int ContactID => Convert.ToInt32(this.FindFirst("ContactID")?.Value);
        public int CTRID => Convert.ToInt32(this.FindFirst("CTRID")?.Value);
        public int CRID => Convert.ToInt32(this.FindFirst("CRID")?.Value);
        public int CAID => Convert.ToInt32(this.FindFirst("CAID")?.Value);
        public int CustomerID => Convert.ToInt32(this.FindFirst("CustomerID")?.Value);
        public string Email => this.FindFirst(ClaimTypes.Email)?.Value;
        public string Username => this.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        public string Name => this.FindFirst(ClaimTypes.Name)?.Value;
        public string Mobile => this.FindFirst(ClaimTypes.MobilePhone)?.Value;
        public string HomePhone => this.FindFirst(ClaimTypes.HomePhone)?.Value;
        public bool IsChild => Convert.ToBoolean(this.FindFirst("IsChild")?.Value);
        public string ContactPhoto => this.FindFirst("ContactPhoto")?.Value;
        public bool IsMerchant => Convert.ToBoolean(this.FindFirst("IsMerchant")?.Value);
        public string BillAddress => this.FindFirst("BillAddress")?.Value;

    }
}