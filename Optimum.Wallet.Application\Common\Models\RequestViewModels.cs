using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;
using Optimum.Wallet.Domain.Entities;

namespace Optimum.Wallet.Application.Common.Models
{
    public class MerchantWithdrawRequest
{
    [Required]
    public string sCAID { get; set; }
    [Required]
    public decimal amount { get; set; }
}

public class MerchantPaymentRequest
{
    public int MerchantId { get; set; }
    public int MerchantCAID { get; set; }
    public string MerchantName { get; set; }
    public WalletAccount MerchantAccount { get; set; }
    public string BankName { get; set; }
    public string BankAccount { get; set; }
    public string BankSwiftCode { get; set; }
    public string BankIBAN { get; set; }
    public decimal Amount { get; set; }
}

public class MoneyTransferBeneficiary
{
    [Required]
    public int Id { get; set; }
    [Required]
    public string Name { get; set; }
    public string BankName { get; set; }
    public string BankAccount { get; set; }
    public string BankSwiftCode { get; set; }
    [Required]
    public string BankIBAN { get; set; }
}

public class SplitPaymentBill
{
    public int Id { get; set; }
    public string Title { get; set; }
    public decimal Amount { get; set; }
    public DateTime DateCreated { get; set; }
    public int CAID { get; set; }
    public int TotalRecipients { get; set; }
    public int TotalPaid { get; set; }
    public decimal TotalAmountPaid { get; set; }
    public string SwiftCode { get; set; }
    public int NoOfDecimals { get; set; }
}


public class SplitPaymentRequest
{
    [Required]
    public string BillName { get; set; }
    [Required]
    public decimal BillAmount { get; set; }
    public string BillNotes { get; set; }
    [Required]
    public List<SplitPaymentRecipient> BillRecipient { get; set; }
}

public class SplitPaymentDetails : SplitPaymentRequest
{
    public int Id { get; set; }
    public DateTime DateCreated { get; set; }
    public int CAID { get; set; }
    public string AccountName { get; set; }
    public string SwiftCode { get; set; }
    public int NoOfDecimals { get; set; }
}

public class SplitPaymentRecipient
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Mobile { get; set; }
    public decimal Amount { get; set; }
    public bool Paid { get; set; }
    public DateTime DatePaid { get; set; }
}

public class SplitPaymentCheckUserRequest
{
    public string toMobileNo { get; set; }
    public string toCPR { get; set; }
    public string type { get; set; } = "W";
    public string sCAID { get; set; } = "";
}

public class SplitPaymentCheckUserResponse
{
    public string Status { get; set; }
    public string Message { get; set; }
    public dynamic User { get; set; }
}

public class RequestViewModel
{
    public string Title { get; set; }
    public List<RequestFormField> Fields { get; set; }
}

public class Form
{
    public int ID { get; set; }
    public int TableId { get; set; }
    public string Title { get; set; }
    public List<RequestFormField> Fields { get; set; }
}

public class FormHeader
{
    public int ID { get; set; }
    public string Title { get; set; }
    public int DetailsId { get; set; }
}

public class RequestFormField
{
    public int ID { get; set; }
    public string ControlType { get; set; }
    public string DataType { get; set; }
    public string Title { get; set; }
    public string Value { get; set; }
    public bool Editable { get; set; }
    public bool Publish { get; set; }
    public string ValidationRules { get; set; }
    public string PresetValue { get; set; }
    public int CLPId { get; set; }
    public string CLIId { get; set; }
    public List<RequestDropDown> DropdownValues { get; set; }
    public List<CardDetailsViewModel> Cards { get; set; }
    public List<RequestFormFieldTab> Tabs { get; set; }
    public List<WalletAccount> Accounts { get; set; }
}

public class RequestFormFieldTab
{
    public int FieldId { get; set; }
    public int TabNo { get; set; }
    public int ControlSort { get; set; }

}

public class RequestFormFieldValidationRule
{
    public int Id { get; set; }
    public int FieldId { get; set; }
    public string Title { get; set; }
    public string Value { get; set; }
    public string Rule { get; set; }
}

public class RequestDropDown
{
    public int ID { get; set; }
    public string Title { get; set; }
    public int? ControlId { get; set; }
}
public class EmailValues
{
    public string FormName { get; set; }
    public string Category { get; set; }
    public string ItemName { get; set; }
    public int CategoryId { get; set; }
    public string ControlType { get; set; }
    public string DataType { get; set; }
}

public class FormField
{
    public int CLDtlId { get; set; }
    public int CLCatId { get; set; }
    public string CLIId { get; set; }
    public string CLCatNameE { get; set; }
    public string ItemName { get; set; }
    public int TableId { get; set; }
}

public class TicketRecipientData : CustTicktCatScheduleTbl
{
    public int CustDocID { get; set; }
    public int TicktStatsID { get; set; }
    public int TiktTypeID { get; set; }
    public string UserId { get; set; }
}

public class ChooseListPageCategoryTbl : ChooseListPageTbl
{
    public string DataType { get; set; }
}

public class ChooseListDetailsCategoryTbl : ChooseListDetailsTbl
{
    public string DataType { get; set; }
}

public class ProductServicePageTbl : ProductServiceTbl
{
    public string PreSetValue { get; set; }
}
public class CustomerServicesProductTbl : CustomerServicesTbl
{
    public string ProductNameE { get; set; }
}

public class ExchangeRate
{
    public int CcyCode { get; set; }
    public string SwiftCode { get; set; }
    public int NoOfDecimal { get; set; }
    public decimal Rate { get; set; }
    public decimal RateM { get; set; }
    public decimal BuyRate { get; set; }
    public decimal BuyRateM { get; set; }
    public decimal SellRate { get; set; }
    public decimal SellRateM { get; set; }
    public decimal SourceAmount { get; set; }
}

public class SpendingControlsTbl
{
    public int CAID; public string WeeklySpendingLimit; public string weeklyNumberTran; public string SingleTranLimit; public string DailyCashWithDrawLimit;
}
}
