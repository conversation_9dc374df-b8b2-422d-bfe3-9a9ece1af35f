using System;
using System.Collections.Generic;

namespace Optimum.Wallet.Domain.Entities
{
    public class CustomerFile
    {
        public CustomerFile()
        {
            ContactTbls = new HashSet<ContactTbl>();
        }
        public int RecID { get; set; }
        public string Crno { get; set; }
        public string BankCode { get; set; }
        public string BankName { get; set; }
        public string ArabicName { get; set; }
        public int? AccountNo { get; set; }
        public int? GoodsReceivedAct { get; set; }
        public int? AdvAccount { get; set; }
        public int? UnBilledAct { get; set; }
        public string CustShortName { get; set; }
        public string ArbShortName { get; set; }
        public string ContactPerson { get; set; }
        public int? IndustryNo { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string CityArb { get; set; }
        public string Address3 { get; set; }
        public string Street { get; set; }
        public string Expo { get; set; }
        public string Tel1 { get; set; }
        public string Tel2 { get; set; }
        public string Telex { get; set; }
        public string Fax { get; set; }
        public string SwiftNo { get; set; }
        public string Email { get; set; }
        public string WebSite { get; set; }
        public string Acofficer { get; set; }
        public string UserModified { get; set; }
        public DateTime? DtInput { get; set; }
        public bool? Cancel { get; set; }
        public string UseCan { get; set; }
        public DateTime? DtCan { get; set; }
        public string ProfitSeg { get; set; }
        public bool RecLocked { get; set; }
        public string LockedBy { get; set; }
        public int? CustomerType { get; set; }
        public double? CustomerLimit { get; set; }
        public double? CrLimtDays { get; set; }
        public string CustomerGid { get; set; }
        public string CommentsEng { get; set; }
        public string ZipCode { get; set; }
        public string PassportNo { get; set; }
        public DateTime? PassExpiry { get; set; }
        public string Cprno { get; set; }
        public DateTime? Cprexpiry { get; set; }
        public DateTime? Crexpiry { get; set; }
        public DateTime? FinYear { get; set; }
        public DateTime? BillingDate { get; set; }
        public DateTime? StateDate { get; set; }
        public decimal? DiscountCus { get; set; }
        public int? CustomerTypeId { get; set; }
        public string CompIdc { get; set; }
        public string BranchC { get; set; }
        public bool? DiscountCard { get; set; }
        public bool AutoDelivery { get; set; }
        public int? ActType { get; set; }
        public string Par1 { get; set; }
        public string Par2 { get; set; }
        public string Par3 { get; set; }
        public string Par4 { get; set; }
        public string Par5 { get; set; }
        public string Par6 { get; set; }
        public string Par7 { get; set; }
        public string Par8 { get; set; }
        public int AraccountNo { get; set; }
        public int ArgoodsReceivedAct { get; set; }
        public int AradvAccount { get; set; }
        public string Aracofficer { get; set; }
        public decimal? ArcustomerLimit { get; set; }
        public decimal? ArcrLimtDays { get; set; }
        public DateTime? ArbillingDate { get; set; }
        public DateTime? ArstateDate { get; set; }
        public decimal? ArdiscountCus { get; set; }
        public bool ArautoDelivery { get; set; }
        public bool Active { get; set; }
        public string Logo { get; set; }
        public string Password { get; set; }
        public int Customer { get; set; }
        public string Address4 { get; set; }
        public string Address5 { get; set; }
        public string Address6 { get; set; }
        public string Address7 { get; set; }
        public int? AddressFormatId { get; set; }
        public string CustomerCode { get; set; }
        public int? SortCode { get; set; }
        public int? Frequency { get; set; }
        public DateTime? ProcessDate { get; set; }
        public int? CustUploadId { get; set; }
        public double? CommissionPer { get; set; }
        public int? PayMethdId { get; set; }
        public int? AddressId { get; set; }
        public int? AreaId { get; set; }
        public int? CustActId { get; set; }
        public int? CityId { get; set; }
        public int? CountryId { get; set; }
        public bool? DefArcash { get; set; }
        public DateTime? DateModified { get; set; }
        public string Longitude { get; set; }
        public string Latitude { get; set; }
        public string Instagram { get; set; }
        public string Facebook { get; set; }
        public string Twitter { get; set; }
        public string Linkedin { get; set; }
        public DateTime? OpenDate { get; set; }
        public DateTime? CloseDate { get; set; }
        public DateTime? CrexpDate { get; set; }
        public DateTime? ArcrexpDate { get; set; }
        public short? InActive { get; set; }
        public bool Publish { get; set; }
        public string VatNo { get; set; }
        public DateTime? VatReportingDate { get; set; }
        public int? CustomerAr { get; set; }
        public int? CustomerAp { get; set; }
        public string LogoArabic { get; set; }
        public string CompDesc { get; set; }
        public string Ibanno { get; set; }
        public string ShopPhoto { get; set; }
        public string ShopVideo { get; set; }

        public virtual ICollection<ContactTbl> ContactTbls { get; set; }
    }
}
