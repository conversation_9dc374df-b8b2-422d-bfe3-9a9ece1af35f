﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ACNO" xml:space="preserve">
    <value>A/C NO</value>
  </data>
  <data name="AddingTheItem" xml:space="preserve">
    <value>Adding the item...</value>
  </data>
  <data name="AddReview" xml:space="preserve">
    <value>Add a review</value>
  </data>
  <data name="AdViewModelCart" xml:space="preserve">
    <value>Add to Cart</value>
  </data>
  <data name="AllShops" xml:space="preserve">
    <value>All Shops</value>
  </data>
  <data name="AllYourShops" xml:space="preserve">
    <value>All your shops!</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="AreYouSureYouWantToRemoveThisProduct" xml:space="preserve">
    <value>Are you sure you want to remove this product?</value>
  </data>
  <data name="BasketFullOf" xml:space="preserve">
    <value>A basket full of</value>
  </data>
  <data name="BestMatch" xml:space="preserve">
    <value>Best Match</value>
  </data>
  <data name="BiometricAuthentication" xml:space="preserve">
    <value>Biometric Authentication</value>
  </data>
  <data name="by" xml:space="preserve">
    <value>by</value>
  </data>
  <data name="CANCEL" xml:space="preserve">
    <value>CANCEL</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>Categories</value>
  </data>
  <data name="Checkout" xml:space="preserve">
    <value>Checkout</value>
  </data>
  <data name="CLEAR" xml:space="preserve">
    <value>CLEAR</value>
  </data>
  <data name="ClearSignature" xml:space="preserve">
    <value>Clear Signature</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="Confirmation" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="ConfirmDelivery" xml:space="preserve">
    <value>Confirm Delivery</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="CustomerDelivery" xml:space="preserve">
    <value>Customer Delivery</value>
  </data>
  <data name="DailyOffers" xml:space="preserve">
    <value>Daily Offers</value>
  </data>
  <data name="Dated" xml:space="preserve">
    <value>Dated</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Delivery" xml:space="preserve">
    <value>Delivery</value>
  </data>
  <data name="DeliveryAddress" xml:space="preserve">
    <value>Delivery Address</value>
  </data>
  <data name="DeliveryConfirmation" xml:space="preserve">
    <value>Delivery Confirmation</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="DESCRIPTION" xml:space="preserve">
    <value>DESCRIPTION</value>
  </data>
  <data name="DifferentSellerMessage" xml:space="preserve">
    <value>The shop you're trying to order from is a different seller, would you like to clear your cart and continue adding this item?</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>Distance</value>
  </data>
  <data name="Favourite" xml:space="preserve">
    <value>Favourite</value>
  </data>
  <data name="Favourites" xml:space="preserve">
    <value>Favourites</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>Filters</value>
  </data>
  <data name="Gems&amp;Goodies" xml:space="preserve">
    <value>Gems &amp; Goodies</value>
  </data>
  <data name="GranViewModeltal" xml:space="preserve">
    <value>Grand Total</value>
  </data>
  <data name="Guest" xml:space="preserve">
    <value>Guest</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Items</value>
  </data>
  <data name="Languages" xml:space="preserve">
    <value>Languages</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="LogisticOperations" xml:space="preserve">
    <value>Logistic Operations</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Log out</value>
  </data>
  <data name="MallatsProduct" xml:space="preserve">
    <value>Mallats Product</value>
  </data>
  <data name="Marketplace" xml:space="preserve">
    <value>Marketplace</value>
  </data>
  <data name="Menu" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="MerchantPickup" xml:space="preserve">
    <value>Merchant Pickup</value>
  </data>
  <data name="Min" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="MyFavourites" xml:space="preserve">
    <value>My Favourites</value>
  </data>
  <data name="MyOrder" xml:space="preserve">
    <value>My Order</value>
  </data>
  <data name="MyOrders" xml:space="preserve">
    <value>My Orders</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Net" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="Newest" xml:space="preserve">
    <value>Newest</value>
  </data>
  <data name="NoCategoriesWereFound" xml:space="preserve">
    <value>No categories were found!</value>
  </data>
  <data name="NoFavouritesWereAdded!" xml:space="preserve">
    <value>No favourites were added!</value>
  </data>
  <data name="NoFiltersWereFound" xml:space="preserve">
    <value>No filters were found!</value>
  </data>
  <data name="NoOrdersFound" xml:space="preserve">
    <value>No orders found!</value>
  </data>
  <data name="NoReviewsFound" xml:space="preserve">
    <value>No reviews found</value>
  </data>
  <data name="OnePlaceOneMarket" xml:space="preserve">
    <value>One place, one market.</value>
  </data>
  <data name="OrderDate" xml:space="preserve">
    <value>Order Date</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>Order Number</value>
  </data>
  <data name="OrderReceipt" xml:space="preserve">
    <value>Order Receipt</value>
  </data>
  <data name="OrderRequiresAction" xml:space="preserve">
    <value>Order requires action</value>
  </data>
  <data name="Others" xml:space="preserve">
    <value>Others</value>
  </data>
  <data name="OutOfStock" xml:space="preserve">
    <value>Out of Stock</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="PleaseSelectOneOfTheFollowingOptions" xml:space="preserve">
    <value>Please select one of the following options</value>
  </data>
  <data name="PleaseTakeActionBelow" xml:space="preserve">
    <value>Please take action below</value>
  </data>
  <data name="PriceHighToLow" xml:space="preserve">
    <value>Price-High to Low</value>
  </data>
  <data name="PriceLowToHigh" xml:space="preserve">
    <value>Price-Low to High</value>
  </data>
  <data name="PrintTime" xml:space="preserve">
    <value>Print Time</value>
  </data>
  <data name="ProductDescription" xml:space="preserve">
    <value>Product Description</value>
  </data>
  <data name="ProductSpecification" xml:space="preserve">
    <value>Product Specification</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="Qty" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="RATE" xml:space="preserve">
    <value>RATE</value>
  </data>
  <data name="Receipt" xml:space="preserve">
    <value>Receipt</value>
  </data>
  <data name="ReceiverName" xml:space="preserve">
    <value>Receiver's Name</value>
  </data>
  <data name="ReceiverSignature" xml:space="preserve">
    <value>Receiver's Signature</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="RemoveProduct" xml:space="preserve">
    <value>Remove Product</value>
  </data>
  <data name="Reorder" xml:space="preserve">
    <value>Re-order</value>
  </data>
  <data name="Reviews" xml:space="preserve">
    <value>Reviews</value>
  </data>
  <data name="Search..." xml:space="preserve">
    <value>Search...</value>
  </data>
  <data name="SelectAlternative" xml:space="preserve">
    <value>Select Alternative</value>
  </data>
  <data name="shareMessage" xml:space="preserve">
    <value>Have a look at this product I found in mallats.</value>
  </data>
  <data name="Shops" xml:space="preserve">
    <value>Shops</value>
  </data>
  <data name="SimilarProducts" xml:space="preserve">
    <value>Similar Products</value>
  </data>
  <data name="SoldBy" xml:space="preserve">
    <value>Sold by</value>
  </data>
  <data name="SomeItemsNotAvailable" xml:space="preserve">
    <value>Some items are not available, please choose out of the alternatives that have been provided.</value>
  </data>
  <data name="Sort" xml:space="preserve">
    <value>Sort</value>
  </data>
  <data name="SortBy" xml:space="preserve">
    <value>Sort By</value>
  </data>
  <data name="TakeAction" xml:space="preserve">
    <value>Take Action</value>
  </data>
  <data name="TOTAL" xml:space="preserve">
    <value>TOTAL</value>
  </data>
  <data name="TotalInclVAT&amp;Delivery" xml:space="preserve">
    <value>Total (Incl. VAT &amp; Delivery)</value>
  </data>
  <data name="TypeTheNameHere" xml:space="preserve">
    <value>Type the name here..</value>
  </data>
  <data name="VAT" xml:space="preserve">
    <value>VAT</value>
  </data>
  <data name="VATNO" xml:space="preserve">
    <value>VAT NO</value>
  </data>
  <data name="ViewAll" xml:space="preserve">
    <value>View All</value>
  </data>
  <data name="ViewAllShops" xml:space="preserve">
    <value>View All Shops</value>
  </data>
  <data name="ViewAlternative" xml:space="preserve">
    <value>View Alternative</value>
  </data>
  <data name="ViewMarketplace" xml:space="preserve">
    <value>View Marketplace</value>
  </data>
  <data name="ViewShops" xml:space="preserve">
    <value>View Shops</value>
  </data>
</root>