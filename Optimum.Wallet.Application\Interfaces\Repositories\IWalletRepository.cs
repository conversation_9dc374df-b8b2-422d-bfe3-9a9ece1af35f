using Optimum.Wallet.Application.Common.Models;

namespace Optimum.Wallet.Application.Interfaces.Repositories
{
    public interface IWalletRepository
    {
        public Task<IEnumerable<WalletCurrency>> GetWalletCurrenciesAsync(int ContactID);
        public Task<IEnumerable<WalletAccountType>> GetWalletAccountTypesAsync(char type = 'A');
        public Task<List<WalletAccount>> GetWalletAccounts(int contactId, int CAID = -1, int type = 1, int PaySubType = -1);
        public Task<List<WalletAccount>> GetCurrencyWallets(int RefContactID, int MainCAID);
        public Task<decimal> GetCustomerWalletBalance(int CAID, int contactId);
        public Task<List<T>> GetChildAccounts<T>(int parentContactId);
        public Task<List<ChildAccountDetails>> GetChildAccountsDetails(int parentContactId);
        public Task<List<GoalAccount>> GetGoalsAccounts(int ContactId, int paytype = 1, int PaySubType = 0);
        public Task<List<GoalAccount>> M_GetGoalsAccounts(string ContactIds, string CAIDs, int paytype = 1, int PaySubType = 0);
        public Task<List<MenuViewModel>> GetWalletMenu(int ContactID, ICardRepository cardRepository);
        public Task<ChildAccount> GetSingleAccount<T>(int contactId, int CAID);
        public Task<MerchantData> GetMerchantDataAsync(int CustomerID);
    }
}
