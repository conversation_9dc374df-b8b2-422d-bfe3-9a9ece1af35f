using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption;
using Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.Context;
using System.Reflection;
using System.Security.Claims;
using System.Threading.RateLimiting;
using Optimum.Wallet.Api.Configurations;
using Optimum.Wallet.Api.Middlewares;
using Optimum.Wallet.Core.Settings;
using Optimum.Wallet.Application.Services;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Application;
using Optimum.Wallet.Application.Interfaces;
using Optimum.Wallet.Infrastructure;
using Optimum.Wallet.Core.Interfaces;

[assembly: ApiController]
var builder = WebApplication.CreateBuilder(args);

Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(builder.Configuration)
                .Enrich.FromLogContext()
                .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
                .CreateLogger();

// Add services to the container.

builder.Host.UseSerilog();
Log.Information("Optimum Wallet Api Started");
Config.Set(builder.Configuration);

var services = builder.Services;

services.AddHttpClient();
services.AddLocalization();
services.AddMemoryCache();
services.AddDistributedMemoryCache();
services.AddSingleton<LocalizationMiddleware>();
services.Configure<RequestLocalizationOptions>(
                options =>
                {
                    var supportedCultures = Config.supportedCultureInfo;
                    options.DefaultRequestCulture = new RequestCulture(Config.GetDefaultCultureName());
                    options.SupportedCultures = supportedCultures;
                    options.SupportedUICultures = supportedCultures;
                });
services.AddDistributedMemoryCache();
//services.AddSingleton<IStringLocalizerFactory, JsonStringLocalizerFactory>();
services.AddControllers()
    .AddNewtonsoftJson(options => options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore); ;
services.AddHttpContextAccessor();
services.AddCustomeSwaggerservice();

services.AddDataProtection()
    .PersistKeysToFileSystem(new DirectoryInfo(@"C:\\WalletKeys"))
    .UseCryptographicAlgorithms(
    new AuthenticatedEncryptorConfiguration()
    {
        EncryptionAlgorithm = EncryptionAlgorithm.AES_256_CBC,
        ValidationAlgorithm = ValidationAlgorithm.HMACSHA256
    });
services.AddCustomJWTservice(builder.Configuration);

services.AddScoped<IAccountService, AccountService>();
services.AddScoped<IPaymentService, PaymentService>();
services.AddTransient<CacheHelper>();

services.AddApplication();
services.AddInfrastructureServices(builder.Configuration);
services.AddHealthChecks()
    .AddAsyncCheck("sql", async  () =>
    {
        try
        {
            var connection = new SqlConnection(Config.GetConnectionString("Utilities"));
            await connection.OpenAsync();
            await connection.CloseAsync();
        }
        catch(Exception)
        {
            Log.Error($"Unhealthy sql connection..");
            return HealthCheckResult.Unhealthy();
        }
        return HealthCheckResult.Healthy();
    });

var myOptions = new RateLimitOptions();
builder.Configuration.GetSection(RateLimitOptions.RateLimit).Bind(myOptions);

builder.Services.AddRateLimiter(options =>
{
    options.RejectionStatusCode = 429;
    options.OnRejected = (context, cancellationToken) =>
    {
        context.HttpContext.Response.StatusCode = StatusCodes.Status429TooManyRequests;
        Log.Information($"Ratelimit rejected request : keys are {context.HttpContext.User.Claims.FirstOrDefault(c => c.Type == "Name")?.Value} - {context.HttpContext.Connection.RemoteIpAddress.ToString()}");
        return new ValueTask();
    };
    options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(
        httpcontext =>
    RateLimitPartition.GetFixedWindowLimiter(
            partitionKey: httpcontext.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? httpcontext.Connection.RemoteIpAddress.ToString(),
            factory: partition => new FixedWindowRateLimiterOptions
            {
                AutoReplenishment = myOptions.AutoReplenishment,
                PermitLimit = myOptions.PermitLimit,
                QueueLimit = myOptions.QueueLimit,
                QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                Window = TimeSpan.FromSeconds(myOptions.Window)
            }
        ));
    options.AddPolicy("Web", httpContext =>
    RateLimitPartition.GetFixedWindowLimiter(httpContext.Connection.RemoteIpAddress.ToString(),
    partition => new FixedWindowRateLimiterOptions
    {
        AutoReplenishment = myOptions.AutoReplenishment,
        PermitLimit = 25,
        Window = TimeSpan.FromSeconds(1)
    }));
});

var app = builder.Build();
var env = app.Environment;

app.UseErrorHandlerMiddleware();
app.UseStaticFiles(new StaticFileOptions
{
    OnPrepareResponse = ctx =>
    {
        ctx.Context.Response.Headers.Append("Cache-Control", "public, max-age=********");
    }
});


app.UseHttpsRedirection();

var localizeOptions = app.Services.GetService<IOptions<RequestLocalizationOptions>>();
app.UseRequestLocalization(localizeOptions.Value);
app.UseMiddleware<LocalizationMiddleware>();
app.UseVerifyTokenStatus();
app.UseRouting();
app.UseAuthentication();

app.Use(async (httpContext, next) =>
{
    var username = httpContext.User.Identity.IsAuthenticated ? httpContext.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value : "anonymous";
    LogContext.PushProperty("User", username);

    var ip = httpContext.Connection.RemoteIpAddress.ToString();
    LogContext.PushProperty("IP", !String.IsNullOrWhiteSpace(ip) ? ip : "unknown");

    await next.Invoke();
});

app.UseSerilogRequestLogging();

app.UseAuthorization();
app.UseRateLimiter();
app.UseSwaggerBasicAuthMiddleware();
// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment() || app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}
app.UseHealthChecks("/health");
app.MapControllers();

app.Run();
