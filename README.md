# Optimum Wallet Core API

## Overview
Optimum Wallet Core API is a comprehensive digital wallet solution that provides secure payment processing, card management, and account services. This API serves as the backend for the wallet application, offering a robust set of features for financial transactions and user account management.

The project follows **Clean Architecture** principles, ensuring separation of concerns, maintainability, and testability while providing a scalable foundation for the digital wallet ecosystem.

## Architecture Overview

### Clean Architecture Implementation

This project implements Clean Architecture with four distinct layers, each with specific responsibilities and dependencies flowing inward toward the domain:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                        │
│                 (Optimum.Wallet.Api)                       │
│  Controllers │ Middlewares │ Authentication │ API Models   │
└─────────────────────┬───────────────────────────────────────┘
                      │ Dependencies flow inward
┌─────────────────────▼───────────────────────────────────────┐
│                   Application Layer                         │
│               (Optimum.Wallet.Application)                  │
│   Services │ Interfaces │ DTOs │ Mappings │ Validation     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  Infrastructure Layer                       │
│               (Optimum.Wallet.Infrastructure)               │
│  Repositories │ External Services │ Data Access │ Caching   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                    Domain Layer                             │
│                 (Optimum.Wallet.Domain)                    │
│    Entities │ Value Objects │ Domain Events │ Exceptions   │
└─────────────────────────────────────────────────────────────┘
```

## Project Structure & Responsibilities

### 1. **Optimum.Wallet.Api** (Presentation Layer)
**Responsibility**: HTTP API endpoints, request/response handling, authentication, and middleware

**Key Components**:
- **Controllers**: RESTful API endpoints
  - `AccountController.cs`: User authentication, registration, profile management
  - `WalletController.cs`: Wallet operations, balance inquiries
  - `CardsController.cs`: Virtual/physical card management
  - `PaymentController.cs`: Payment processing and transactions
  - `WalletActionsController.cs`: Wallet-specific actions and operations

- **Middlewares**: Cross-cutting concerns
  - `ErrorHandlerMiddleware.cs`: Global exception handling
  - `LocalizationMiddleware.cs`: Multi-language support
  - `SwaggerBasicAuthMiddleware.cs`: API documentation security
  - `VerifyTokenStatus.cs`: JWT token validation

- **Configuration**: Application setup and dependency injection
  - `Program.cs`: Application bootstrap and service configuration
  - `CustomServiceExtensions.cs`: Custom service registrations

**Dependencies**: Only depends on Application layer interfaces

### 2. **Optimum.Wallet.Application** (Application Layer)
**Responsibility**: Business logic orchestration, use cases, and application services

**Key Components**:
- **Services**: Business logic implementation
  - `AccountService.cs`: Account-related business operations
  - `PaymentService.cs`: Payment processing logic
  - `WebServices.cs`: External service integrations

- **Interfaces**: Contracts for repositories and services
  - `IAccountService.cs`: Account service contract
  - `IPaymentService.cs`: Payment service contract
  - `IWebServices.cs`: External services contract
  - Repository interfaces for data access abstraction

- **Common**: Shared application logic
  - DTOs for data transfer between layers
  - Helper classes and utilities
  - Response models and API contracts

- **Mappings**: Object-to-object mapping profiles
  - AutoMapper configurations for entity transformations

**Dependencies**: Only depends on Domain layer

### 3. **Optimum.Wallet.Infrastructure** (Infrastructure Layer)
**Responsibility**: External concerns, data persistence, and third-party integrations

**Key Components**:
- **Repository Implementations**: Data access layer using Entity Framework Core and Dapper
  - `WalletRepository.cs`: Wallet-related database operations
  - `CardRepository.cs`: Card management data access
  - `FormRepository.cs`: Form and configuration data operations
  - `GenericRepository.cs`: Generic CRUD operations for entities
  - `TokenRequest.cs`: Authentication token management

- **Data Context**: Entity Framework Core database context
  - `ApplicationDbContext.cs`: Main database context with entity configurations
  - Database migrations and schema management

- **Services**: Infrastructure service implementations
  - `WebServices.cs`: External API integrations (Payment gateways, SMS, Email)
  - `CrediAppService.cs`: Credit application processing
  - External service implementations and integrations

- **Dependency Injection**: Infrastructure service registration
  - `DependencyInjection.cs`: Infrastructure layer service configuration
  - Repository and service lifetime management

**Dependencies**: Depends on Domain and Application layers

### 4. **Optimum.Wallet.Domain** (Domain Layer)
**Responsibility**: Core business entities, rules, and domain logic

**Key Components**:
- **Entities**: Core business objects with identity
  - User, Wallet, Card, Transaction entities
  - Rich domain models with business behavior

- **Value Objects**: Objects defined by their attributes
  - Money, Address, Phone number representations

- **Domain Events**: Business events that occur within the domain
  - Transaction completed, card activated, etc.

- **Exceptions**: Domain-specific exceptions
  - Business rule violations
  - Domain constraint failures

**Dependencies**: No dependencies on other layers or external frameworks

### 5. **Optimum.Wallet.Core** (Legacy/Shared)
**Status**: Being phased out in favor of proper layer separation
- Contains configuration settings and constants
- Gradually being moved to appropriate layers

## Request Flow & Data Pipeline

### Typical Request Flow

```
┌─────────────┐    ┌──────────────┐    ┌─────────────────┐    ┌──────────────┐
│   Client    │────│ Middleware   │────│   Controller    │────│  Application │
│ (Mobile App)│    │  Pipeline    │    │    (API)        │    │   Service    │
└─────────────┘    └──────────────┘    └─────────────────┘    └──────────────┘
                           │                     │                      │
                           ▼                     ▼                      ▼
                   ┌──────────────┐    ┌─────────────────┐    ┌──────────────┐
                   │   Security   │    │   Validation    │    │   Business   │
                   │ & Localization│    │  & Mapping     │    │    Logic     │
                   └──────────────┘    └─────────────────┘    └──────────────┘
                                                                      │
                                                                      ▼
                                              ┌──────────────┐    ┌──────────────┐
                                              │Infrastructure│────│   Database   │
                                              │  Repository  │    │  (SQL Server)│
                                              └──────────────┘    └──────────────┘
```

### 1. **Request Processing Pipeline**

**Middleware Layer**:
1. **LocalizationMiddleware**: Determines user language (Arabic/English)
2. **ErrorHandlerMiddleware**: Global exception handling and logging
3. **Authentication**: JWT token validation and user context setup
4. **Rate Limiting**: Prevents API abuse with configurable limits

**Controller Layer**:
1. **Authorization**: Validates user permissions for endpoints
2. **Model Binding**: Maps HTTP requests to C# objects
3. **Validation**: Input validation using data annotations
4. **Service Invocation**: Calls appropriate application services

### 2. **Business Logic Flow**

**Application Services**:
- **AccountService**: Handles user registration, authentication, profile updates
- **PaymentService**: Processes transactions, integrates with payment gateways
- **WebServices**: Manages external API communications

**Repository Pattern**:
- **ICardRepository**: Card-related data operations (implemented in Infrastructure layer)
- **IWalletRepository**: Wallet balance and transaction operations (implemented in Infrastructure layer)
- **IFormRepository**: Form and configuration data access (implemented in Infrastructure layer)
- **IGenericRepositoryAsync<T>**: Generic CRUD operations for any entity type (implemented in Infrastructure layer)

All repository implementations are located in `Optimum.Wallet.Infrastructure.Repository` and use Entity Framework Core with Dapper for optimal performance.

### 3. **Data Access Pattern**

The application follows Clean Architecture principles with repository implementations in the Infrastructure layer:

```csharp
// Example flow for wallet balance inquiry
[HttpGet("balance")]
public async Task<IActionResult> GetBalance()
{
    // 1. Controller receives request
    var walletId = CurrentUser.WalletId;
    
    // 2. Calls application service
    var balance = await _walletService.GetBalanceAsync(walletId);
    
    // 3. Returns standardized response
    return Ok(BaseResponse<decimal>.Success(balance));
}

// Application Service Implementation
public async Task<decimal> GetBalanceAsync(int walletId)
{
    // 1. Business logic validation
    if (walletId <= 0) throw new InvalidWalletException();
    
    // 2. Repository call (injected from Infrastructure layer)
    var wallet = await _walletRepository.GetByIdAsync(walletId);
    
    // 3. Business rule application
    return wallet.CalculateAvailableBalance();
}

// Repository Implementation (in Infrastructure layer)
public class WalletRepository : GenericRepositoryAsync<Wallet>, IWalletRepository
{
    private readonly ApplicationDbContext _dbContext;
    
    public WalletRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }
    
    public async Task<Wallet> GetByIdAsync(int walletId)
    {
        return await _dbContext.Wallets
            .FirstOrDefaultAsync(w => w.Id == walletId);
    }
}
```

**Key Architecture Benefits**:
- **Separation of Concerns**: Data access logic is isolated in Infrastructure layer
- **Testability**: Controllers and services can be unit tested with mocked repositories
- **Maintainability**: Database changes don't affect business logic
- **Clean Dependencies**: API layer only depends on Application interfaces, not Infrastructure implementations

## Key Features

### Core Functionality
- **Account Management**: User registration, authentication, and profile management
- **Card Management**: Virtual and physical card operations
- **Payment Processing**: Secure payment transactions with Benefit Payment Gateway integration
- **Wallet Operations**: Fund transfers, balance inquiries, and transaction history
- **Multi-language Support**: Localization for English and Arabic

### Advanced Features
- **Real-time Notifications**: OneSignal integration for push notifications
- **Rate Limiting**: Built-in protection against API abuse
- **Data Protection**: ASP.NET Core Data Protection API for sensitive data
- **Health Monitoring**: Comprehensive health checks and monitoring
- **Audit Trail**: Complete transaction and activity logging
- **Multi-tenant Support**: Support for multiple wallet configurations

## Architecture Benefits

### 1. **Separation of Concerns**
- Each layer has a single, well-defined responsibility
- Business logic is isolated from infrastructure concerns
- UI logic is separated from data access

### 2. **Testability**
- Easy unit testing with proper abstractions
- Mock-friendly interfaces
- Isolated business logic testing

### 3. **Maintainability**
- Changes in one layer don't cascade to others
- Clear dependency boundaries
- Easier code refactoring and updates

### 4. **Scalability**
- Infrastructure components can be scaled independently
- Easy to add new features without affecting existing code
- Microservices-ready architecture

### 5. **Flexibility**
- Easy to swap out infrastructure components
- Database-agnostic business logic
- Framework-independent domain model

## Security Architecture

### Authentication & Authorization
```
┌─────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Client    │────│ JWT Token    │────│   Middleware    │
│             │    │ Validation   │    │  Authorization  │
└─────────────┘    └──────────────┘    └─────────────────┘
                           │                     │
                           ▼                     ▼
                   ┌──────────────┐    ┌─────────────────┐
                   │  User Claims │    │   Role-based    │
                   │  Extraction  │    │   Access Control│
                   └──────────────┘    └─────────────────┘
```

### Security Features
- **JWT Authentication**: Stateless token-based authentication
- **Data Protection**: Encryption for sensitive data at rest
- **HTTPS Enforcement**: TLS encryption for data in transit
- **Rate Limiting**: Protection against DDoS and brute force attacks
- **Input Validation**: Comprehensive validation to prevent injection attacks
- **Audit Logging**: Complete audit trail of all operations

## Recent Architecture Improvements

### Clean Architecture Refactoring (June 2025)

The project has been recently refactored to strictly follow Clean Architecture principles:

**Key Changes Made**:
1. **Repository Migration**: Moved all repository implementations from `Optimum.Wallet.Api.Data.Repository` to `Optimum.Wallet.Infrastructure.Repository`
2. **Data Context Relocation**: Moved `ApplicationDbContext` from API layer to `Optimum.Wallet.Infrastructure.Data.Context`
3. **Dependency Injection Improvement**: Centralized infrastructure service registration in `Optimum.Wallet.Infrastructure.DependencyInjection`
4. **Namespace Standardization**: Updated all namespaces to reflect proper layer separation
5. **Generic Repository Pattern**: Enhanced with `IGenericRepositoryAsync<T>` for common CRUD operations

**Benefits Achieved**:
- ✅ **Proper Layer Separation**: Data access logic is now properly isolated in Infrastructure layer
- ✅ **Improved Testability**: Controllers no longer directly depend on Entity Framework
- ✅ **Enhanced Maintainability**: Clear boundaries between layers make changes easier
- ✅ **Better Scalability**: Infrastructure components can be swapped without affecting business logic
- ✅ **Clean Dependencies**: API layer only references Application interfaces

**Migration Guide for Developers**:
- Repository interfaces remain in `Optimum.Wallet.Application.Interfaces`
- Repository implementations are now in `Optimum.Wallet.Infrastructure.Repository`
- Use dependency injection to access repositories through interfaces
- All database context usage should go through repository abstractions

## Technologies & Dependencies

### Core Technologies
- **Framework**: ASP.NET Core 8.0
- **Authentication**: JWT (JSON Web Tokens)
- **Database**: SQL Server with Entity Framework Core 8.0.17 and Dapper ORM
- **Logging**: Serilog with structured logging
- **API Documentation**: Swagger/OpenAPI
- **Caching**: In-memory and distributed caching
- **Mapping**: AutoMapper for object transformations
- **Data Protection**: ASP.NET Core Data Protection API 8.0.17

### Infrastructure Dependencies
- **Entity Framework Core**: 8.0.17 (Primary ORM)
- **Dapper**: 2.1.66 (High-performance queries)
- **Microsoft.AspNetCore.DataProtection**: 8.0.17
- **Microsoft.Extensions.DependencyInjection**: 8.0.17
- **Newtonsoft.Json**: 13.0.3
- **System.ServiceModel**: 6.2.0 (SOAP services)

### Infrastructure Components
- **Rate Limiting**: Built-in ASP.NET Core rate limiting
- **Data Protection**: ASP.NET Core Data Protection API
- **Notifications**: OneSignal integration
- **Localization**: Multi-language support (English/Arabic)
- **Health Checks**: Comprehensive system monitoring
- **Configuration**: ASP.NET Core Configuration system

### External Integrations
- **Payment Gateway**: Benefit Payment Gateway integration
- **SMS Services**: Multiple SMS provider support
- **Email Services**: SMTP integration for notifications
- **File Storage**: Configurable file storage solutions

## Development Setup

### Prerequisites

- **.NET 8.0 SDK or later**
- **SQL Server** (LocalDB acceptable for development)
- **Visual Studio 2022** or **VS Code** with C# extension
- **Git** for version control

### Project Dependencies
The solution follows Clean Architecture with these project references:
- **Optimum.Wallet.Api** → Optimum.Wallet.Application, Optimum.Wallet.Infrastructure
- **Optimum.Wallet.Application** → Optimum.Wallet.Domain, Optimum.Wallet.Core
- **Optimum.Wallet.Infrastructure** → Optimum.Wallet.Application, Optimum.Wallet.Domain, Optimum.Wallet.Core
- **Optimum.Wallet.Domain** → (No dependencies)
- **Optimum.Wallet.Core** → (Shared utilities)

### Configuration

The application uses a layered configuration approach with `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=...;Database=OptimumWallet;..."
  },
  "JwtSettings": {
    "SecretKey": "your-secret-key",
    "Issuer": "OptimumWallet",
    "Audience": "OptimumWalletUsers",
    "ExpirationMinutes": 60
  },
  "RateLimiting": {
    "PermitLimit": 100,
    "Window": "00:01:00"
  },
  "PaymentGateway": {
    "BenefitApiUrl": "https://api.benefit.com",
    "MerchantId": "your-merchant-id",
    "SecretKey": "your-secret-key"
  }
}
```

### Running the Application

1. **Clone the repository**
   ```powershell
   git clone https://github.com/your-org/Optimum-Wallet-Core-Api.git
   cd Optimum-Wallet-Core-Api
   ```

2. **Restore dependencies**
   ```powershell
   dotnet restore
   ```

3. **Update configuration**
   - Copy `appsettings.Development.json.example` to `appsettings.Development.json`
   - Update connection strings and API keys

4. **Run database migrations** (if applicable)
   ```powershell
   dotnet ef database update
   ```

5. **Start the application**
   ```powershell
   dotnet run --project Optimum.Wallet.Api
   ```

6. **Access the API**
   - API: `https://localhost:5001`
   - Swagger UI: `https://localhost:5001/swagger`
   - Health Check: `https://localhost:5001/health`

## API Documentation

### Swagger Integration
API documentation is automatically generated and available at `/swagger` when running in development mode.

### Authentication
Most endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Response Format
All API responses follow a consistent format:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ },
  "errors": null,
  "timestamp": "2025-06-25T10:30:00Z"
}
```

### Key Endpoints

#### Account Management
- `POST /api/account/register` - User registration
- `POST /api/account/login` - User authentication  
- `GET /api/account/profile` - Get user profile
- `PUT /api/account/profile` - Update user profile

#### Wallet Operations
- `GET /api/wallet` - Get wallet information
- `GET /api/wallet/balance` - Get wallet balance
- `GET /api/wallet/transactions` - Get transaction history
- `POST /api/wallet/transfer` - Transfer funds

#### Card Management
- `GET /api/cards` - Get user cards
- `POST /api/cards/virtual` - Create virtual card
- `POST /api/cards/physical` - Request physical card
- `PUT /api/cards/{id}/activate` - Activate card

#### Payment Processing
- `POST /api/payment/process` - Process payment
- `GET /api/payment/{id}/status` - Check payment status
- `POST /api/payment/refund` - Process refund

## Monitoring & Observability

### Health Checks
The application includes comprehensive health checks:
- Database connectivity
- External service availability
- Application dependencies
- System resources

Access health status at: `GET /health`

### Logging Strategy
**Serilog Configuration**:
- **Structured Logging**: JSON format for machine processing
- **Log Levels**: Trace, Debug, Information, Warning, Error, Critical
- **Log Sinks**: File, Console, and optional external systems
- **Correlation IDs**: Request tracing across components

**Log Locations**:
- Development: Console + File (`logs/wallet-api-{date}.log`)
- Production: File + External monitoring systems

### Performance Monitoring
- **Response Time Tracking**: Built-in request timing
- **Error Rate Monitoring**: Automatic error aggregation
- **Resource Usage**: Memory and CPU monitoring
- **Cache Hit Rates**: Caching effectiveness metrics

## Security Implementation

### Data Protection
- **Encryption at Rest**: Sensitive data encrypted in database
- **Encryption in Transit**: HTTPS/TLS for all communications
- **Key Management**: ASP.NET Core Data Protection API
- **PII Protection**: Personal information encryption

### Input Validation
- **Model Validation**: Data annotations and FluentValidation
- **SQL Injection Prevention**: Parameterized queries with Dapper
- **XSS Protection**: Input sanitization and output encoding
- **CSRF Protection**: Anti-forgery tokens

### Authentication Flow
```
┌─────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Login     │────│   Validate   │────│   Generate JWT  │
│  Request    │    │ Credentials  │    │     Token       │
└─────────────┘    └──────────────┘    └─────────────────┘
       │                   │                      │
       ▼                   ▼                      ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Store     │────│   Return     │────│   Subsequent    │
│  Session    │    │   Token      │    │   Requests      │
└─────────────┘    └──────────────┘    └─────────────────┘
```

## Deployment Architecture

### Environment Configuration
- **Development**: Local development with SQL Server LocalDB
- **Staging**: Pre-production environment with full external integrations
- **Production**: High-availability deployment with load balancing

### Scaling Considerations
- **Horizontal Scaling**: Stateless design supports multiple instances
- **Database Scaling**: Read replicas and connection pooling
- **Caching Strategy**: Distributed caching for session data
- **Load Balancing**: Support for multiple API instances

## Development Guidelines

### Code Organization
- **Clean Architecture**: Strict layer separation
- **SOLID Principles**: Single responsibility, open/closed, etc.
- **DRY Principle**: Don't repeat yourself
- **Dependency Injection**: Constructor injection throughout

### Testing Strategy
- **Unit Tests**: Business logic and service layer testing
- **Integration Tests**: API endpoint and database testing
- **Security Tests**: Authentication and authorization testing
- **Performance Tests**: Load and stress testing

### Code Quality
- **Static Analysis**: Code quality rules and standards
- **Code Reviews**: Peer review process
- **Documentation**: Comprehensive inline documentation
- **Consistent Formatting**: EditorConfig and code style rules

## Future Enhancements

### Planned Features
- **Microservices Migration**: Break down into smaller services
- **Event Sourcing**: Implement event-driven architecture
- **GraphQL Support**: Alternative query interface
- **Advanced Analytics**: Transaction pattern analysis
- **Machine Learning**: Fraud detection and risk assessment

### Technology Upgrades
- **.NET 8 Migration**: Latest framework features
- **Container Support**: Docker and Kubernetes deployment
- **Cloud Integration**: Azure/AWS service integration
- **Advanced Monitoring**: Application Performance Monitoring (APM)

---

## Contributing

Please read our contributing guidelines and follow the established architecture patterns when adding new features or making modifications.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
