// using Microsoft.Extensions.Localization;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Application.Common.Models;

namespace Optimum.Wallet.Application.Common
{
    public sealed class WalletApplication
    {
        // Keep track of the number of decimals to be used across the application
        public static int NoOfDecimals { get; set; }

        // Keep track of the default currency to be used across the application
        public static string Currency { get; set; }


        // Keep track of the maximum amount to be used across the application throgh the input
        public string MaximumInputAmount { get; set; }

        public string MaximumInputText { get; set; }
        public int MaximumAddChild { get; set; }

        public Dictionary<string, string> MultiLnagCurrencies { get; set; }

        // Keep track of the company Id
        public static string CompanyId => Config.GetStringValue("CompanyId") ?? "-1";

        // Keep track of the branch Id
        public static string BranchId => Config.GetStringValue("BranchId") ?? "-1";

        // Keep track of the application folder
        public static string AppFolder => Config.GetStringValue("AppFolder") ?? "";

        public static string PhotosPath => Config.GetStringValue("PhotosPath") ?? "";
        public static string MobileAppFolder => Config.GetStringValue("MobileAppFolder") ?? "";

        public static bool MarketingNotificationServiceLocked { get; set; }

        public static bool OtherNotificationServiceLocked { get; set; }

        public static bool OneSignalServiceLocked { get; set; }
        public static bool GPSContactServiceLocked { get; set; }

        public static bool EnforceOTPInDesktop => (Config.GetStringValue("EnforceOTPInDesktop") + "").ToUpper() == "TRUE";

        public const string SSOSignCertKeyName = "SSOSignCert";

        public static bool HasEntertainer => (Config.GetStringValue("EntertainerEnabled") + "").ToUpper() == "TRUE";

        public static bool IsCardTransactionExecuted = false;

        public static List<LanguageModel> Languages { get; set; }

        public static List<int> MultiCurrencyForms => new List<int> { PageTypes.WALLET_ADD_MONEY, PageTypes.WALLET_SEND_MONEY, PageTypes.CARD_ADD_MONEY, PageTypes.CARD_SEND_MONEY, PageTypes.WALLET_SCAN_AND_PAY };

        // Contructor for the signleton instance
        private WalletApplication(object globalLocalizer = null)
        {
            // Initialize the decimals
            NoOfDecimals = GetNoOfDecimals();

            // Initialize the currency
            Currency = GetCurrency();

            MaximumInputAmount = GetEnvVariable("MaxAmountInputValidation", "100000");
            MaximumInputText = GetEnvVariable("MaxTextInputValidation", "70");

            int MaxNoiLeadChildAdded = 0;
            int.TryParse(GetEnvVariable("MaxNoiLeadChildAdded", "10"), out MaxNoiLeadChildAdded);
            MaximumAddChild = MaxNoiLeadChildAdded;

            // Initialize the Languages
            Languages = GetLanguages();

            MarketingNotificationServiceLocked = false;
            OtherNotificationServiceLocked = false;
            OneSignalServiceLocked = false;
        }

        private int GetNoOfDecimals(int defaultValue = 3)
        {
            var dbValue = DapperHelper.Query<string>("SELECT Decimal FROM [Currency File] WHERE BranchID = @BranchId", new { BranchId = 0 }, "Ledger").FirstOrDefault();

            int decimals;
            return int.TryParse(dbValue + "", out decimals) ? decimals : defaultValue;
        }

        public string GetCurrency(string defaultValue = "")
        {
            var dbValue = DapperHelper.Query<string>($"SELECT Col_CurrencyFile_SwiftCode FROM [Currency File] WHERE BranchID = @BranchId AND Active = 1", new { BranchId = 0 }, "Ledger").FirstOrDefault();
            return !string.IsNullOrWhiteSpace(dbValue) ? dbValue : defaultValue;
        }

        public List<LanguageModel> GetLanguages()
        {
            var LangList = DapperHelper.Query<LanguageModel>($"SELECT Id,LangName 'Name',LangCode 'Code' , FlagImg FROM web_Langtbl WHERE Active=1 ORDER BY sort").ToList();
            return LangList != null && LangList.Count > 0 ? LangList : new List<LanguageModel>() { new LanguageModel { Id = 1, Name = "English", Code = "en" } };
        }

        public static string GetEnvVariable(string varName, string defaultValue = "")
        {
            var dbValue = DapperHelper.Query<string>(
                " SELECT Value FROM EnvSetupTbl" +
                " WHERE VariableName = @VariableName AND CompID = @CompId AND Branch = @Branch AND Active = 1",
                new
                {
                    VariableName = varName,
                    CompId = CompanyId,
                    Branch = BranchId
                }).FirstOrDefault();

            return !string.IsNullOrWhiteSpace(dbValue) ? dbValue : defaultValue;
        }

        public static string WebServiceUsername => Config.GetStringValue("WebServiceUsername") ?? "";
        public static string WebServicePassword => Config.GetStringValue("WebServicePassword") ?? "";
        public static string WebServiceDateFormat => Config.GetStringValue("WebServiceDateFormat") ?? "yyyy-MM-dd";

        public static bool IsDummyData => Config.GetStringValue("DummyData").ToLower() == "true";


        public static bool IsAllowLoginByCPR => Config.GetStringValue("AllowLoginBy").ToLower().Contains("cpr");
        public static bool IsAllowLoginByMobile => Config.GetStringValue("AllowLoginBy").ToLower().Contains("mobile");

        public static string HideBeneficiaries => Config.GetStringValue("HideBeneficiaries") ?? "";
        public static string RefirectServiceToProductIdNo => Config.GetStringValue("RefirectServiceToProductIdNo") ?? "";

        private static IEnumerable<LanguageModel> GetLanguagesExludingTheCurrentLanguage(string currentLanguage)
        {
            return Languages.Where(lang => lang.Code != currentLanguage).OrderBy(lang => lang.Name);
        }

        private static IEnumerable<LanguageModel> GetLanguagesIncludingTheCurrentLanguage()
        {
            return Languages.OrderBy(lang => lang.Name);
        }

        private static IEnumerable<LanguageModel> GetCurrentLanguage()
        {
            return Languages.Where(l => l.Code == LanguageHelper.GetCurrentLangCode());
        }
    }
}