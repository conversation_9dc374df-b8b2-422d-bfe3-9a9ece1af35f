//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Optimum.Wallet.Domain.Entities
{
    using System;
    using System.Collections.Generic;
    
    public class PAYMENT_GATEWAY_TYPES
    {
        public PAYMENT_GATEWAY_TYPES()
        {
            this.PAYMENT_GATEWAY_PARAMS = new HashSet<PAYMENT_GATEWAY_PARAMS>();
            this.PAYMENT_GATEWAY_TRANSACTIONS = new HashSet<PAYMENT_GATEWAY_TRANSACTIONS>();
        }
    
        public int ID { get; set; }
        public string PG_Name { get; set; }
        public string virtualPaymentClientURL { get; set; }
        public string SecureHashing { get; set; }
        public int ReceiptBankAccountNo { get; set; }
        public string OptimumPGUser { get; set; }
        public Nullable<int> PaymentTypeID { get; set; }
    
        public virtual ICollection<PAYMENT_GATEWAY_PARAMS> PAYMENT_GATEWAY_PARAMS { get; set; }
        public virtual ICollection<PAYMENT_GATEWAY_TRANSACTIONS> PAYMENT_GATEWAY_TRANSACTIONS { get; set; }
    }
}
