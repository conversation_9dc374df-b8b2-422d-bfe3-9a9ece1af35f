﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Card" xml:space="preserve">
    <value>Card</value>
  </data>
  <data name="ContactBody" xml:space="preserve">
    <value>&lt;p&gt;Should you have further inquiries, please do contact us. We are at your service.&lt;/p&gt;
                    &lt;p class="contact-information"&gt;
                        &lt;strong class="color-theme font-16"&gt;Working hours&lt;/strong&gt;
                        &lt;br&gt; Sunday to Thursday 08:00 to 17:00
                    &lt;/p&gt;</value>
  </data>
  <data name="ErrorPayInsertPaymentDetails" xml:space="preserve">
    <value>Failed to insert your transaction details into our records. Please try again later.</value>
  </data>
  <data name="ErrorPayProcessTranContactUs" xml:space="preserve">
    <value>Failed to process your transaction details. Please contact us to enquire about your transaction.</value>
  </data>
  <data name="ErrorPayRetrievePaymentDetails" xml:space="preserve">
    <value>Failed to retrieve the transaction details required to process your payment. Please try again later.</value>
  </data>
  <data name="ErrorPayRetrieveTranDetailsContactUs" xml:space="preserve">
    <value>Failed to retrieve your transaction details. Please contact us to enquire about your transaction.</value>
  </data>
  <data name="ErrorPayRetrieveTranRecordContactUs" xml:space="preserve">
    <value>Failed to retrieve your transaction record. Please contact us to enquire about your transaction.</value>
  </data>
  <data name="ErrorPayRetrieveTransactionIdentifier" xml:space="preserve">
    <value>Failed to retrieve your transaction identifier. Please contact us to enquire about your transaction.</value>
  </data>
  <data name="ErrorPaySubmitPaymentDetails" xml:space="preserve">
    <value>Failed to submit your transaction details to the payment gateway. Please try again later.</value>
  </data>
  <data name="ExceedMaximumDailyTransferLimitError" xml:space="preserve">
    <value>You are exceeding your maximum daily transfer limit of {0}. You can only transfer {1} for today.</value>
    <comment>{0}{1}</comment>
  </data>
  <data name="InactiveUserError" xml:space="preserve">
    <value>Operation performed with inactive user.</value>
  </data>
  <data name="InsufficientCardBalance" xml:space="preserve">
    <value>You have insufficient card balance.</value>
  </data>
  <data name="InsufficientWalletBalance" xml:space="preserve">
    <value>You have insufficient wallet balance.</value>
  </data>
  <data name="InvalidAmountError" xml:space="preserve">
    <value>The given amount is invalid.</value>
  </data>
  <data name="InvalidRecipientError" xml:space="preserve">
    <value>You have entered invalid recipient details.</value>
  </data>
  <data name="LimitReachError" xml:space="preserve">
    <value>You have reached your limit of {0} transactions per day.</value>
    <comment>{0}</comment>
  </data>
  <data name="MoneyTransfer" xml:space="preserve">
    <value>Money Transfer</value>
  </data>
  <data name="MoneyTransferRegisteration" xml:space="preserve">
    <value>Money Transfer Registeration</value>
  </data>
  <data name="MonTransfer0%VAT" xml:space="preserve">
    <value>0 % VAT</value>
  </data>
  <data name="MonTransferAccount/IBANNo" xml:space="preserve">
    <value>Account/IBAN No</value>
  </data>
  <data name="MonTransferAccountNo" xml:space="preserve">
    <value>Account No</value>
  </data>
  <data name="MonTransferACNumbera" xml:space="preserve">
    <value>A/C Number</value>
  </data>
  <data name="MonTransferAddress" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="MonTransferAgentName" xml:space="preserve">
    <value>Agent Name</value>
  </data>
  <data name="MonTransferAmountAfterVAT" xml:space="preserve">
    <value>Amount After VAT</value>
  </data>
  <data name="MonTransferAmountBeforeVAT" xml:space="preserve">
    <value>Amount Before VAT</value>
  </data>
  <data name="MonTransferBank" xml:space="preserve">
    <value>Bank</value>
  </data>
  <data name="MonTransferBankCode" xml:space="preserve">
    <value>Bank Code</value>
  </data>
  <data name="MonTransferBenBankName" xml:space="preserve">
    <value>Ben Bank Name</value>
  </data>
  <data name="MonTransferBeneficiary" xml:space="preserve">
    <value>Beneficiary</value>
  </data>
  <data name="MonTransferBeneficiaryDetails" xml:space="preserve">
    <value>Beneficiary Details</value>
  </data>
  <data name="MonTransferBeneficiaryName" xml:space="preserve">
    <value>Beneficiary Name</value>
  </data>
  <data name="MonTransferBHD" xml:space="preserve">
    <value>BHD</value>
  </data>
  <data name="MonTransferBranch" xml:space="preserve">
    <value>Branch</value>
  </data>
  <data name="MonTransferBranchCode" xml:space="preserve">
    <value>Branch Code</value>
  </data>
  <data name="MonTransferBranchName" xml:space="preserve">
    <value>Branch Name</value>
  </data>
  <data name="MonTransferCardNo" xml:space="preserve">
    <value>Card Number</value>
  </data>
  <data name="MonTransferCashPickup" xml:space="preserve">
    <value>Cash Pickup</value>
  </data>
  <data name="MonTransferCharge" xml:space="preserve">
    <value>Charge</value>
  </data>
  <data name="MonTransferContactPerson" xml:space="preserve">
    <value>Contact Person</value>
  </data>
  <data name="MonTransferCountry" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="MonTransferCreditToAccount" xml:space="preserve">
    <value>Credit to account</value>
  </data>
  <data name="MonTransferCurrency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="MonTransferCustomerAddress" xml:space="preserve">
    <value>Customer Address</value>
  </data>
  <data name="MonTransferCustomerDetails" xml:space="preserve">
    <value>Customer Details</value>
  </data>
  <data name="MonTransferCustomerName" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="MonTransferDestination" xml:space="preserve">
    <value>Destination</value>
  </data>
  <data name="MonTransferFundsource" xml:space="preserve">
    <value>Fundsource</value>
  </data>
  <data name="MonTransferFXAmount" xml:space="preserve">
    <value>FX Amount</value>
  </data>
  <data name="MonTransferIDNumber" xml:space="preserve">
    <value>ID Number</value>
  </data>
  <data name="MonTransferLocalAmount" xml:space="preserve">
    <value>Local Amount</value>
  </data>
  <data name="MonTransferLocalAmountBHD" xml:space="preserve">
    <value>Local Amount(BHD)</value>
  </data>
  <data name="MonTransferPaymentDetails" xml:space="preserve">
    <value>Payment Details</value>
  </data>
  <data name="MonTransferPaymentOption" xml:space="preserve">
    <value>Payment Option</value>
  </data>
  <data name="MonTransferProductDetails" xml:space="preserve">
    <value>Product Details</value>
  </data>
  <data name="MonTransferProductIssuerNumber" xml:space="preserve">
    <value>Product Issuer Number</value>
  </data>
  <data name="MonTransferPurpose" xml:space="preserve">
    <value>Purpose</value>
  </data>
  <data name="MonTransferQouteFees" xml:space="preserve">
    <value>Fees</value>
  </data>
  <data name="MonTransferQouteFeesWithoutVAT" xml:space="preserve">
    <value>Fees Without VAT</value>
  </data>
  <data name="MonTransferQouteFeeswithVAT" xml:space="preserve">
    <value>Fees with VAT</value>
  </data>
  <data name="MonTransferQouteRate" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="MonTransferQouteSendAmount" xml:space="preserve">
    <value>Send Amount</value>
  </data>
  <data name="MonTransferQouteSubsidy" xml:space="preserve">
    <value>Subsidy</value>
  </data>
  <data name="MonTransferQouteTotalToPay" xml:space="preserve">
    <value>Total to Pay</value>
  </data>
  <data name="MonTransferQouteVAT" xml:space="preserve">
    <value>VAT</value>
  </data>
  <data name="MonTransferRate" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="MonTransferRecipientName" xml:space="preserve">
    <value>Recipient Name</value>
  </data>
  <data name="MonTransferSECNO" xml:space="preserve">
    <value>SEC NO</value>
  </data>
  <data name="MonTransferSelectBeneficiary" xml:space="preserve">
    <value>Select A Beneficiary</value>
  </data>
  <data name="MonTransferSelectCard" xml:space="preserve">
    <value>Select Card</value>
  </data>
  <data name="MonTransferSelectMethod" xml:space="preserve">
    <value>Select Transfer Method</value>
  </data>
  <data name="MonTransferSelectPayOption" xml:space="preserve">
    <value>Select Payment Option</value>
  </data>
  <data name="MonTransferSelectPayOptionCard" xml:space="preserve">
    <value>BFC Pay Card</value>
  </data>
  <data name="MonTransferSelectPayOptionDebit" xml:space="preserve">
    <value>Debit Card</value>
  </data>
  <data name="MonTransferSelectPayOptionWallet" xml:space="preserve">
    <value>Wallet</value>
  </data>
  <data name="MonTransferSelectPurpose" xml:space="preserve">
    <value>Select Purpose</value>
  </data>
  <data name="MonTransferSelectSource" xml:space="preserve">
    <value>Select Source</value>
  </data>
  <data name="MonTransferSource" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="MonTransferSubsidy" xml:space="preserve">
    <value>Subsidy</value>
  </data>
  <data name="MonTransferTaxInvoiceNo" xml:space="preserve">
    <value>Tax Invoice No</value>
  </data>
  <data name="MonTransferTelephoneNo" xml:space="preserve">
    <value>Telephone No</value>
  </data>
  <data name="MonTransferTelephoneNumber" xml:space="preserve">
    <value>Telephone Number</value>
  </data>
  <data name="MonTransferTotalAmount" xml:space="preserve">
    <value>Total Amount</value>
  </data>
  <data name="MonTransferTransactionDate" xml:space="preserve">
    <value>Transaction Date</value>
  </data>
  <data name="MonTransferTransactionDetails" xml:space="preserve">
    <value>Transaction Details</value>
  </data>
  <data name="MonTransferTransactionReference's" xml:space="preserve">
    <value>Transaction Reference's</value>
  </data>
  <data name="MonTransferTransactionType" xml:space="preserve">
    <value>Transaction Type</value>
  </data>
  <data name="MonTransferTransferType" xml:space="preserve">
    <value>Transfer Type</value>
  </data>
  <data name="NotMatchInOurRecordError" xml:space="preserve">
    <value>The information that you have provided does not match any account in our records.</value>
  </data>
  <data name="PageTitleMyCards" xml:space="preserve">
    <value>My Cards</value>
  </data>
  <data name="PageTitleMyWallet" xml:space="preserve">
    <value>My Wallet</value>
  </data>
  <data name="PayApprovedMsg" xml:space="preserve">
    <value>Thank you for the card payment of {0}.</value>
    <comment>{0}</comment>
  </data>
  <data name="PayApprovedStatus" xml:space="preserve">
    <value>Payment Approved</value>
  </data>
  <data name="PayAuthorizationNumber" xml:space="preserve">
    <value>Authorization Number</value>
  </data>
  <data name="PayDeclinedMsg" xml:space="preserve">
    <value>Your payment has been declined! Please refer to the details below.</value>
  </data>
  <data name="PayDeclinedStatus" xml:space="preserve">
    <value>Payment Declined</value>
  </data>
  <data name="PayGatewayProcessedMsg" xml:space="preserve">
    <value>Your payment has been processed by the payment gateway and the response details are shown below.</value>
  </data>
  <data name="PaymentStatus" xml:space="preserve">
    <value>Payment Status</value>
  </data>
  <data name="PayReferenceNumber" xml:space="preserve">
    <value>Reference Number</value>
  </data>
  <data name="PayResponseMessage" xml:space="preserve">
    <value>Response Message</value>
  </data>
  <data name="PayTransactionNumber" xml:space="preserve">
    <value>Transaction Number</value>
  </data>
  <data name="ReachMaximumDailyTransferLimitError" xml:space="preserve">
    <value>You have reached your maximum daily transfer limit of {0}.</value>
    <comment>{0}</comment>
  </data>
  <data name="RegisterNow" xml:space="preserve">
    <value>Register Now</value>
  </data>
  <data name="RequestFromYourSelfError" xml:space="preserve">
    <value>You cannot request money from your self.</value>
  </data>
  <data name="RequestMoreThanError" xml:space="preserve">
    <value>You cannot request more than {0} in a single transaction.</value>
    <comment>{0}</comment>
  </data>
  <data name="SenViewModelYourSelfError" xml:space="preserve">
    <value>You cannot send the money to your self.</value>
  </data>
  <data name="SmartMoneyCPRExpired" xml:space="preserve">
    <value>Your ID Card has expired, please submit the new in order to complete the registration process.</value>
  </data>
  <data name="SmartMoneyNotRegistered" xml:space="preserve">
    <value>You are not registered in smart money service</value>
  </data>
  <data name="SmartMoneyRegisteration" xml:space="preserve">
    <value>Smart Money Registeration</value>
  </data>
  <data name="Statement" xml:space="preserve">
    <value>Statement</value>
  </data>
  <data name="StmtBillingAmount" xml:space="preserve">
    <value>Billing Amount</value>
  </data>
  <data name="StmtChooseStatement" xml:space="preserve">
    <value>Please choose a statement</value>
  </data>
  <data name="StmtClosingBalance" xml:space="preserve">
    <value>Closing Balance</value>
  </data>
  <data name="StmtCurrentMonthTransactions" xml:space="preserve">
    <value>Current Month Transactions</value>
  </data>
  <data name="StmtDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="StmtDueDate" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="StmtExportAs" xml:space="preserve">
    <value>Export as</value>
  </data>
  <data name="StmtMinimumPayment" xml:space="preserve">
    <value>Minimum Payment</value>
  </data>
  <data name="StmtPdfBtnText" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="StmtPostingDate" xml:space="preserve">
    <value>Posting Date</value>
  </data>
  <data name="StmtPreviousMonthsStatements" xml:space="preserve">
    <value>Previous Months Statements</value>
  </data>
  <data name="StmtTotalCredits" xml:space="preserve">
    <value>Total Credits</value>
  </data>
  <data name="StmtTotalDebit" xml:space="preserve">
    <value>Total Debit</value>
  </data>
  <data name="StmtTransactionAmount" xml:space="preserve">
    <value>Transaction Amount</value>
  </data>
  <data name="StmtTransactionDate" xml:space="preserve">
    <value>Transaction Date</value>
  </data>
  <data name="StmtWallet" xml:space="preserve">
    <value>Wallet</value>
  </data>
  <data name="StopCardFail" xml:space="preserve">
    <value>Failed to de-activate your card. Please try again later.</value>
  </data>
  <data name="StopCardSuccess" xml:space="preserve">
    <value>Your card has been de-activated. For replacement request, please contact BFC Payments customer service at 1771 1775.</value>
  </data>
  <data name="StopCardSuccessWithNumber" xml:space="preserve">
    <value>Your card {0} has been de-activated. For replacement request, please contact BFC Payments customer service at 1771 1775.</value>
  </data>
  <data name="SuccessSmartMoneyRegister" xml:space="preserve">
    <value>You have been registered to smart money successfully</value>
  </data>
  <data name="TransactionProcessedFail" xml:space="preserve">
    <value>Failed to send your transaction details! Please Try again later.</value>
  </data>
  <data name="TransactionProcessedSuccess" xml:space="preserve">
    <value>This request has been successfully completed</value>
  </data>
  <data name="TransactionRejected" xml:space="preserve">
    <value>Your transaction has been rejected</value>
  </data>
  <data name="TransferButton" xml:space="preserve">
    <value>Transfer</value>
  </data>
  <data name="TransferButtonLoading" xml:space="preserve">
    <value>Transferring</value>
  </data>
  <data name="TransferMoreThanError" xml:space="preserve">
    <value>You cannot transfer more than {0} in a single transaction.</value>
    <comment>{0}</comment>
  </data>
</root>