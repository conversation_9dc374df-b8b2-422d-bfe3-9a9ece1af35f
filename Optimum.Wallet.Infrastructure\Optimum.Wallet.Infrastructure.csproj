<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <OutputType>Library</OutputType>
     <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.17" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.0.2" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
    <PackageReference Include="System.ServiceModel.Federation" Version="6.0.0" />
    <PackageReference Include="System.ServiceModel.Http" Version="6.0.0" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="6.0.0" />
    <PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Optimum.Wallet.Core\Optimum.Wallet.Core.csproj" />
    <ProjectReference Include="..\Optimum.Wallet.Application\Optimum.Wallet.Application.csproj" />
    <ProjectReference Include="..\Optimum.Wallet.Domain\Optimum.Wallet.Domain.csproj" />
  </ItemGroup>

</Project>
