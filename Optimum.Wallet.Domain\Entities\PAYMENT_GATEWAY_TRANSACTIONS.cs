//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Optimum.Wallet.Domain.Entities
{
    using System;
    using System.Collections.Generic;
    
    public class PAYMENT_GATEWAY_TRANSACTIONS
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public PAYMENT_GATEWAY_TRANSACTIONS()
        {
            this.PAYMENT_GATEWAY_TN_DETAILS = new HashSet<PAYMENT_GATEWAY_TN_DETAILS>();
            this.PAYMENT_GATEWAY_TRAN_RESPONSE = new HashSet<PAYMENT_GATEWAY_TRAN_RESPONSE>();
        }
    
        public int ID { get; set; }
        public string QueryString { get; set; }
        public Nullable<System.DateTime> PG_RES_TIME { get; set; }
        public string PG_TRNO { get; set; }
        public string PG_RRNO { get; set; }
        public string PG_TRCODE { get; set; }
        public string PG_AUTHID { get; set; }
        public int PG_TYPE { get; set; }
        public Nullable<decimal> PG_Amount { get; set; }
        public string PG_User { get; set; }
        public bool Approved { get; set; }
        public bool Declined { get; set; }
        public bool ReceiptProcessed { get; set; }
        public bool EmailSend { get; set; }
        public string PG_PAYMENTID { get; set; }
        public string PG_REQ_QUERYSTRING { get; set; }
        public string PG_RAW_RESPONSE { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PAYMENT_GATEWAY_TN_DETAILS> PAYMENT_GATEWAY_TN_DETAILS { get; set; }
        public virtual PAYMENT_GATEWAY_TYPES PAYMENT_GATEWAY_TYPES { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PAYMENT_GATEWAY_TRAN_RESPONSE> PAYMENT_GATEWAY_TRAN_RESPONSE { get; set; }
        public virtual PAYMENT_GATEWAY_TRANSACTIONS PAYMENT_GATEWAY_TRANSACTIONS1 { get; set; }
        public virtual PAYMENT_GATEWAY_TRANSACTIONS PAYMENT_GATEWAY_TRANSACTIONS2 { get; set; }
    }
}
