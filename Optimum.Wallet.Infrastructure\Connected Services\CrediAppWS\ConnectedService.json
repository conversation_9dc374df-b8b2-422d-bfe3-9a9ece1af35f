{"ExtendedData": {"inputs": ["http://localhost/CrediAppService.wsdl"], "collectionTypes": ["System.Array", "System.Collections.Generic.Dictionary`2"], "namespaceMappings": ["*, Optimum.Wallet.Api.CrediAppWS"], "references": ["<PERSON>Mapper, {AutoMapper, 12.0.1}", "AutoMapper.Extensions.Microsoft.DependencyInjection, {AutoMapper.Extensions.Microsoft.DependencyInjection, 12.0.0}", "Azure.Core, {Azure.Core, 1.24.0}", "Azure.Identity, {Azure.Identity, 1.6.0}", "D:\\OnlineGitHub\\Innopact\\Mallats\\LatestMallats\\Mallats-Wallet\\Mallats-Wallet\\bin\\Debug\\net7.0\\Optimum.Wallet.Core.dll", "<PERSON><PERSON>, {<PERSON><PERSON>, 2.0.123}", "Microsoft.AspNetCore.Authentication.Abstractions, {Microsoft.AspNetCore.Authentication.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Authentication.Core, {Microsoft.AspNetCore.Authentication.Core, 2.2.0}", "Microsoft.AspNetCore.Authentication.JwtBearer, {Microsoft.AspNetCore.Authentication.JwtBearer, 7.0.3}", "Microsoft.AspNetCore.Authorization, {Microsoft.AspNetCore.Authorization, 2.2.0}", "Microsoft.AspNetCore.Authorization.Policy, {Microsoft.AspNetCore.Authorization.Policy, 2.2.0}", "Microsoft.AspNetCore.Hosting.Abstractions, {Microsoft.AspNetCore.Hosting.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Hosting.Server.Abstractions, {Microsoft.AspNetCore.Hosting.Server.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Http, {Microsoft.AspNetCore.Http, 2.2.0}", "Microsoft.AspNetCore.Http.Abstractions, {Microsoft.AspNetCore.Http.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Http.Extensions, {Microsoft.AspNetCore.Http.Extensions, 2.2.0}", "Microsoft.AspNetCore.Http.Features, {Microsoft.AspNetCore.Http.Features, 5.0.17}", "Microsoft.AspNetCore.JsonPatch, {Microsoft.AspNetCore.JsonPatch, 7.0.3}", "Microsoft.AspNetCore.Mvc.Abstractions, {Microsoft.AspNetCore.Mvc.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Mvc.ApiExplorer, {Microsoft.AspNetCore.Mvc.ApiExplorer, 2.2.0}", "Microsoft.AspNetCore.Mvc.Core, {Microsoft.AspNetCore.Mvc.Core, 2.2.5}", "Microsoft.AspNetCore.Mvc.NewtonsoftJson, {Microsoft.AspNetCore.Mvc.NewtonsoftJson, 7.0.3}", "Microsoft.AspNetCore.OpenApi, {Microsoft.AspNetCore.OpenApi, 7.0.3}", "Microsoft.AspNetCore.ResponseCaching.Abstractions, {Microsoft.AspNetCore.ResponseCaching.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Routing, {Microsoft.AspNetCore.Routing, 2.2.0}", "Microsoft.AspNetCore.Routing.Abstractions, {Microsoft.AspNetCore.Routing.Abstractions, 2.2.0}", "Microsoft.AspNetCore.WebUtilities, {Microsoft.AspNetCore.WebUtilities, 2.2.0}", "Microsoft.Bcl.AsyncInterfaces, {Microsoft.Bcl.AsyncInterfaces, 5.0.0}", "Microsoft.Data.SqlClient, {Microsoft.Data.SqlClient, 5.0.1}", "Microsoft.EntityFrameworkCore, {Microsoft.EntityFrameworkCore, 7.0.3}", "Microsoft.EntityFrameworkCore.Abstractions, {Microsoft.EntityFrameworkCore.Abstractions, 7.0.3}", "Microsoft.EntityFrameworkCore.Relational, {Microsoft.EntityFrameworkCore.Relational, 7.0.3}", "Microsoft.EntityFrameworkCore.SqlServer, {Microsoft.EntityFrameworkCore.SqlServer, 7.0.3}", "Microsoft.Extensions.Caching.Abstractions, {Microsoft.Extensions.Caching.Abstractions, 7.0.0}", "Microsoft.Extensions.Caching.Memory, {Microsoft.Extensions.Caching.Memory, 7.0.0}", "Microsoft.Extensions.Configuration, {Microsoft.Extensions.Configuration, 2.0.0}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 7.0.0}", "Microsoft.Extensions.Configuration.Binder, {Microsoft.Extensions.Configuration.Binder, 2.0.0}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 7.0.0}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 7.0.0}", "Microsoft.Extensions.DependencyModel, {Microsoft.Extensions.DependencyModel, 7.0.0}", "Microsoft.Extensions.FileProviders.Abstractions, {Microsoft.Extensions.FileProviders.Abstractions, 3.1.8}", "Microsoft.Extensions.Hosting.Abstractions, {Microsoft.Extensions.Hosting.Abstractions, 3.1.8}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 7.0.0}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 7.0.0}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 5.0.10}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 7.0.0}", "Microsoft.Extensions.Options.ConfigurationExtensions, {Microsoft.Extensions.Options.ConfigurationExtensions, 2.0.0}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 7.0.0}", "Microsoft.Identity.Client, {Microsoft.Identity.Client, 4.45.0}", "Microsoft.Identity.Client.Extensions.Msal, {Microsoft.Identity.Client.Extensions.Msal, 2.19.3}", "Microsoft.IdentityModel.Abstractions, {Microsoft.IdentityModel.Abstractions, 6.21.0}", "Microsoft.IdentityModel.JsonWebTokens, {Microsoft.IdentityModel.JsonWebTokens, 6.21.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 6.21.0}", "Microsoft.IdentityModel.Protocols, {Microsoft.IdentityModel.Protocols, 6.21.0}", "Microsoft.IdentityModel.Protocols.OpenIdConnect, {Microsoft.IdentityModel.Protocols.OpenIdConnect, 6.21.0}", "Microsoft.IdentityModel.Protocols.WsTrust, {Microsoft.IdentityModel.Protocols.WsTrust, 6.8.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 6.21.0}", "Microsoft.IdentityModel.Tokens.Saml, {Microsoft.IdentityModel.Tokens.Saml, 6.8.0}", "Microsoft.IdentityModel.Xml, {Microsoft.IdentityModel.Xml, 6.8.0}", "Microsoft.Net.Http.Headers, {Microsoft.Net.Http.Headers, 2.2.0}", "Microsoft.OpenApi, {Microsoft.OpenApi, 1.4.3}", "Microsoft.SqlServer.Server, {Microsoft.SqlServer.Server, 1.0.0}", "Newtonsoft.J<PERSON>, {Newtonsoft<PERSON>J<PERSON>, 13.0.2}", "Newtonsoft.Json.<PERSON>, {Newtonsoft.Json.Bson, 1.0.2}", "<PERSON><PERSON><PERSON><PERSON>, {Q<PERSON><PERSON>r, 1.4.2}", "<PERSON><PERSON><PERSON>, {Serilog, 2.10.0}", "Serilog.AspNetCore, {Serilog.AspNetCore, 6.1.0}", "Serilog.Enrichers.Environment, {Serilog.Enrichers.Environment, 2.2.0}", "Serilog.Enrichers.Thread, {Serilog.Enrichers.Thread, 3.1.0}", "Serilog.Extensions.Hosting, {Serilog.Extensions.Hosting, 5.0.1}", "Serilog.Extensions.Logging, {Serilog.Extensions.Logging, 3.1.0}", "Serilog.Formatting.Compact, {Serilog.Formatting.Compact, 1.1.0}", "Serilog.Settings.Configuration, {Serilog.Settings.Configuration, 3.3.0}", "Serilog.<PERSON><PERSON>.<PERSON>, {Serilog.Sinks.Console, 4.0.1}", "Serilog.Sinks.Debug, {Serilog.Sinks.Debug, 2.0.0}", "Serilog.Sinks.File, {Serilog.Sinks.File, 5.0.0}", "StandardizedQR, {StandardizedQR, 1.0.21}", "Swashbuckle.AspNetCore.Swagger, {Swashbuckle.AspNetCore.Swagger, 6.5.0}", "Swashbuckle.AspNetCore.SwaggerGen, {Swashbuckle.AspNetCore.SwaggerGen, 6.5.0}", "Swashbuckle.AspNetCore.SwaggerUI, {Swashbuckle.AspNetCore.SwaggerUI, 6.5.0}", "System.Diagnostics.DiagnosticSource, {System.Diagnostics.DiagnosticSource, 5.0.0}", "System.Drawing.Common, {System.Drawing.Common, 5.0.0}", "System.Globalization, {System.Globalization, 4.3.0}", "System.IdentityModel.Tokens.Jwt, {System.IdentityModel.Tokens.Jwt, 6.21.0}", "System.IO, {System.IO, 4.3.0}", "System.IO.Pipelines, {System.IO.Pipelines, 5.0.2}", "System.Memory.Data, {System.Memory.Data, 1.0.2}", "System.Reflection, {System.Reflection, 4.3.0}", "System.Reflection.DispatchProxy, {System.Reflection.DispatchProxy, 4.7.1}", "System.Reflection.Primitives, {System.Reflection.Primitives, 4.3.0}", "System.Resources.ResourceManager, {System.Resources.ResourceManager, 4.3.0}", "System.Runtime, {System.Runtime, 4.3.0}", "System.Security.AccessControl, {System.Security.AccessControl, 5.0.0}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 5.0.0}", "System.Security.Cryptography.ProtectedData, {System.Security.Cryptography.ProtectedData, 5.0.0}", "System.Security.Cryptography.Xml, {System.Security.Cryptography.Xml, 5.0.0}", "System.Security.Permissions, {System.Security.Permissions, 5.0.0}", "System.Security.Principal.Windows, {System.Security.Principal.Windows, 5.0.0}", "System.ServiceModel, {System.ServiceModel.Primitives, 4.10.0}", "System.ServiceModel.Duplex, {System.ServiceModel.Duplex, 4.10.0}", "System.ServiceModel.Federation, {System.ServiceModel.Federation, 4.10.0}", "System.ServiceModel.Http, {System.ServiceModel.Http, 4.10.0}", "System.ServiceModel.NetTcp, {System.ServiceModel.NetTcp, 4.10.0}", "System.ServiceModel.Primitives, {System.ServiceModel.Primitives, 4.10.0}", "System.ServiceModel.Security, {System.ServiceModel.Security, 4.10.0}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encodings.Web, {System.Text.Encodings.Web, 7.0.0}", "System.<PERSON>.<PERSON>, {System.Text.<PERSON>, 7.0.0}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "System.Windows.Extensions, {System.Windows.Extensions, 5.0.0}", "System.Xml.ReaderWriter, {System.Xml.ReaderWriter, 4.3.0}", "System.Xml.XmlDocument, {System.Xml.XmlDocument, 4.3.0}"], "targetFramework": "net7.0", "typeReuseMode": "All"}}