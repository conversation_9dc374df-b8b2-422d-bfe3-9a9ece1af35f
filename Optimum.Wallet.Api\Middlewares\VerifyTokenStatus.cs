using Microsoft.EntityFrameworkCore;
using Optimum.Wallet.Infrastructure.Data.Context;
using Optimum.Wallet.Application.Common.Models;

namespace Optimum.Wallet.Api.Middlewares
{
    public class VerifyTokenStatus
    {
        private readonly RequestDelegate _next;

        public VerifyTokenStatus(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext httpContext)
        {
            if (httpContext.Request.Headers.Authorization.Any() 
                && httpContext.Request.Headers.Authorization.FirstOrDefault().StartsWith("Bearer ",StringComparison.OrdinalIgnoreCase))
            {
                var dbcontext = httpContext.RequestServices.GetRequiredService<ApplicationDbContext>();
                var logger = httpContext.RequestServices.GetRequiredService<ILogger<VerifyTokenStatus>>();
                var token = httpContext.Request.Headers.Authorization.FirstOrDefault().Replace("Bearer ", "");
                logger.LogInformation($"Verifying the status of token {token} from IP {httpContext.Connection.RemoteIpAddress.MapToIPv4()}");
                var data = dbcontext.TokenRequests.Where(a => a.Token == token).AsNoTracking().FirstOrDefault();
                if(data == null || data.revoked)
                {
                    logger.LogInformation($"Token {token} is either revoked or not found");
                    httpContext.Response.StatusCode = 401;
                    await httpContext.Response.WriteAsJsonAsync(BaseResponse<string>.Failure("Invalid or expired token"));
                    return;
                }
                await dbcontext.DisposeAsync();
                logger.LogInformation($"Verified token {token} is issue to {data.UName} {data.ContactID} {data.RefreshToken}");
            }
            await _next(httpContext);
        }
    }

    public static class VerifyTokenStatusExtensions
    {
        public static IApplicationBuilder UseVerifyTokenStatus(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<VerifyTokenStatus>();
        }
    }
}
