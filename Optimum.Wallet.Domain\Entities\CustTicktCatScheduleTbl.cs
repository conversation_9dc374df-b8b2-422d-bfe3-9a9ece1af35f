using System;
using System.Collections.Generic;

namespace Optimum.Wallet.Domain.Entities
{
    public class CustTicktCatScheduleTbl
    {
        public int Ctsid { get; set; }
        public string TicketLevel { get; set; }
        public string Leveluser { get; set; }
        public string ActionTime { get; set; }
        public string ActionSubject { get; set; }
        public string ActionDescription { get; set; }
        public int? MapId { get; set; }
        public string UserModified { get; set; }
        public DateTime? DateModfied { get; set; }
        public int TypeId { get; set; }
        public int? CustomerRecId { get; set; }
        public int? ActivityId { get; set; }
        public int? AnalysisId { get; set; }
        public int? DepartmentId { get; set; }
    }
}
