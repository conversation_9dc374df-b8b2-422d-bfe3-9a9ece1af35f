using Microsoft.Extensions.DependencyInjection;
using AutoMapper;
using Optimum.Wallet.Application.Mappings;

namespace Optimum.Wallet.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // Add AutoMapper configuration
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ModelToViewMapping());
        });
        
        IMapper mapper = mapperConfig.CreateMapper();
        services.AddSingleton(mapper);
        
        // Add other application services here
        
        return services;
    }
}
