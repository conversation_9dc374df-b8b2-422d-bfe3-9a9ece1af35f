using System;
using System.Collections.Generic;

namespace Optimum.Wallet.Domain.Entities
{
    public class ChooseListPageTbl
    {
        public int Clpid { get; set; }
        public int? PageId { get; set; }
        public int? IType { get; set; }
        public int? ISubType { get; set; }
        public int? CLCatId { get; set; }
        public int? IWaqfType { get; set; }
        public int? SortCode { get; set; }
        public bool? Active { get; set; }
        public string PreSetValue { get; set; }
        public bool? Publish { get; set; }
        public bool? Editable { get; set; }
        public bool InternalUse { get; set; }
        public bool? ExternalEditable { get; set; }
        public bool? ExternalPublish { get; set; }
        public int? Parent { get; set; }
        public int? Col { get; set; }
        public int? Oid { get; set; }
        public string Description { get; set; }
        public bool? IsCloseCollapse { get; set; }
        public int? BatchSort { get; set; }
    }
}
