﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Optimum.Wallet.Infrastructure.Data.Context;
using Optimum.Wallet.Infrastructure.Repository;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Core.Interfaces;

namespace Optimum.Wallet.Infrastructure.Data
{
    public static class ServiceRegistration
    {
        public static void AddDbContext(this IServiceCollection services,IConfiguration configuration)
        {
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseSqlServer(Config.GetConnectionString("Utilities"))
                .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            });
            services.AddTransient(typeof(IGenericRepositoryAsync<>), typeof(GenericRepositoryAsync<>));
            services.AddTransient<ITokenRequestAsync,TokenRequestAsync>();
        }
    }
}
