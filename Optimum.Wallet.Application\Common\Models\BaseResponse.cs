using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Optimum.Wallet.Application.Common.Models
{
    public class BaseResponse<T>
    {
        public BaseResponse(bool succeeded, IEnumerable<string> errors, T data)
        {
            Succeeded = succeeded;
            Messages = errors.ToArray();
            Data = data;
        }

        public bool Succeeded { get; set; }
        public string[] Messages { get; set; }
        public T Data { get; set; }
        public DateTime TimeStamp => DateTime.Now;
        public string responseid { get; set; } = Guid.NewGuid().ToString();

        public static BaseResponse<T> Success(string message, T data)
        {
            return new BaseResponse<T>(true, new string[] { message }, data);
        }
        public static BaseResponse<T> Success(T data)
        {
            return new BaseResponse<T>(true, new string[] { "Success" }, data);
        }
        public static BaseResponse<T> Success()
        {
            return new BaseResponse<T>(true, new string[] { "Success" }, default);
        }

        public static BaseResponse<T> Failure(IEnumerable<string> errors)
        {
            return new BaseResponse<T>(false, errors, default);
        }
        public static BaseResponse<T> Failure(string errors)
        {
            return new BaseResponse<T>(false, new string[] { errors }, default);
        }
        public static BaseResponse<T> Failure()
        {
            return new BaseResponse<T>(false, new string[] { "Error Occured" }, default);
        }
        public static BaseResponse<T> Failure(T Data)
        {
            return new BaseResponse<T>(false, new string[] { "Error Occured" }, Data);
        }

    }
}
