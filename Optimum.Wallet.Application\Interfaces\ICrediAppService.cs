using Optimum.Wallet.Application.Common.Models;
using System.Threading.Tasks;

namespace Optimum.Wallet.Application.Interfaces
{
    /// <summary>
    /// Interface for the CrediApp web service client
    /// This follows the Dependency Inversion Principle by defining the interface in the Core layer
    /// </summary>
    public interface ICrediAppService
    {
        Task<List<CardDetailsViewModel>> F4_GetCardListAsync(string pCpr);
        Task<CardBalance> F5_GetBalanceAsync(string pCpr, string pCardNumber, string mainCardBalanceJson);
        Task<Dictionary<string, string>> F6_GetStatementListAsync(string pCpr, string pCardNumber);
        Task<List<CardStatementModelView>> F8_GetCurrentStatementTransactionAsync(string pCpr, string pCardNumber);
        Task<List<CardStatementModelView>> GetStatementTransactionAsync(string pCpr, string pCardNumber, string type, string statementDate, string suppCardsJson, string onlySupp);
        Task<Dictionary<string, string>> F10_StopCard_RawAsync(string pCpr, string pCardNumber);
        Task<Dictionary<string, string>> F13_PayMyCardPayment_RawAsync(string pCpr, string pCardNumber, string pAmount, string pCurrency);
        Task<Dictionary<string, string>> External_Transfer_Funds_RawAsync(string pCpr, string pCardNumber, string pAmount, string pCurrency, string pBeneficiary);
        Task<Dictionary<string, string>> F16A_ActivatEstatement_RawAsync(string pCpr, string pCardNumber, string pEmail);
        Task<Dictionary<string, string>> F18_UpdateMobileNumber_RawAsync(string pCpr, string pCardNumber, string pMobile);
        Task<Dictionary<string, string>> F19_UpdateEmail_RawAsync(string pCpr, string pCardNumber, string pEmail);
        Task<PersonalData> F25_GetPersonalData_RawAsync(string pCpr);
        Task<VerifyUser> F27_CardHolderVerification_RawAsync(string _sCardEmbossedName, string _sCardLast4Digit, string pCpr, string pMobile, string valdationFlag);
        Task<string> F29_GetWalletsBalancesAsync(string pCpr, string pCardNumber, string mainCardBalanceJson);
        Task<string> F30_VerifyTransferAmountAsync(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency);
        Task<string> F31_ConfirmTransferAmountAsync(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency);
        Task<string> GetWalletStatementTransactionAsync(string pCpr, string pCardNumber, string type, string statementDate, string Currency);
        Task<string> F23_LoyFppProgAsync(string pCpr);
        Task<string> F24_LoyThameenAsync(string pCpr);
        Task<string> RunWebServiceAsync(string functionName, string parameters);
    }
}
