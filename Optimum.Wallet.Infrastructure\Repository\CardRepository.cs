using Microsoft.AspNetCore.DataProtection;
using Microsoft.Extensions.Logging;
using Optimum.Wallet.Application.Common;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Extensions;
using Optimum.Wallet.Application.Interfaces.Repositories;
using Serilog;
using System.Globalization;

namespace Optimum.Wallet.Infrastructure.Repository
{
    public class CardRepository : ICardRepository
    {
        public int _decimals = -1;
        public string _currency = "";

        ILogger<CardRepository> _logger;
        private readonly IDataProtectionProvider _dataProtection;
        private readonly IWalletRepository _walletRepository;

        public CardRepository(ILogger<CardRepository> logger, IDataProtectionProvider dataProtection, IWalletRepository walletRepository           )
        {
            _logger = logger;
            _dataProtection = dataProtection;
            Misc._rootProvider = _dataProtection.CreateProtector(GetType().FullName);
            _walletRepository = walletRepository;
        }

        public async Task<IEnumerable<WalletCurrency>> GetCardCurrenciesAsync(int ContactID)
        {
            return await DapperHelper.QueryAsync<WalletCurrency>($@"
                SELECT MIN(CF.RecID) AS Id, MIN(CF.CcyName) AS [Name], CF.SwiftCode AS [Code], MIN(CF.Scan) Image, CASE WHEN MIN(CA.CAID) IS NULL THEN 0 ELSE 1 END AS Active, CASE WHEN MIN(CF.Rate) = 1 THEN 1 ELSE 0 END AS IsDefault
                FROM dbo.CustAcctSetupTbl CAS 
                INNER JOIN {DapperHelper.LedgerDbName}BudgetCenter BC ON BC.BudgetSeg = CAS.BranchID
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON CAS.ARAccountNo = AF.AccountNo
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy AND CF.BranchID = CAS.BranchID
                LEFT JOIN {DapperHelper.UtilitiesDbName}CustomerAccountTbl CA ON CA.RefContactId = @ContactID AND CA.CustActId = CAS.CustActId
                WHERE CAS.Active = 1 AND CAS.CustAcctType IN (3,4)
                AND BC.Active = 1 AND BC.Cancel = 0 
                AND AF.Active = 1 AND AF.Cancel = 0 
                AND CF.Active = 1 AND CF.Cancel = 0
                AND BC.BudgetSeg = @BranchID
                GROUP BY CF.SwiftCode, CF.CcyName
                ORDER BY CF.CcyName", new
            {
                BranchID = WalletApplication.BranchId,
                ContactID = ContactID
            });
        }

        public async Task<IEnumerable<WalletAccountType>> GetCardAccountTypesAsync(char type = 'C')
        {
            return await DapperHelper.QueryAsync<WalletAccountType>($@"
                SELECT ID AS Id, SubTypeNameE AS Name, [Prefix] AS [Code], SortCode AS SortCode, MultiCurrency AS MultiCurrency, Active AS Active FROM dbo.CustomerAccountSubTypeTbl WHERE Active = 1 AND [Type] = '{type}' AND ID NOT IN ({CustomerAccountSubType.SAVINGS}) ORDER BY SortCode
            ");
        }

        public async Task<IEnumerable<T>> GetCards<T>(int contactId,
            int customerId, int cardId = -1, bool isSupplementary = false, string mainCardNumber = "",
            bool includeExpired = false, bool updateBalances = false, string customerCpr = "") where T : new()
        {
            var cardsQuery = await DapperHelper.QueryAsync<dynamic>($@"
                SELECT CA.CAID AS [CardId], CA.CustAcCode AS [CardNumber], CA.CustAcNameE AS [CardHolderName], ISNULL(NULLIF(CA.PanNo, '0'), 'MasterCard') AS CardType, ISNULL(CA.Tax_No, 'Mallats') AS CardClass, ISNULL(CA.StatementBalance, 0) AS [Balance], FORMAT(CA.CACRExpDate, 'MM/yy') AS [CardExpiry], CT.Cont_CPR AS [CardHolderCpr], CONVERT(VARCHAR(10), CA.StatusID) AS [Status], CONVERT(VARCHAR(10), CAS.CustAcctType) AS [CardAccountTypeId], CF.[Decimal] AS [NoOfDecimals], CF.SwiftCode AS [SwiftCode]
                ,CRLimit 'CreditLimit' ,CashLimit               
                FROM dbo.CustomerAccountTbl CA
                INNER JOIN dbo.CustAcctSetupTbl CAS ON CAS.CustActId = CA.CustActId --AND CAS.Default_ = 1
                INNER JOIN dbo.ContactTbl CT ON CT.ContactID = CA.RefContactID
                INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON AF.AccountNo = CA.CAAccountNo
                INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy
                WHERE CT.ContactId = @ContactId AND ISNULL(CA.PayType, 0) = 2 AND CAS.CustAcctType IN (3,4)
            ",
            new
            {
                ContactId = contactId
            });
            var cards = cardsQuery.ToList();

            if (cards == null || isSupplementary)
            {
                return new List<T>();
            }

            var cardsList = new List<T>();
            foreach (var tmp in cards)
            {
                var expiryDate = DateTime.MinValue;
                DateTime.TryParseExact(tmp.CardExpiry, "MM/yy", CultureInfo.InvariantCulture, DateTimeStyles.None, out expiryDate);
                var todayDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day);

                dynamic dummyCard = new T();
                dummyCard.CardHolderName = tmp.CardHolderName;
                dummyCard.CardNumber = ((string)tmp.CardNumber).FormatCardNumber();
                dummyCard.CardExpiry = tmp.CardExpiry;
                dummyCard.CardId = (int)tmp.CardId;
                dummyCard.CardAccountTypeId = (string)tmp.CardAccountTypeId;
                dummyCard.CardHolderCpr = tmp.CardHolderCpr;
                dummyCard.CardType = tmp.CardType;
                dummyCard.CardTypeId = ProductValues.GetValueByName(tmp.CardType);
                dummyCard.CardClass = tmp.CardClass;
                dummyCard.CardClassId = CategoryValues.GetValueByName(tmp.CardClass);
                dummyCard.ParentCardId = (int)(tmp.ParentCardId ?? 0);
                dummyCard.StatusId = tmp.Status == "1" && expiryDate.AddMonths(1) <= todayDate ? StatusValues.EXPIRED : StatusValues.ACTIVE;
                dummyCard.CardType = tmp.CardType;
                dummyCard.Balance = new CardBalance() { AvailableBalance = (decimal)tmp.Balance, CashLimit = (decimal)tmp.CashLimit, CreditLimit = (decimal)tmp.CreditLimit };
                dummyCard.EncryptedCAID = ((string)(tmp.CardId + "")).Encrypt(General.CAIDEncryptPurpose);
                dummyCard.HashedCAID = ((string)(tmp.CardId + "")).HashMD5();
                dummyCard.SwiftCode = tmp.SwiftCode;
                dummyCard.NoOfDecimals = (int)(tmp.NoOfDecimals ?? 0);
                if (isSupplementary)
                {
                    dummyCard.SuppCardNumber = (string)dummyCard.CardNumber;
                }

                cardsList.Add(dummyCard);
            }

            return cardsList;
        }

        public async Task<List<WalletAccount>> GetWalletAccounts(int contactId, int CAID = -1, int type = 1, int PaySubType = -1)
        {
           return await _walletRepository.GetWalletAccounts(contactId, CAID, type, PaySubType);
        }

        public async Task<bool> doesUserOwnCaid(AppUser appUser, int CAID)
        {
            var walletCaids = await GetWalletAccounts(appUser.ContactID);
            bool hasWalletCaid = walletCaids.Where(c => c.CAID == CAID).Any();
            var cardCaids = await GetCards<CardDetailsViewModel>(appUser.ContactID, appUser.CustomerID, -1, false, "", true, true, appUser.Username);
            bool hasCardCaid = cardCaids.Where(c => int.Parse(c.EncryptedCAID.Decrypt(General.CAIDEncryptPurpose)) == CAID).Any();
            var merchantWalletCaids = await GetWalletAccounts(appUser.ContactID, -1, 2);
            var hasMerchantWalletCaid = merchantWalletCaids.Where(c => c.CAID == CAID).Any();

            List<CardDetailsViewModel> childCardCaids = new List<CardDetailsViewModel>() { };

            if (!appUser.IsChild)
            {
                var childAccountsDetails = await _walletRepository.GetChildAccountsDetails(appUser.ContactID);

                if (childAccountsDetails != null)
                {
                    foreach (var childAccount in childAccountsDetails)
                    {
                        var childCardTask = await GetCards<CardDetailsViewModel>(childAccount.ContactID, appUser.CustomerID, -1, false, "", true, true, childAccount.CONT_CPR);

                        foreach (var childCard in childCardTask)
                        {
                            childCardCaids.Add(childCard);
                        }
                    }
                }
            }

            bool hasChildCardCaid = childCardCaids.Where(c => int.Parse(c.EncryptedCAID.Decrypt(General.CAIDEncryptPurpose)) == CAID).Any();

            return hasWalletCaid || hasCardCaid || hasMerchantWalletCaid || hasChildCardCaid;
        }

        public async Task<T> GetCardById<T>(int contactId,
            int customerId, int cardId, bool isSupplementary = false, string mainCardNumber = "",
            bool includeExpired = false, bool updateBalances = false, string customerCpr = "") where T : new()
        {
            var cardsList = await GetCards<T>(contactId, customerId, cardId, isSupplementary, mainCardNumber, includeExpired, updateBalances, customerCpr);

            // Reset the card data (PCI Requirement)
            cardId = 0; mainCardNumber = "";

            return cardsList.FirstOrDefault();
        }

        public async Task<List<WalletAccount>> GetCurrencyWallets(int RefContactID, int MainCAID)
        {
            return await _walletRepository.GetCurrencyWallets(RefContactID, MainCAID);
        }
    }
}