﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="allAreas" xml:space="preserve">
    <value>All Areas</value>
  </data>
  <data name="allCategories" xml:space="preserve">
    <value>All Categories</value>
  </data>
  <data name="allTypes" xml:space="preserve">
    <value>All Types</value>
  </data>
  <data name="AqaraatsMarketPlace" xml:space="preserve">
    <value>Aqaraats MarketPlace</value>
  </data>
  <data name="Bakery" xml:space="preserve">
    <value>Bakery</value>
  </data>
  <data name="Cafeteria" xml:space="preserve">
    <value>Cafeteria</value>
  </data>
  <data name="CarWash" xml:space="preserve">
    <value>Car Wash</value>
  </data>
  <data name="CoffeeShop" xml:space="preserve">
    <value>Coffee Shop</value>
  </data>
  <data name="ComingSoon" xml:space="preserve">
    <value>Coming Soon</value>
  </data>
  <data name="Delivering" xml:space="preserve">
    <value>Delivering</value>
  </data>
  <data name="electricityInc" xml:space="preserve">
    <value>Electricity Incl/excl</value>
  </data>
  <data name="findproperty" xml:space="preserve">
    <value>find a property</value>
  </data>
  <data name="findpropertydesc" xml:space="preserve">
    <value>Find properties with your favourite features</value>
  </data>
  <data name="FindYourProperty" xml:space="preserve">
    <value>Find Your Property</value>
  </data>
  <data name="Food" xml:space="preserve">
    <value>Food</value>
  </data>
  <data name="furnicherOp" xml:space="preserve">
    <value>Furniture Options</value>
  </data>
  <data name="Gas" xml:space="preserve">
    <value>Gas</value>
  </data>
  <data name="Groceries" xml:space="preserve">
    <value>Groceries</value>
  </data>
  <data name="HouseKeeping" xml:space="preserve">
    <value>House Keeping</value>
  </data>
  <data name="includeUnavailable" xml:space="preserve">
    <value>Include Unavailable Properties</value>
  </data>
  <data name="Investment" xml:space="preserve">
    <value>Investment</value>
  </data>
  <data name="keyword" xml:space="preserve">
    <value>Keyword (e.g. 'office' )</value>
  </data>
  <data name="KhidmaatMarketPlace" xml:space="preserve">
    <value>Khidmaat MarketPlace</value>
  </data>
  <data name="Laundry" xml:space="preserve">
    <value>Laundry</value>
  </data>
  <data name="Maintenance" xml:space="preserve">
    <value>Maintenance</value>
  </data>
  <data name="MallatsMarketPlace" xml:space="preserve">
    <value>Mallats MarketPlace</value>
  </data>
  <data name="Malls" xml:space="preserve">
    <value>Malls</value>
  </data>
  <data name="MarketPlace" xml:space="preserve">
    <value>MarketPlace</value>
  </data>
  <data name="maxArea" xml:space="preserve">
    <value>Max Area (mt2)</value>
  </data>
  <data name="maxBathroom" xml:space="preserve">
    <value>Maximum Bathrooms</value>
  </data>
  <data name="maxBedroom" xml:space="preserve">
    <value>Maximum Bedrooms</value>
  </data>
  <data name="minArea" xml:space="preserve">
    <value>Min Area (mt2)</value>
  </data>
  <data name="minBathroom" xml:space="preserve">
    <value>Minimum Bathrooms</value>
  </data>
  <data name="minBedroom" xml:space="preserve">
    <value>Minimum Bedrooms</value>
  </data>
  <data name="MtaemMarketPlace" xml:space="preserve">
    <value>Mtaem MarketPlace</value>
  </data>
  <data name="OurAgencies" xml:space="preserve">
    <value>Our Agencies</value>
  </data>
  <data name="PendingOrders" xml:space="preserve">
    <value>Pending Orders</value>
  </data>
  <data name="Pharmacy" xml:space="preserve">
    <value>Pharmacy</value>
  </data>
  <data name="RealEstate" xml:space="preserve">
    <value>Real Estate</value>
  </data>
  <data name="RealEstate1" xml:space="preserve">
    <value>Real Estate</value>
  </data>
  <data name="Rent" xml:space="preserve">
    <value>Rent</value>
  </data>
  <data name="reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Restaurants" xml:space="preserve">
    <value>Restaurants</value>
  </data>
  <data name="Sale" xml:space="preserve">
    <value>Sale</value>
  </data>
  <data name="search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="selectMaxPrice" xml:space="preserve">
    <value>Select Max Price</value>
  </data>
  <data name="selectMinPrice" xml:space="preserve">
    <value>Select Min Price</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="Shopping" xml:space="preserve">
    <value>Shopping</value>
  </data>
  <data name="Shopping1" xml:space="preserve">
    <value>Shopping</value>
  </data>
  <data name="showLess" xml:space="preserve">
    <value>Hide search options</value>
  </data>
  <data name="showMore" xml:space="preserve">
    <value>Show more search options</value>
  </data>
  <data name="TopHighlight" xml:space="preserve">
    <value>Top Highlighs</value>
  </data>
  <data name="Trading" xml:space="preserve">
    <value>Trading</value>
  </data>
  <data name="ViewAll" xml:space="preserve">
    <value>View All</value>
  </data>
  <data name="Water" xml:space="preserve">
    <value>Water</value>
  </data>
</root>