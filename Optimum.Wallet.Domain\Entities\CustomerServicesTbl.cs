using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Optimum.Wallet.Domain.Entities
{
    public class CustomerServicesTbl
    {
        public int ID { get; set; }
        public int? ProductId { get; set; }
        public string Par1 { get; set; }
        public string Par2 { get; set; }
        public string Par3 { get; set; }
        public string Par4 { get; set; }
        public string Par5 { get; set; }
        public string Par6 { get; set; }
        public string Par7 { get; set; }
        public string Par8 { get; set; }
        public string UserModified { get; set; }
        public DateTime? DateModified { get; set; }
        public bool? Active { get; set; }
        public int? RecID { get; set; }
        public int? TypeId { get; set; }
        public int? ContactID { get; set; }
        public int? Caid { get; set; }
        public int? Crid { get; set; }
        public int? Posted { get; set; }
        public int? SeqNo { get; set; }
        public int? CustTicktId { get; set; }
        public int? FbatchId { get; set; }
        public bool? AccountPosted { get; set; }
        public bool? SalaryPosted { get; set; }
        public string RejectedBy { get; set; }
        public DateTime? RejectedOn { get; set; }
        public string Parent { get; set; }
        public int? Status { get; set; }
        public DateTime? StatusDate { get; set; }
        public string TaemployeeId { get; set; }
        public int? ConSubProfitSeg { get; set; }
        public int? DraftRecId { get; set; }
        public bool Iprocessed { get; set; }
        public string DocId { get; set; }
    }
}
