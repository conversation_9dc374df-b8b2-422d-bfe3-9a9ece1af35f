using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using System.Text;
using Microsoft.AspNetCore.Authentication.Cookies;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Configurations.Models;

namespace Optimum.Wallet.Api.Configurations
{
    public static class CustomServiceExtensions
    {
        public static T GetOptions<T>(this IServiceCollection services, string sectionName) where T : new()
        {
            using var serviceProvider = services.BuildServiceProvider();
            var configuration = serviceProvider.GetRequiredService<IConfiguration>();
            var section = configuration.GetSection(sectionName);
            var options = new T();
            section.Bind(options);
            return options;
        }

        public static void AddCustomeSwaggerservice(this IServiceCollection services)
        {
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen(option =>
            {
                option.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Name = "Authorization",
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                    In = ParameterLocation.Header,
                    Description = "JWT Authorization header using the Bearer scheme. \r\n\r\n Enter 'Bearer' [space] and then your token in the text input below.\r\n\r\nExample: \"Bearer XYZ\"",
                });
                option.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        new string[] {}
                    }
                });

                option.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "Optimum ERP API",
                    Version = "v20",
                    Description = "Optimum ERP API",
                    Contact = new OpenApiContact
                    {
                        Name = "Amthal Group W.L.L",
                        Url = new Uri("https://amthalgroup.com/")
                    },
                    License = new OpenApiLicense
                    {
                        Name = "MIT",
                    },
                });
            });
        }

        public static void AddCustomJWTservice(this IServiceCollection services,IConfiguration configuration)
        {
            services.Configure<JWTSettings>(configuration.GetSection("JWTSettings"));
            //This parameter is injected for the expired token validation service
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(configuration["JWTSettings:SecretKey"])),

                // Consider validating issuer & audience
                ValidateIssuer = true,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero,
                ValidateLifetime = true,
                ValidIssuer = configuration["JWTSettings:Issuer"]
            };

            services.AddSingleton(tokenValidationParameters);

            services.AddAuthentication(x =>
            {
                x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(x =>
            {
                x.RequireHttpsMetadata = true;
                x.SaveToken = true;
                x.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(configuration["JWTSettings:SecretKey"])),

                    // Consider validating issuer & audience
                    ValidateIssuer = true,
                    ValidateAudience = false,

                    ValidateLifetime = true,
                    ValidIssuer = configuration["JWTSettings:Issuer"]
                };
                x.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = c =>
                    {
                        c.NoResult();
                        c.Response.StatusCode = 500;
                        c.Response.ContentType = "text/plain";
                        return c.Response.WriteAsync(c.Exception.ToString());
                    },
                    OnChallenge = async (ctx) =>
                    {
                        ctx.HandleResponse();
                        if (ctx.AuthenticateFailure != null)
                        {
                            ctx.Response.StatusCode = StatusCodes.Status401Unauthorized;
                            ctx.Response.ContentType = "text/plain";
                            var result = JsonConvert.SerializeObject(BaseResponse<string>.Failure(new string[] { "You are not Authorized" }));
                            await ctx.Response.WriteAsync(result);
                        }
                        else
                        {
                            ctx.Response.StatusCode = StatusCodes.Status401Unauthorized;
                            ctx.Response.ContentType = "text/plain";
                            var result = JsonConvert.SerializeObject(BaseResponse<string>.Failure(new string[] { "You are not Authorized" }));
                            await ctx.Response.WriteAsync(result);
                        }
                    },
                    OnForbidden = context =>
                    {
                        context.Response.StatusCode = 403;
                        context.Response.ContentType = "application/json";
                        var result = JsonConvert.SerializeObject(BaseResponse<string>.Failure(new string[] { "You are not authorized to access this resource" }));
                        return context.Response.WriteAsync(result);
                    },
                };
            });
        }

        public static void AddCustomServiceExtensions(this IServiceCollection services)
        {

        }
    }
}
