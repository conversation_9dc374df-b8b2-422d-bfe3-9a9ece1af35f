﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33213.308
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Optimum.Wallet.Api", "Optimum.Wallet.Api\Optimum.Wallet.Api.csproj", "{6E444825-7130-406B-B854-CFC0F614B09F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Optimum.Wallet.Core", "Optimum.Wallet.Core\Optimum.Wallet.Core.csproj", "{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Optimum.Wallet.Domain", "Optimum.Wallet.Domain\Optimum.Wallet.Domain.csproj", "{069FB75A-EE90-4483-85C4-10FDFB85672D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Optimum.Wallet.Application", "Optimum.Wallet.Application\Optimum.Wallet.Application.csproj", "{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Optimum.Wallet.Infrastructure", "Optimum.Wallet.Infrastructure\Optimum.Wallet.Infrastructure.csproj", "{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6E444825-7130-406B-B854-CFC0F614B09F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Debug|x64.Build.0 = Debug|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Debug|x86.Build.0 = Debug|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Release|x64.ActiveCfg = Release|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Release|x64.Build.0 = Release|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Release|x86.ActiveCfg = Release|Any CPU
		{6E444825-7130-406B-B854-CFC0F614B09F}.Release|x86.Build.0 = Release|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Debug|x64.Build.0 = Debug|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Debug|x86.Build.0 = Debug|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Release|x64.ActiveCfg = Release|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Release|x64.Build.0 = Release|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Release|x86.ActiveCfg = Release|Any CPU
		{6AF68A21-E474-48E4-AEAE-75B18CFEA2A3}.Release|x86.Build.0 = Release|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Debug|x64.Build.0 = Debug|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Debug|x86.Build.0 = Debug|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Release|Any CPU.Build.0 = Release|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Release|x64.ActiveCfg = Release|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Release|x64.Build.0 = Release|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Release|x86.ActiveCfg = Release|Any CPU
		{069FB75A-EE90-4483-85C4-10FDFB85672D}.Release|x86.Build.0 = Release|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Debug|x64.Build.0 = Debug|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Debug|x86.Build.0 = Debug|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Release|Any CPU.Build.0 = Release|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Release|x64.ActiveCfg = Release|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Release|x64.Build.0 = Release|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Release|x86.ActiveCfg = Release|Any CPU
		{CE7081FB-55B0-790B-4CAF-B0CB3D1117F9}.Release|x86.Build.0 = Release|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Debug|x64.Build.0 = Debug|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Debug|x86.Build.0 = Debug|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Release|Any CPU.Build.0 = Release|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Release|x64.ActiveCfg = Release|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Release|x64.Build.0 = Release|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Release|x86.ActiveCfg = Release|Any CPU
		{5F1D5A60-BDB4-29B2-499F-3767CE2CA3CC}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5340B744-F100-4775-8400-B7FAC63AA5B6}
	EndGlobalSection
EndGlobal
