using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Optimum.Wallet.Core.Configurations.Models;
using Optimum.Wallet.Core.Extensions;
using Optimum.Wallet.Application.Common;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Domain.Entities;
using Optimum.Wallet.Application.Interfaces;
using Microsoft.AspNetCore.Http;
using Optimum.Wallet.Application.Common.Models;

namespace Optimum.Wallet.Application.Services
{
    public class AccountService : IAccountService
    {
        #region Class Variables

        private const int DerivedKeyLength = 24;
        #endregion

        private readonly ILogger<AccountService> _logger;
        private readonly JWTSettings _jWTSettings;

        public AccountService (ILogger<AccountService> logger, IOptions<JWTSettings> jWTSettings) 
        { 
            _logger = logger;
            _jWTSettings = jWTSettings.Value;
        }

        // Example values for now
        int maxInvalidPasswordAttempts = 3;

        public async Task<ContactTbl> GetContact(string Username,string MobileNumber="")
        {
            var sqlUser = "SELECT * FROM dbo.ContactTbl WHERE 0=0 AND ";
            if (WalletApplication.IsAllowLoginByCPR) { sqlUser += "Cont_CPR=@Cont_CPR"; }
            else if (WalletApplication.IsAllowLoginByMobile) { sqlUser += " AND ContactMobile=@ContactMobile"; }
            var user = await DapperHelper.QueryAsync<ContactTbl>(
                $"{sqlUser} ORDER BY [MobileLogin] DESC", new
                {
                    Cont_CPR = Username,
                    ContactMobile = MobileNumber
                });
            return user.FirstOrDefault();
        }

        public async Task<Tuple<bool, ContactTbl, string>> ValidateCustomUser(string username, string password, string message, bool isFingerprintLogin = false, string currentDeviceId = "", bool isMallatsLogin = false)
        {
            _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Entered");

            // Keep track of the results
            message = "Invalid login attempt.";
            ContactTbl contact = new ContactTbl();

            try
            {
                // Fetch the user including their security details
                _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Checking data in db...");
                var userQuery = await DapperHelper.QueryAsync<ContactLoginData>(
                    " SELECT CST.salt, CST.iteration_count, CT.*" +
                    " FROM dbo.ContactTbl CT" +
                    " INNER JOIN dbo.ContactSecurityTbl CST ON CST.ContactID = CT.ContactID" +
                    " WHERE (CT.Cont_CPR = @Cont_CPR OR CT.ContactMobile like ('%' + @Cont_CPR)) AND CT.[Login] = 1 AND CT.[Active] = 1",
                    new
                    {
                        Cont_CPR = username
                    }
                );

                if (Config.GetBooleanValue("IsMallatsApp"))
                {
                    userQuery = await DapperHelper.QueryAsync<ContactLoginData>(
                        " SELECT CST.salt, CST.iteration_count, CT.*" +
                        " FROM dbo.ContactTbl CT" +
                        " LEFT JOIN dbo.ContactSecurityTbl CST ON CST.ContactID = CT.ContactID" +
                        " WHERE CT.ContactEmail=@ContactEmail AND CT.[Login] = 1 AND CT.[Active] = 1",
                        new
                        {
                            ContactEmail = username
                        }
                    );
                }

                var user = userQuery.FirstOrDefault();
                _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Checked data in db...");

                // Check that the user exists 
                if (user == null)
                {
                    return new Tuple<bool, ContactTbl, string>(false, null, "IncorrectLoginDetails");
                }

                // Check if the account is locked or not
                _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Checking login lock: {user.LoginLock}");
                if (user.LoginLock)
                {
                    message = "Locked contact.";
                    return new Tuple<bool, ContactTbl, string>(false, null, "LoginLocked");
                }

                // Only check the password if the user isn't logging in using his fingerprint
                _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Checking fingerprint: {isFingerprintLogin}");
                if (!isFingerprintLogin)
                {
                    // The login attempt failed
                    var storedPassword = user.MobilePassword;

                    if (Config.GetBooleanValue("IsMallatsApp"))
                    {
                        storedPassword = user.Password;
                        password = password + user.Cont_CPR;
                    }
                    
                    _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Verifying password...");
                    if (!VerifyPassword(password, storedPassword, user.salt, user.iteration_count))
                    {
                        _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} password invalid");
                        // Increase the failed login attempts and lock the account if 
                        if (++user.LoginAttempt >= maxInvalidPasswordAttempts)
                        {
                            user.LoginLock = true;
                        }

                        _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Updating login attempts...");
                        // Return the result
                        await DapperHelper.ExceuteAsync(
                            "UPDATE dbo.ContactTbl SET LoginAttempt = @LoginAttempt, LoginLock = @LoginLock WHERE ContactID = @ContactID",
                            new
                            {
                                LoginAttempt = user.LoginAttempt,
                                LoginLock = user.LoginLock,
                                ContactID = user.ContactID
                            }
                        );
                        _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Updated login attempts...");
                        return new Tuple<bool, ContactTbl, string>(false, null, "IncorrectLoginDetails");
                    }
                }
                /* else if (user.FingerprintLock)
                {
                    // If the user tries to login with his fingerprint, check that his account allows it
                    _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Fingerprint login locked...");
                    message = "Fingerprint hash not setup.";
                    return new Tuple<bool, ContactTbl, string>(false, null, "IncorrectLoginDetails");
                } */

                // The login attempt succedeed, reset the failed attempts
                user.LoginAttempt = 0;

                // Return the result
                _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Valid login, clearing login attempts...");
                await DapperHelper.ExceuteAsync(
                    "UPDATE dbo.ContactTbl SET LoginAttempt = @LoginAttempt, LoginSessionId = @LoginSessionId WHERE ContactID = @ContactID",
                    new
                    {
                        LoginAttempt = user.LoginAttempt,
                        ContactID = user.ContactID,
                        LoginSessionId = !string.IsNullOrWhiteSpace(currentDeviceId) ? currentDeviceId : Guid.NewGuid().ToString()
                    }
                );
                _logger.LogInformation($"{{IP}}accountServicer/ValidateCustomUser/{username} Valid login, cleared login attempts...");
                message = "Successful login.";
                contact = user;
                return new Tuple<bool, ContactTbl, string>(true, contact, "success");
            }
            catch (Exception ex)
            {
                _logger.LogError($"accountServicer/ValidateCustomUser/{username} Error", ex);
                message = "Something went wrong.";
            }

            return new Tuple<bool, ContactTbl, string>(false, null, "AnErrorOccured"); ;
        }

        /**
        * Compares the hash of the given password against the saved hash.
        */
        public static bool VerifyPassword(string passwordGuess, string actualSavedHashResults, string salt, string iterationCount)
        {
            // Keep track of the verification result
            var isValid = false;

            if(!Config.GetBooleanValue("IsMallatsApp"))
            {
                try
                {
                    // Password salt byte array
                    var saltBytes = Convert.FromBase64String(salt);

                    // Byte array of the password
                    var actualPasswordByteArr = Convert.FromBase64String(actualSavedHashResults);

                    // Iteration count
                    var iterationCountByteArr = Convert.FromBase64String(iterationCount);
                    var iterationCountInt = BitConverter.ToInt32(iterationCountByteArr, 0);

                    // Generate a PBKDF2 hash using the given ingredients
                    var passwordGuessByteArr = GenerateHashValue(passwordGuess, saltBytes, iterationCountInt);

                    // Validate the given hash against the stored hash
                    isValid = ConstantTimeComparison(passwordGuessByteArr, actualPasswordByteArr);
                }
                catch (Exception ex)
                {
                    isValid = false;
                }
            } else
            {
                using (var mD5CryptoServiceProvider = MD5.Create())
                {
                    byte[] bytes = Encoding.ASCII.GetBytes(passwordGuess);
                    byte[] array = mD5CryptoServiceProvider.ComputeHash(bytes);
                    StringBuilder stringBuilder = new StringBuilder();
                    for (int i = 0; i < array.Length; i++)
                    {
                        stringBuilder.AppendFormat("{0:x2}", array[i]);
                    }

                    return stringBuilder.ToString().Equals(actualSavedHashResults.ToLower());
                }
            }

            // Return the verification result
            return isValid;
        }

        /**
        * Creates a PBKDF2 hashe given the 3 main ingredients: password, salt & iteration count
        */
        private static byte[] GenerateHashValue(string password, byte[] salt, int iterationCount)
        {
            byte[] hashValue;
            var valueToHash = string.IsNullOrEmpty(password) ? string.Empty : password;
            using (var pbkdf2 = new Rfc2898DeriveBytes(valueToHash, salt, iterationCount))
            {
                hashValue = pbkdf2.GetBytes(DerivedKeyLength);
            }
            return hashValue;
        }

        /**
        * Compares the bits of the actual and given password.
        */
        private static bool ConstantTimeComparison(byte[] passwordGuess, byte[] actualPassword)
        {
            var difference = (uint)passwordGuess.Length ^ (uint)actualPassword.Length;
            for (var i = 0; i < passwordGuess.Length && i < actualPassword.Length; i++)
            {
                difference |= (uint)(passwordGuess[i] ^ actualPassword[i]);
            }

            return difference == 0;
        }

        public async Task<Tuple<bool, string>> SetUserLogin(string cpr, string exception, bool enableAccount = false, string password = "")
        {
            exception = "";
            var i = 0;
            try
            {
                // Commit the changes to the db
                i = await DapperHelper.ExceuteAsync(
                " SET DATEFORMAT DMY; " +
                " DECLARE @ContactId INT; " +
                " SELECT @ContactId=ContactId FROM ContactTbl WHERE Cont_CPR=@ContactCPR " +
                " UPDATE dbo.ContactTbl SET MobileLogin=1, [Login] = 1 WHERE ContactID = @ContactId;" +
                " DELETE FROM ContactSecurityTbl WHERE ContactID=@ContactId ; " +
                " INSERT INTO dbo.ContactSecurityTbl(ContactID,salt,iteration_count)VALUES(@ContactId,@salt,@iteration_count) ;" +
                " DECLARE @CRID INT;																										" +
                " DECLARE @CustomerID INT;																									" +
                " DECLARE @RefParentContactID INT;																							" +
                " DECLARE @RefCAID INT;																										" +
                " DECLARE @CustAcName NVARCHAR(80);																							" +
                " 																															" +
                " SELECT @CRID=CR.CRID,@CustomerID=C.CustomerID,@CustAcName=C.ContactEng FROM dbo.ContactTbl C								" +
                " INNER JOIN dbo.CustomerRelationTbl CR ON CR.CustomerID = C.CustomerID AND CR.Branch = @BranchId							" +
                " WHERE C.ContactID= @ContactId 																							" +
                " 																															" +
                " SELECT @RefParentContactID=C.ContactID,@RefCAID=CA.CAID FROM dbo.ContactTbl C 											" +
                " INNER JOIN dbo.CustomerAccountTbl CA ON CA.RefContactID=C.ContactID AND CA.CustActId = 1								" +
                " WHERE CustomerID=@CustomerID AND TypeID=1																					" +
                " 																															" +
                /*"  IF NOT EXISTS(SELECT * FROM dbo.CustomerAccountTbl WHERE RefContactID=@ContactId AND CustActId = 1)										" +
                "  BEGIN																													" +
                "  																															" +
                " INSERT INTO dbo.CustomerAccountTbl																						" +
                " 		(CRID,CustActId,CustAcNameE,CustAcNameA,UserModified,DateModified,DateInput,RefContactID,RefParentContactID,RefCAID)" +
                " 		VALUES																												" +
                " 		(@CRID,@CustActId,@CustAcName,@CustAcName,@ContactId,GETDATE(),GETDATE(),@ContactId,@RefParentContactID,@RefCAID)" +
                " END																														" +
                " 																															" +*/
                "   IF NOT EXISTS(SELECT * FROM dbo.ContactRelationTbl WHERE ContactID=@ContactId)											" +
                "  BEGIN																													" +
                " 		INSERT INTO dbo.ContactRelationTbl																					" +
                " 		(ContactID,CRID)																									" +
                " 		VALUES																												" +
                " 		(@ContactId,@CRID)																									" +
                " END	" +
                " INSERT INTO dbo.PermissionTblContact ( [UID] , OID , [Read] , Edit , [Add] , [Delete] , SysID , UserModified , ModifieDate )     " +
                " SELECT @ContactId , OID , [Read] , Edit , [Add] , [Delete] , SysID , UserModified , ModifieDate          		            " +
                " FROM dbo.PermissionTblContact          																					        " +
                " WHERE [UID] = CONVERT(NVARCHAR,@BlankContactId) AND SysID = 50 AND OID NOT IN (SELECT OID FROM PermissionTblContact WHERE UID =CONVERT(NVARCHAR,@ContactId)) ",
            new
            {
                ContactCPR = cpr,
                salt = "",
                iteration_count = "",
                CustActId = "1",
                BlankContactId = 5,
                BranchId = WalletApplication.BranchId
            }
                );

                if (i > 0 && !string.IsNullOrWhiteSpace(password))
                {
                    var resetPass = await ResetUserPasswordByCpr(cpr, password);
                    i += resetPass ? 1 : 0;
                }
            }
            catch (Exception ex)
            {
                exception = ex + "";
                _logger.LogError($"[SetUserLogin][{cpr}] ", ex);
                return new Tuple<bool, string>(false, exception);
            }

            return new Tuple<bool, string>(i > 0, exception);
        }

        public async Task<bool> ResetUserPasswordByCpr(string cpr, string newPassword)
        {
            // Make sure that we have all the required data
            if (string.IsNullOrWhiteSpace(cpr)
                || string.IsNullOrWhiteSpace(newPassword))
            {
                return false;
            }

            // Get the user            
            var rawUserQuery =
                await DapperHelper.QueryAsync<ContactLoginData>(
                    " SELECT CST.salt, CST.iteration_count, CT.*" +
                    " FROM dbo.ContactTbl CT" +
                    " INNER JOIN dbo.ContactSecurityTbl CST ON CST.ContactID = CT.ContactID" +
                    " WHERE CT.Cont_CPR = @Cont_CPR ",
                    new
                    {
                        Cont_CPR = cpr
                    }
                );
            var rawUser = rawUserQuery.FirstOrDefault();

            // Make sure that the user exists
            if (rawUser == null) return false;

            // All good, update the user's password
            var passwordCall = await CreatePasswordHash(newPassword);
            var password = passwordCall.Item1;
            var salt = passwordCall.Item2;
            var iterationCount = passwordCall.Item3;

            // Commit the changes to the db
            DapperHelper.Exceute(
                " UPDATE dbo.ContactTbl SET MobilePassword = @Password, LoginAttempt = 0, LoginLock = 0, changePassword = 0 WHERE ContactID = @ContactId;" +
                " UPDATE dbo.ContactSecurityTbl SET salt = @Salt, iteration_count = @IterationCount WHERE ContactID = @ContactId;",
                new
                {
                    Password = password,
                    ContactID = rawUser.ContactID,
                    Salt = salt,
                    IterationCount = iterationCount
                }
            );
            return true;
        }

        public async Task<Tuple<string, string, string>> CreatePasswordHash(string password)
        {
            // Generate a random salt
            var byteSalt = GenerateRandomSalt();
            var salt = Convert.ToBase64String(byteSalt);

            // Generate a PBKDF2 hash using the given ingredients
            var hashValue = GenerateHashValue(password, byteSalt, 12000);

            // Get the bytes of the iteration count
            var iterationCountBtyeArr = BitConverter.GetBytes(12000);
            var iterationCount = Convert.ToBase64String(iterationCountBtyeArr);

            // Return the base64 representation of the hash
            var valueToSave = new byte[DerivedKeyLength];
            Buffer.BlockCopy(hashValue, 0, valueToSave, 0, DerivedKeyLength);
            return new Tuple<string, string, string>(Convert.ToBase64String(valueToSave), salt, iterationCount);
        }

        private const int SaltByteLength = 24;

        private static byte[] GenerateRandomSalt()
        {
            var salt = new byte[SaltByteLength];

            using (var csprng = RandomNumberGenerator.Create())
            {
                csprng.GetBytes(salt);
            }

            return salt;
        }

        public async Task<bool> ResetUserPasswordByMobile(string mobile, string newPassword)
        {
            // Make sure that we have all the required data
            if (string.IsNullOrWhiteSpace(mobile)
                || string.IsNullOrWhiteSpace(newPassword))
            {
                return false;
            }

            // Get the user            
            var rawUser =
                DapperHelper.Query<ContactLoginData>(
                    " SELECT CST.salt, CST.iteration_count, CT.*" +
                    " FROM dbo.ContactTbl CT" +
                    " INNER JOIN dbo.ContactSecurityTbl CST ON CST.ContactID = CT.ContactID" +
                    " WHERE CT.ContactMobile = @ContactMobile AND MobileLogin=1 ",
                    new
                    {
                        ContactMobile = mobile
                    }
                ).FirstOrDefault();

            // Make sure that the user exists
            if (rawUser == null) return false;

            // All good, update the user's password
            var passwordCall = await CreatePasswordHash(newPassword);
            var password = passwordCall.Item1;
            var salt = passwordCall.Item2;
            var iterationCount = passwordCall.Item3;

            // Commit the changes to the db
            DapperHelper.Exceute(
                " UPDATE dbo.ContactTbl SET MobilePassword = @Password, LoginAttempt = 0, LoginLock = 0, changePassword = 0 WHERE ContactID = @ContactId;" +
                " UPDATE dbo.ContactSecurityTbl SET salt = @Salt, iteration_count = @IterationCount WHERE ContactID = @ContactId;",
                new
                {
                    Password = password,
                    ContactID = rawUser.ContactID,
                    Salt = salt,
                    IterationCount = iterationCount
                }
            );
            return true;
        }

        public string GenerateJWTTokenFromClaims(Claim[] claims)
        {
            var key = Encoding.ASCII.GetBytes(_jWTSettings.SecretKey);
            var credentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature);

            var jwtSecurityToken = new JwtSecurityToken(
            issuer: _jWTSettings.Issuer,
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(_jWTSettings.ExpirationTimeMinutes),
            signingCredentials: credentials);

            var userToken = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);

            return userToken;
        }

        public ClaimsPrincipal ValidateJwtToken(string token, bool validatelifetime = false)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jWTSettings.SecretKey);
            try
            {
                var principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidIssuer = _jWTSettings.Issuer,
                    ValidateIssuer = true,
                    ValidateAudience = false,
                    ValidateLifetime = validatelifetime,
                    // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validateViewModelken);

                var jwtToken = (JwtSecurityToken)validateViewModelken;

                // return claims from JWT token if validation successful
                return principal;
            }
            catch(Exception ex)
            {
                _logger.LogError($"[AccountService]/ValidateJwtToken/{ex.Message}");
                // return null if validation fails
                return null;
            }
        }

        public async Task<string> GetRefreshToken(string username,string token,int ContactID)
        {
            var randomNumber = new byte[64];
            var refreshtoken = "";
            using (var a = RandomNumberGenerator.Create())
            {
                a.GetBytes(randomNumber);
                refreshtoken = Convert.ToBase64String(randomNumber);
            }

            var paremeter = new { username,RefreshToken = refreshtoken, ContactID, Token = token };
            await DapperHelper.ExecuteScalarAsync<int>(@$" INSERT INTO TokenRequests (UName ,ContactID ,Token ,RefreshToken ,revoked ,RequestTime,RefreshTokenExpiryTime)
                                                    SELECT @username,@ContactID,@Token,@RefreshToken,0,GETDATE(),GETDATE()+1; 
                                                    SELECT SCOPE_IDENTITY() ", paremeter);
            return refreshtoken;
        }

        #region SignIn and Signout
        public async Task<string> GenerateJWTTokenSignIn(string username, bool isMobile = false, HttpContext httpContext = null)
        {
            try
            {
                _logger.LogInformation($"{{IP}}[GenerateJWTTokenSignIn][{username}] Sign In START");

                // Get the user's details
                var sqlQuery = " SELECT CT.*, CF.BankCode, CF.CRID, CF.CAID, /*CRT.CTRID,*/ CASE WHEN ISNULL(Parent,'')!='' THEN 'true' ELSE 'false' END 'IsChild', CAS.CustAcctType " +
                    " FROM dbo.ContactTbl CT" +
                    " LEFT JOIN dbo.vw_CustomerFile CF ON CF.RefContactID = CT.ContactID" +
                    " /*INNER JOIN dbo.CustomerFile CF ON CT.CustomerID = CF.RecID*/" +
                    " /*INNER JOIN dbo.CustomerRelationTbl CR ON CR.CustomerID = CF.RecID*/" +
                    " /*LEFT JOIN dbo.CustomerAccountTbl CA ON /*CA.CRID = CR.CRID AND*/ CA.RefContactID = CT.ContactID*/ /*AND CA.CustActId = 1*/" +
                    " LEFT JOIN dbo.CustAcctSetupTbl CAS ON CAS.CustActId = CF.CustActId" +
                    " /*INNER JOIN dbo.ContactRelationTbl CRT ON CRT.ContactID = CT.ContactID AND CRT.CRID = CR.CRID*/" +
                    " WHERE ";

                if(Config.GetBooleanValue("IsMallatsApp"))
                {
                    //  AND CT.MobileLogin=1
                    sqlQuery += "CT.ContactEmail = @Cont_CPR AND CT.[Login] = 1 AND ct.Active = 1 ";
                } else
                {
                    sqlQuery += "CT.Cont_CPR = @Cont_CPR AND CT.[Login] = 1 AND ct.Active = 1";
                }

                var userAccountsQuery = await DapperHelper.QueryAsync<ContactLoginData>(sqlQuery
                    ,
                    new
                    {
                        Cont_CPR = username
                    }
                );
                var userAccounts = userAccountsQuery.ToList();

                if (userAccounts == null || !userAccounts.Any())
                {
                    _logger.LogWarning($"[GenerateJWTTokenSignIn][{username}] Contact data not found!");
                    return "failed";
                }

                // Check if the user has a merchant account open
                var isMerchant = userAccounts.Exists(r => r.CustAcctType == 2);

                var user = userAccounts.FirstOrDefault();
                var LoginSessionId = "";

                // User exists, update the login details
                _logger.LogInformation($"{{IP}}[GenerateJWTTokenSignIn][{username}] Update login params START");
                await DapperHelper.ExceuteAsync(
                    "UPDATE dbo.ContactTbl SET LoginSessionID=@LoginSessionID,LastLogin = @LastLogin, RemoteHost = @RemoteHost WHERE ContactId = @ContactId",
                    new
                    {
                        LastLogin = DateTime.Now,
                        RemoteHost = httpContext.Connection.RemoteIpAddress.ToString(),
                        ContactId = user.ContactID,
                        LoginSessionID = LoginSessionId
                    }
                );
                _logger.LogInformation($"{{IP}}[GenerateJWTTokenSignIn][{username}] Update login params END");

                var claims = new List<Claim>
                {
                    // create *required* claims
                    new Claim(ClaimTypes.NameIdentifier, user.Cont_CPR + ""),
                    new Claim(ClaimTypes.Name, user.ContactEng + ""),

                    // create *optional* claims
                    new Claim(ClaimTypes.Email, user.ContactEmail + ""),
                    new Claim("ContactId", user.ContactID + ""),
                    new Claim("CustomerId", user.CustomerID + ""),
                    new Claim("BankCode", user.BankCode + ""),
                    new Claim("CTRID", user.CTRID + ""),
                    new Claim("CRID", user.CRID + ""),
                    new Claim("CAID", user.CAID + ""),
                    new Claim(ClaimTypes.MobilePhone, user.ContactMobile + ""),
                    new Claim(ClaimTypes.HomePhone, user.ContactTel + ""),
                    new Claim("IsChild", user.IsChild + ""),
                    new Claim("ContactPhoto", user.ContactPhoto + ""),
                    new Claim("IsMerchant", isMerchant + ""),
                    new Claim("ContactEmail", user.ContactEmail + "")
                };

                if (!string.IsNullOrWhiteSpace(user.BillAddress))
                {
                    claims.Add(new Claim("BillAddress", user.BillAddress));
                }

                //var identity = new ClaimsIdentity(claims, "WalletApplication.Cookie");

                // Configure the authentication cookie properties
                //var authProperties = new AuthenticationProperties
                //{
                //    IsPersistent = true
                //};

                // Get the login duration and session renew data
                _logger.LogInformation($"{{IP}}[GenerateJWTTokenSignIn][{username}] isMobile: " + isMobile);
                var keyPrefix = isMobile ? "Mobile" : "";

                // Default for now
                //var loginDurationMinutes = Config.GetStringValue("JWTSettings:ExpirationTimeMinutes") ?? "15";
                //var loginSessionRenew = Config.GetStringValue(keyPrefix + "LoginSessionRenew") ?? "true";

                //_logger.LogInformation($"{{IP}}[GenerateJWTTokenSignIn][{username}] loginDurationMinutes: " + loginDurationMinutes + ", loginSessionRenew: " + loginSessionRenew);

                // Update the authentication cookie properties with the new data
                //authProperties.ExpiresUtc = DateTime.Now.AddMinutes(Convert.ToDouble(loginDurationMinutes));
                //authProperties.AllowRefresh = loginSessionRenew.ToUpper() == "TRUE";

                //await httpContext.SignInAsync(new ClaimsPrincipal(identity), authProperties);

                var token = GenerateJWTTokenFromClaims(claims.ToArray());

                _logger.LogInformation($"{{IP}}[GenerateJWTTokenSignIn][{username}] Sign In END");
                return token;
            }
            catch (Exception ex)
            {
                _logger.LogError($"[GenerateJWTTokenSignIn][{username}] " + ex);
            }

            return "";
        }
        #endregion

        public async Task<Tuple<bool,string>> ChangePassword(string username, string oldPassword, string newPassword)
        {
            var message = "";
            // Make sure that we have all the required data
            if (string.IsNullOrWhiteSpace(username)
                || string.IsNullOrWhiteSpace(oldPassword)
                || string.IsNullOrWhiteSpace(newPassword))
            {
                message = "ParametersError";
                return new Tuple<bool, string>(false, message);
            }

            // Make sure that the new password is actually new!
            if (oldPassword == newPassword)
            {
                message = "PinExistingError";
                return new Tuple<bool, string>(false, message);
            }

            // Get the user            
            var rawUser =
                DapperHelper.Query<ContactLoginData>(
                    " SELECT CST.salt, CST.iteration_count, CT.*" +
                    " FROM dbo.ContactTbl CT" +
                    " INNER JOIN dbo.ContactSecurityTbl CST ON CST.ContactID = CT.ContactID" +
                    " WHERE CT.Cont_CPR = @Cont_CPR",
                    new
                    {
                        Cont_CPR = username
                    }
                ).FirstOrDefault();

            // Make sure that the user exists and that the old password is valid
            if (rawUser == null
                || !VerifyPassword(oldPassword, rawUser.MobilePassword,
                                rawUser.salt, rawUser.iteration_count))
            {
                message = "PinInvalidError";
                return new Tuple<bool, string>(false, message);
            }

            // All good, update the user's password
            
            var password = await CreatePasswordHash(newPassword);
            var salt = password.Item2;
            var iterationCount = password.Item3;

            // Commit the changes to the db
            DapperHelper.Exceute(
                " UPDATE dbo.ContactTbl SET MobilePassword = @Password, changePassword = 0 WHERE ContactID = @ContactId;" +
                " UPDATE dbo.ContactSecurityTbl SET salt = @Salt, iteration_count = @IterationCount WHERE ContactID = @ContactId;",
                new
                {
                    Password = password.Item1,
                    ContactID = rawUser.ContactID,
                    Salt = salt,
                    IterationCount = iterationCount
                }
            );
            message = "PinUpdatedSuccess";
            return new Tuple<bool, string>(true, message);
        }
    }
}