using System;
using System.Collections.Generic;

namespace Optimum.Wallet.Domain.Entities
{
    public class ProductServiceTbl
    {
        public int ProductId { get; set; }
        public int? SortCode { get; set; }
        public string ProductNameE { get; set; }
        public string ProductNameA { get; set; }
        public int ParentId { get; set; }
        public int TypeId { get; set; }
        public bool? Active { get; set; }
        public string CompId { get; set; }
        public string Branch { get; set; }
        public string UserModified { get; set; }
        public DateTime? DateModified { get; set; }
        public string ProductTypeCode { get; set; }
        public int? FormType { get; set; }
        public string IconClass { get; set; }
        public int? Oid { get; set; }
        public bool HasPayment { get; set; }
        public string ProcessCode { get; set; }
        public int? TempOldId { get; set; }
    }
}
