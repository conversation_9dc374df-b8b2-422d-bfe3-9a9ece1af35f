namespace Optimum.Wallet.Application.Interfaces
{
    public interface IPaymentService
    {
        public Task ProcessPayment(int transactionId,
        string responseCode, decimal amount,
        int statusCode, string exceptionMessage,
        int formId, int formTableId, bool processTransaction, string paymentId, 
        string tranId, string refNo, string tranType = "", string cardNumber = "", int caid = -1, decimal targetAmount = 0);
    }
}
