using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Optimum.Wallet.Api.Resources;
using Optimum.Wallet.Application.Common;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Application.Interfaces;
using Optimum.Wallet.Application.Services;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Extensions;
using Optimum.Wallet.Application.Interfaces.Repositories;
using Optimum.Wallet.Domain.Entities;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Globalization;
using System.Security.Claims;

namespace Optimum.Wallet.Api.Controllers
{
    [Route("api/[controller]")]
    public class AccountController : BaseController<AccountController>
    {
        private readonly IAccountService _accountService;
        private readonly IFormRepository _formRepository;
        private readonly ICardRepository _cardRepository;
        private readonly IWebServices _webServices;
        private readonly CacheHelper _cacheHelper;

        private readonly IStringLocalizer _accountLocalizer;
        private readonly IStringLocalizer _servicesLocalizer;
        private readonly IStringLocalizer _globalLocalizer;
        private readonly IMapper _mapper;

        private static readonly string ShuftiProClientID = Config.GetStringValue("ShuftiProClientID") ?? "";
        private static readonly string ShuftiProSecretKey = Config.GetStringValue("ShuftiProSecretKey") ?? "";
        public AccountController(IAccountService accountServicer,
            IFormRepository formRepository,ICardRepository cardRepository,CacheHelper cacheHelper,
            IWebServices webServices, IStringLocalizer<AccountResource> accountLocalizer,
            IStringLocalizer<ServicesResource> servicesLocalizer,
            IStringLocalizer<GlobalResource> globalLocalizer,IMapper mapper) 
        { 
            _accountService = accountServicer;
            _formRepository = formRepository;
            _cardRepository = cardRepository;
            Misc._rootProvider = DataProtectionProvider.Create(new DirectoryInfo(@"C:\\WalletKeys"));
            _cacheHelper = cacheHelper;
            _webServices = webServices;
            _accountLocalizer = accountLocalizer;
            _globalLocalizer = globalLocalizer;
            _servicesLocalizer = servicesLocalizer;
            _mapper = mapper;
        }

        [HttpPost("Login")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<LoginResponse>))]
        [EnableRateLimiting("Web")]
        public async Task<IActionResult> Login(LoginRequest model)
        {
            Logger.LogInformation($"{{IP}} Login Request/isMobile : {isMobile}/{JsonConvert.SerializeObject(model)}");
            var isFingerprintLogin = false;
            var loginResponse = new LoginResponse();
            if (isMobile && !string.IsNullOrWhiteSpace(model.FingerprintHash))
            {
                // Get the fingerprint hash
                var fingerprintHash = model.FingerprintHash;
                model.Username = fingerprintHash.Decrypt("FINGERPRINT");
                isFingerprintLogin = true;
            }

            var column = "";
            var input = "";
            #region verify if user login with cpr or mobile no.
            //Login by CPR
            if (!string.IsNullOrWhiteSpace(model.Username))
            {
                column = "Cont_CPR";
                input = model.Username;
            } //Login by Mobile
            else if (!string.IsNullOrWhiteSpace(model.Mobile))
            {
                column = "ContactMobile";
                input = model.Mobile;
            }
            #endregion

            Logger.LogInformation($"{{IP}}[Login-POST][{model.Username}] Validating user data");

            var isValid = await _accountService.ValidateCustomUser(input, model.Password, column, isFingerprintLogin, isMallatsLogin: model.isMallatsLogin);
            ContactTbl contactData = isValid.Item2;
            if (isValid.Item1)
            {
                model.Username = contactData.Cont_CPR;
                if (contactData.InProgress)
                {
                    Logger.LogInformation($"{{IP}}[Login-POST][{model.Username}] Retail customer - not verified, redirecting to ShuftiPro process...");
                    loginResponse.IsRetailCustomer = true;
                    loginResponse.KYCInProgress = true;
                    return Ok(BaseResponse<LoginResponse>.Success(isValid.Item3, loginResponse));
                }
                else
                {
                    var token = await _accountService.GenerateJWTTokenSignIn(model.Username, isMobile, HttpContext);
                    if (token != "")
                    {
                        loginResponse.Username = model.Username;
                        loginResponse.JWTToken = token;
                        loginResponse.TokenExpiry = Config.GetIntValue("JWTSettings:ExpirationTimeMinutes");
                        loginResponse.RefreshToken = await _accountService.GetRefreshToken(model.Username, token, CurrentUser.ContactID);
                        return Ok(BaseResponse<LoginResponse>.Success(isValid.Item3, loginResponse));
                    }
                    else
                    {
                        return Ok(BaseResponse<LoginResponse>.Failure());
                    }
                }
            }
            else
            {
                return Ok(BaseResponse<LoginResponse>.Failure(new string[] { isValid.Item3 }));
            }            
        }

        [HttpGet("isBiometricRequired")]
        public IActionResult isBiometricRequired()
        {
            var isBiometricRequired = "success";

            if (HttpContext.User.Identity != null)
            {
                if (HttpContext.User.Identity.IsAuthenticated)
                {
                    isBiometricRequired = "failed";
                }
            }

            return Ok(isBiometricRequired);
        }


        [HttpGet("IsAuthenticated")]
        public IActionResult IsAuthenticated()
        {
            return Ok(HttpContext.User.Identity.IsAuthenticated);
        }


        [Authorize]
        [HttpGet("GetAppUserData")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<AppUserResponse>))]
        public IActionResult GetAppUserData()
        {
            var data = _mapper.Map<AppUserResponse>(CurrentUser);
            return Ok(BaseResponse<AppUserResponse>.Success(data));
        }

        [HttpPost("CreateAccount")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<RegisterResponse>))]
        public async Task<IActionResult> CreateAccount([FromBody] RegisterRequest model)
        {
            Logger.LogInformation($"[Account/CreateAccount-POST][{model.Stage}][{model.Username}] Entered");
            if (User.Identity.IsAuthenticated)
            {
                Logger.LogInformation($"[Account/CreateAccount-POST][{model.Stage}][{CurrentUser.Username}] User is logged in");
                return NotFound(BaseResponse<RegisterResponse>.Failure());
            }
            var response = new RegisterResponse();
            response.Username = model.Username;
            string cprNo = "",
                      mobileNo = "",
                      otp = "";
            var data = new CreateAccountData();

            switch (model.Stage)
            {
                case 1:
                    #region CPR Number
                    // Clear any previous data

                    cprNo = model.Username + "";

                    Logger.LogInformation($"{{IP}}[Account/CreateAccount-POST][{model.Stage}][{cprNo}] CPR Number: {cprNo}");

                    var user = (await DapperHelper.QueryAsync<ContactTbl>(
                        "SELECT * FROM dbo.ContactTbl WHERE CONT_CPR = @cprNo ORDER BY [Login] DESC", new
                        {
                            cprNo = cprNo
                        })).FirstOrDefault();

                    if (string.IsNullOrWhiteSpace(cprNo))
                    {
                        return Ok(BaseResponse<RegisterResponse>.Failure(new string[] { "Please enter CPR No." }));
                    }

                    // Check if the user is retail customer
                    var retailCustomerId = Config.GetStringValue("RetailCustomerId") + "";
                    var isRetail = true;//user == null || (user.CustomerID + "") == retailCustomerId;

                    // Move to retail flow
                    if (user == null)
                    {
                        response.Stage = 5;
                        Logger.LogInformation($"{{IP}}[Account/CreateAccount-post][{model.Stage}][{cprNo}] User is retail customer, moving to retail flow");
                        return Ok(BaseResponse<RegisterResponse>.Success(response));
                    }

                    if (user != null && (user.MobileLogin ?? true) && user.Active && !user.InProgress) // User is active
                    {
                        Logger.LogInformation($"{{IP}}[Account/CreateAccount-post][{model.Stage}][{cprNo}] User is already registered!");
                        // this.AddNotification(Resources.Account.MobileAlreadyRegisteredError, NotificationType.INFO);
                        return Ok(BaseResponse<RegisterResponse>.Failure(new string[] { "User is already registered" }));
                    }


                    // Otherwise, the user is creating a new accoun
                    otp = GeneralHelper.GetRandomString(6, true);
#if DEBUG
                    otp = "123456";
#else
                         var smsMessage = string.Format(_accountLocalizer["KYCOTPSMSMessage"], otp);
                         // var isSent = GeneralHelper.SendSMS("0", $"OTP-eKYC", user.ContactMobile, smsMessage, Application.Instance.GetEnvVariable("DefaultSmsSenderName", "Optimum"));         
                         var isSent = await GeneralHelper.SendSMS("0", $"OTP-eKYC", user.ContactMobile, smsMessage, Config.GetStringValue("DefaultSmsSenderName"));
#endif


                    if (user != null) // User already exist so it need to update
                    {
                        Logger.LogInformation($"{{IP}}[Account/CreateAccount-post][{model.Stage}][{cprNo}] User is has record but he doesn't have access to mobile => isUpdateAccount ");
                    }
                    else
                    {
                        Logger.LogInformation($"{{IP}}[Account/CreateAccount-post][{model.Stage}][{cprNo}] No record for this user => isCreateAccount");
                    }


                    data = new CreateAccountData
                    {
                        User = new CreateAccountUser
                        {
                            Phone = user.ContactMobile,
                            Id = cprNo
                        }
                    };

                    return Ok(BaseResponse<RegisterResponse>.Success(response));
                #endregion
                case 5:
                    #region Mobile Number

                    cprNo = model.Username;
                    mobileNo = model.MobileNumber;

                    if (mobileNo.Split("+973")[1] == "")
                    {
                        return Ok("Please enter a mobile number.");
                    }

                    HttpContext.Session.SetString("ApplyNowMobileNumber", mobileNo);
                    otp = GeneralHelper.GetRandomString(6, true);
#if DEBUG
                    otp = "123456";
#else
                         var smsMessage5 = string.Format(_accountLocalizer["KYCOTPSMSMessage"], otp);
                         // var isSent5 = await GeneralHelper.SendSMS("0", $"OTP-eKYC", mobileNo, smsMessage5, Application.Instance.GetEnvVariable("DefaultSmsSenderName", "Optimum"));      
                         var isSent5 = await GeneralHelper.SendSMS("0", $"OTP-eKYC", mobileNo, smsMessage5, Config.GetStringValue("DefaultSmsSenderName"));
#endif

                    Logger.LogInformation($"{{IP}}[Account/CreateAccount-post][{model.Stage}][{cprNo}] No record for this user => isCreateAccount");


                    var data2 = new CreateAccountData
                    {
                        User = new CreateAccountUser
                        {
                            Phone = mobileNo,
                            Id = cprNo
                        }
                    };

                    return Ok(BaseResponse<RegisterResponse>.Success(response));
                    break;
                    #endregion
                    break;
            }
            return Ok();
        }

        [HttpPost("ForgotPassword")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<string>))]
        public async Task<IActionResult> ForgotPassword([FromBody] RegisterRequest model)
        {
            Logger.LogInformation($"[Account/ForgotPassword-POST][{model.Stage}][{model.Username}] Entered");

            var sqlUser = "SELECT * FROM dbo.ContactTbl WHERE 0=0 AND ";

            var condition = "";
            if (WalletApplication.IsAllowLoginByCPR) { condition = "Cont_CPR=@Cont_CPR"; }
            else if (WalletApplication.IsAllowLoginByMobile) { condition = " AND ContactMobile=@ContactMobile"; }

            sqlUser += condition;

            var user = DapperHelper.Query<ContactTbl>(
                    $"{sqlUser} ORDER BY [MobileLogin] DESC", new
                    {
                        Cont_CPR = model.Username,
                        ContactMobile = model.MobileNumber
                    }).FirstOrDefault();

            var isRegistered = false;
            if (user != null)
            {
                isRegistered = user != null && user.Active && (user.MobileLogin == true ? true : false) && !string.IsNullOrWhiteSpace(user.ContactMobile);
            }
            if (!isRegistered)
            {
                return Ok(BaseResponse<string>.Failure("The entered user details is invalid."));
            }

            var otp = GeneralHelper.GetRandomString(6, true);

            await DapperHelper.ExceuteAsync(
                   " INSERT INTO dbo.SMSMessageTbl( ERTID, ProcessCode, From_MobileNo, To_MobileNo, SMSMessage, DateModified, UserModified, SenderName, BankCode, Unicode, Alert)" +
                   " VALUES(0, 'EKYC', '', @MobileNo, @Message, GETDATE(), N'amthal', @SenderName, N'', 0, 0)",
                   new
                   {
                       MobileNo = user.ContactMobile.Replace("+", ""),
                       Message = "Your 6-digit verification code is: " + otp,
                       SenderName = WalletApplication.GetEnvVariable("DefaultSmsSenderName", "Optimum")
                   });

            await DapperHelper.ExceuteAsync($" UPDATE ContactTbl SET LastOTP = @LastOTP WHERE {condition}", new
            {
                LastOTP = otp,
                Cont_CPR = model.Username,
                ContactMobile = model.MobileNumber
            });
            return Ok(BaseResponse<string>.Success("success"));
        }


        [AllowAnonymous]
        [HttpPost("SetPassword")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<string>))]
        public async Task<IActionResult> SetPassword([FromBody] CreatePasswordRequest createPasswordRequest)
        {
            Logger.LogDebug("[SetPassword-POST] Entered");

            var isResetPwd = createPasswordRequest.isResetPwd;
            var isCreateAccount = createPasswordRequest.isCreateAccount;
            var isUpdateAccount = createPasswordRequest.isUpdateAccount;
            var isKYCAccount = createPasswordRequest.isKYCAccount;
            var actionName = createPasswordRequest.actionName;
            var pin = createPasswordRequest.pin+"";

            if (string.IsNullOrEmpty(pin))
            {
                return Ok(BaseResponse<string>.Failure("pin no cannot be empty"));
            }

            var model = createPasswordRequest.registerRequest;

            if (isCreateAccount)
            {
                Logger.LogDebug($"[SetPassword-POST][{model.Username}] Create Account Process Entered ");
                var createAccountData = createPasswordRequest.registerResponse;

                // Generate the user's password hash
                if (createAccountData != null)
                {
                    Logger.LogDebug($"[SetPassword-POST][{model.Username}] Account data is valid, creating the account ");
                    var isAccountCreated = DapperHelper.Exceute($@"
                        INSERT INTO dbo.ContactTbl(
                            CustomerID, ContactEng, ContactArb, TitleID, ContactTel, ContactFax, ContactMobile, ContactEmail, Active, ContactExtn, Password,
                            LastLogin, RemoteHost, Login, CustomerTypeID, Title, LoginSessionID, changePassword, ItemPricingAccess, SamePriceforGroup,
                            DisplayContractedItems, acceptedtandc, ContactSalutation, CSortCode, UserModified, DtInput, Admin, TypeID, ContactPhoto,
                            Cont_CPR, BillAddress, BillCountry, BillCity, BillZipCode, BillNotes, ContactOfficeTel, ContactDOB, LoginAttempt, LoginLock,
                            FingerprintLogin, FingerprintLock, RedirectLogin, RedirectMessage, EntertainerAccess, EntertainerStart, EntertainerEnd, EntertainerCode,
                            NotificationText, verify, WindowsUser, PasswordChanged, DateModified, EmployeeID, OfficeNo, OptimumLogin, EmpCompanyRecID, LastOTP,
                            MobilePassword, MobileLogin, InProgress, GPSProcessed, GPSNoOfTrials)
                        SELECT @CustomerId CustomerID, @ContactName ContactEng, @ContactName ContactArb, TitleID, @ContactMobile ContactTel, '' ContactFax, @ContactMobile ContactMobile, @ContactEmail  ContactEmail, 0 Active, ContactExtn, NULL Password,
                            NULL LastLogin, NULL RemoteHost, Login, CustomerTypeID, Title, NULL LoginSessionID, changePassword, ItemPricingAccess, SamePriceforGroup,
                            DisplayContractedItems, acceptedtandc, ContactSalutation, CSortCode, UserModified, GETDATE() DtInput, Admin, TypeID, ContactPhoto,
                            @ContactCPR Cont_CPR, BillAddress, BillCountry, BillCity, BillZipCode, BillNotes, ContactOfficeTel, ContactDOB, LoginAttempt, LoginLock,
                            FingerprintLogin, FingerprintLock, RedirectLogin, RedirectMessage, EntertainerAccess, EntertainerStart, EntertainerEnd, EntertainerCode,
                            NotificationText, verify, WindowsUser, NULL PasswordChanged, DateModified, EmployeeID, OfficeNo, OptimumLogin, EmpCompanyRecID, LastOTP,
                            MobilePassword, MobileLogin, 1 InProgress, 1 GPSProcessed, GPSNoOfTrials FROM dbo.ContactTbl WHERE ContactID = @BlankContactId",
                    new
                    {
                        CustomerId = Config.GetStringValue("RetailCustomerId"),
                        ContactName = $"eKYC User {model.Username}",
                        ContactMobile = model.MobileNumber,
                        ContactCPR = model.Username,
                        ContactEmail = $"{model.Username}@{Request.Host.Host}",
                        BlankContactId = 5
                    }) > 0;
                    Logger.LogDebug($"[SetPassword-POST][{model.Username}] Created the account with status {isAccountCreated} ");

                    if (isAccountCreated)
                    {
                        Logger.LogDebug($"[SetPassword-POST][{model.Username}] Setting up the mobile account...");
                        string exception = "";
                        var IsUpdate = await _accountService.SetUserLogin(model.Username, exception, !isKYCAccount, pin);
                        exception = IsUpdate.Item2;
                        Logger.LogDebug($"[SetPassword-POST][{model.Username}] Finished setting up the mobile account with status: {IsUpdate}");
                        if (!IsUpdate.Item1)
                        {
                            // this.AddNotification(Resources.Global.UnexpectedError, NotificationType.ERROR);
                            return Ok(BaseResponse<string>.Failure(new string[] { "Failed stting up the mobile account with status, please contact support." }));
                        }

                        Logger.LogDebug($"[SetPassword-POST][{model.Username}] Proceeding to ShuftiPro process");
                        return Ok(BaseResponse<string>.Success());
                    }
                }

                // Failed to create the account
                Logger.LogDebug($"[SetPassword-POST][{model.Username}] Failed to create the account");
                // this.AddNotification(Resources.Global.UnexpectedError, NotificationType.ERROR);

                // ?
                return Ok(BaseResponse<string>.Failure(new string[] { "Failed to create your account, please contact support." }));
            }

            if (isResetPwd || isUpdateAccount || isKYCAccount)
            {
                Logger.LogDebug($"[SetPassword-POST][{model.Username}] Reset password entered");
                if (isUpdateAccount)
                {
                    Logger.LogDebug($"[SetPassword-POST][{model.Username}] update account entered (Setup User Login)");
                    string exception = "";
                    var IsUpdate = await _accountService.SetUserLogin(model.Username, exception, !isKYCAccount, pin);
                    exception = IsUpdate.Item2;
                    if (!IsUpdate.Item1)
                    {
                        return Ok(NotificationType.ERROR);
                    }
                }


                // Try to reset the password
                Logger.LogDebug($"[SetPassword-POST][{model.Username}][{model.MobileNumber}] Reset User Password By Mobile");
                var passwordReset = false;
                if (isResetPwd)
                {
                    if (WalletApplication.IsAllowLoginByCPR && !string.IsNullOrWhiteSpace(model.Username))
                    {
                        passwordReset = await _accountService.ResetUserPasswordByCpr(model.Username, pin);
                    }
                    else if (WalletApplication.IsAllowLoginByMobile && !string.IsNullOrWhiteSpace(model.MobileNumber))
                    {
                        passwordReset = await _accountService.ResetUserPasswordByMobile(model.MobileNumber, pin);
                    }
                }


                // Inform the user with the results
                var resetStatus = passwordReset ? NotificationType.SUCCESS : NotificationType.ERROR;
                var message = "";

                if (isKYCAccount)
                {
                    Logger.LogDebug($"[SetPassword-POST][{model.Username}] isKYCAccount: getting user data");
                    var user = DapperHelper.Query<ContactTbl>(
                    "SELECT * FROM dbo.ContactTbl WHERE CONT_CPR = @cprNo ORDER BY [Login] DESC", new
                    {
                        cprNo = model.Username
                    }).FirstOrDefault();
                    Logger.LogDebug($"[SetPassword-POST][{model.Username}] isKYCAccount: got user data");

                    if (user == null)
                    {
                        Logger.LogDebug($"[SetPassword-POST][{model.Username}] Couldn't find a user with this ShuftiPro reference id");
                        // return View("Error");
                        return Ok(BaseResponse<string>.Success(NotificationType.ERROR));
                    }

                    Logger.LogDebug($"[SetPassword-POST][{model.Username}] Redirecting to ShuftiPro process");
                    return Ok(BaseResponse<RegisterKycProcess>.Success(new RegisterKycProcess() 
                    { 
                        Username = model.Username,
                        ExternalCode = user.EntertainerCode,
                        ExternalUrl = user.EntertainerAccess, 
                        verified = false 
                    })); ;
                }
                else if (isUpdateAccount)
                {
                    message = "Thank you for registering.You can login now.";
                    resetStatus = NotificationType.SUCCESS;
                }
                else
                    message = passwordReset ? "PinChangeSuccess" : "PinChangeFail";

                // this.AddNotification(message, resetStatus);
                Logger.LogDebug($"[SetPassword-POST][{model.Username}] Reset password status: " + passwordReset);

                return Ok(BaseResponse<RegisterKycProcess>.Success());
                // return RedirectToAction("Login");
            }

            const string defaultErrorMsg = "Failed to create your account!";

            // Start the registration transaction process            
            Logger.LogDebug($"[SetPassword-POST][{model.Username}] Create new account entered");

            // Create the user's password hash
            var salt = "";
            var iterationsCount = "";
            var passwordCall = await _accountService.CreatePasswordHash(pin);
            var password = passwordCall.Item1;
            salt = passwordCall.Item2;
            iterationsCount = passwordCall.Item3;

            // Create the user and retrieve their id
            var contact = DapperHelper.Query<ContactRegisterData>(
                "dbo.Usp_CMRM_CreateNewCustomer",
                new
                {
                    BlankCustomerId = 1,
                    BlankContactId = 5,
                    BankCode = model.Username,
                    BankName = CultureInfo.CurrentCulture.TextInfo.ToTitleCase(model.Name),
                    ArabicName = model.Name,
                    Tel1 = model.MobileNumber,
                    Password = password,
                    Salt = salt,
                    HashIterations = iterationsCount,
                    ChangePassword = 0,
                    DtInput = DateTime.Now,
                    OutputContactTbl = 1,
                    BranchID = WalletApplication.BranchId
                },
                commandType: CommandType.StoredProcedure).FirstOrDefault();

            // Show an error message if we fail to create the contact
            if (contact == null)
            {
                // ModelState.AddModelError("", defaultErrorMsg);
                Logger.LogDebug($"[SetPassword-POST][{model.Username}] Failed to create the user account");
                // return Ok("failed");
                return Ok(BaseResponse<string>.Failure(new string[] { NotificationType.ERROR }));
            }

            Logger.LogDebug($"[SetPassword-POST][{model.Username}] User account created successfully");
            Logger.LogDebug($"[SetPassword-POST][{model.Username}] Account created successfully");

            return Ok(BaseResponse<string>.Success(NotificationType.SUCCESS));
        }

        [AllowAnonymous]
        [HttpPost("VerifyCode")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<string>))]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<VerifyCodeResponse>))]
        public async Task<IActionResult> VerifyCode([FromBody] VerifyCodeRequest verifyCodeRequest)
        {
            var message = "InvalidVerificationCodeError";
            var code = verifyCodeRequest.code;
            var isLogin = verifyCodeRequest.isLogin;
            var isResetPwd = verifyCodeRequest.isResetPwd;

            var response = new VerifyCodeResponse();
            response.Username = verifyCodeRequest.Username;

            Logger.LogDebug("{{IP}}[VerifyCode-POST] Entered");

            if (string.IsNullOrWhiteSpace(code))
            {
                Logger.LogInformation("{{IP}}[VerifyCode-POST] SMS code is empty");
                return Ok(BaseResponse<string>.Failure("Please enter an SMS code."));
            }

            var sqlUser = "SELECT * FROM dbo.ContactTbl WHERE 0=0 AND ";

            var condition = "";
            if (WalletApplication.IsAllowLoginByCPR) { condition = "Cont_CPR=@Cont_CPR"; }
            else if (WalletApplication.IsAllowLoginByMobile) { condition = " AND ContactMobile=@ContactMobile"; }

            var user = (await DapperHelper.QueryAsync<ContactTbl>(sqlUser + condition,
                new
                {
                    Cont_CPR = verifyCodeRequest.Username,
                    ContactMobile = verifyCodeRequest.MobileNumber
                })).FirstOrDefault();

            if (user != null && user.LastOtp != verifyCodeRequest.code)/// TODO SAVE OTP ON Memory or contact is not yet created
            {
                Logger.LogInformation("{{IP}}[VerifyCode-POST] " + message);
                return Ok(BaseResponse<string>.Failure("Incorrect SMS code."));
            }

            Logger.LogInformation("[VerifyCode-POST] Code verification passed, redirecting to password page");

            if (!isLogin)
            {
                //=====================================================================================================
                // Check if the user is trying to create an account
                var isCreateAccount = verifyCodeRequest.isCreateAccount;

                // Check if the mode is for update current account 
                var isUpdateAccount = verifyCodeRequest.isUpdateAccount;
                //=====================================================================================================               

                if (user != null && user.InProgress) // User is waiting KYC process
                {
                    var isRetailCustomer = verifyCodeRequest.isKYCAccount;

                    // Retail process
                    if (isRetailCustomer && !user.Verify)
                    {
                        // Retail user exists but isn't verified, redirect to ShuftiPro process
                        Logger.LogDebug($"[VerifyCode-POST][{user.Cont_CPR}] Retail customer - not verified, redirecting to ShuftiPro process...");
                        response.ShuftiproRef = user.EntertainerCode;
                        response.ShuftiproUrl = user.EntertainerAccess;
                        response.ShuftiPending = true;
                        return Ok(BaseResponse<VerifyCodeResponse>.Success(response));
                    }
                    else if (isRetailCustomer && user.Verify && DapperHelper.Query<int>($@"
                        SELECT COUNT(Id)
                        FROM dbo.CustomerServicesTbl cst 
                        INNER JOIN dbo.ContactTbl ct ON ct.ContactID = cst.ContactID
                        WHERE ct.Cont_CPR = @ContactCPR AND cst.Active = 1 AND cst.ProductId = {PageTypes.RETAIL_CUSTOMER_KYC}
                        ", new { ContactCPR = user.Cont_CPR }).FirstOrDefault() <= 0)
                    {
                        // Retail user exists and is verified, redirect to ShuftiPro process
                        response.Stage = 2;
                        return Ok(BaseResponse<VerifyCodeResponse>.Success(response));
                    }
                    else if (isRetailCustomer && user.DisplayContractedItems)
                    {
                        // Retail user exists and is verified, but has to upload missing documents
                        response.Stage = 6;
                        return Ok(BaseResponse<VerifyCodeResponse>.Success(response));
                    }
                    // Non-Retail process
                    else if (user.Verify)
                    {
                        Logger.LogInformation($"[VerifyCode-POST][{user.Cont_CPR}] User is waiting for KYC verification!");
                        response.Stage = 30;
                        return Ok(BaseResponse<VerifyCodeResponse>.Success(response));
                    }
                    else
                    {
                        Logger.LogInformation($"[VerifyCode-POST][{user.Cont_CPR}] User is waiting for KYC process!");
                        response.Stage = 4;
                        return Ok(BaseResponse<VerifyCodeResponse>.Success(response));
                    }
                }

                //If is update account
                if (isUpdateAccount && verifyCodeRequest.Username != "")
                {
                    //Check if is registred or not
                    var isRegistered = user.Active && (user.MobileLogin ?? true);

                    // return isRegistered ? RedirectToAction("Login") : RedirectToAction("SetPassword");
                    return isRegistered ? Ok(BaseResponse<string>.Failure()) : Ok(BaseResponse<string>.Success());
                }
                //If is create account
                else if (isCreateAccount)
                {
                    return Ok(BaseResponse<string>.Success());
                }
                else
                {
                    /*Coming from Create Account Button (Register) */
                    return Ok(BaseResponse<string>.Success());
                    // return RedirectToAction("CreateAccount");
                }
            }

            return Ok(BaseResponse<string>.Failure());
        }

        [Authorize]
        [HttpPost("GetMobileScriptParams")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<ScriptParamsResponse>))]
        public async Task<IActionResult> GetMobileScriptParams()
        {
            var result = new ScriptParamsResponse();
            Logger.LogInformation($"{{IP}}{{USER}}[GetMobileScriptParams][POST]");
            try
            {
                var notificationCounts = await _formRepository.GetNotificationsCount(CurrentUser.CustomerID + "", CurrentUser.ContactID + "");
                result.notifications = notificationCounts["UNREAD"];

                // Get the profile details
                var profile = new ScriptParamProfile
                {
                    username = CurrentUser.Username,
                    name = CurrentUser.Name,
                    avatarId = (CurrentUser.CustomerID + "").Encrypt("AVATAR_ID")
                };
                result.profile= profile;
                result.deviceId = (CurrentUser.Username + "").Encrypt("USERNAME");
            }
            catch (Exception ex)
            {
                Logger.LogError($"{{IP}}{{USER}}[GetMobileScriptParams][POST][Error]{ex.Message}");
                return Ok(BaseResponse<ScriptParamsResponse>.Failure());
                
            }

            return Ok(BaseResponse<ScriptParamsResponse>.Success(result));
        }

        [Authorize]
        [HttpGet("Profile")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK,Type = typeof(ProfileViewModel))]
        public async Task<IActionResult> Profile(int id = -1)
        {
            bool IsNotCurrent = false;
            var ContactID = CurrentUser.ContactID;

            if (id > 0)
            {
                IsNotCurrent = true;
            }

            // Get the logged in contact details including the related customer
            var contactQuery = await DapperHelper.QueryMapAsync<ContactTbl, CustomerFile, ContactTbl>(
                "SELECT *" +
                " FROM dbo.ContactTbl CT" +
                " INNER JOIN dbo.CustomerFile CF ON CT.CustomerID = CF.RecID" +
                " WHERE CT.ContactID = @ContactID",
                (ct, cf) =>
                {
                    ct.CustomerFile = cf;
                    return ct;
                },
                "RecID",
                new
                {
                    ContactID = ContactID
                }
            );
            var contact = contactQuery.FirstOrDefault();

            // Make sure that we have valid data
            if (contact == null)
            {
                return Ok(BaseResponse<string>.Failure());
            }

            // Check if the logged in contact is a main contact
            var isMainContact = contact.CustomerTypeID == CustomerTypes.MAIN;

            // Populate the models
            var personalModel = new PersonalViewModel
            {
                Name = CultureInfo.CurrentCulture.TextInfo.ToTitleCase(contact.ContactEng),
                HomeTel = contact.ContactTel,
                Mobile = contact.ContactMobile,
                Email = contact.ContactEmail,
                BirthDate = contact.ContactDOB,
                CPR = contact.Cont_CPR,
                Address6 = contact.EntertainerCode,
                IsMain = isMainContact,
                Avatar = contact.ContactPhoto.ContactAvatarUrl(),
                econtactid = contact.ContactID.ToString().Encrypt("CONTACT_ID")
            };

            if (IsNotCurrent)
            {
                //TODO:make it dictionary
                var SymbolsMoneyControl = await DapperHelper.ExecuteScalarAsync<string>("SELECT SpecValue FROM dbo.ContactSpecificationsTbl WHERE SpecKey='SymbolsMoneyControl' AND ContactId=@ContactId", new { ContactId = ContactID });
                personalModel.SymbolsMoneyControl = !string.IsNullOrWhiteSpace(SymbolsMoneyControl) && SymbolsMoneyControl.ToUpper() == "TRUE";

                var SymbolsMoney = await DapperHelper.ExecuteScalarAsync<string>("SELECT SpecValue FROM dbo.ContactSpecificationsTbl WHERE SpecKey='SymbolsMoney' AND ContactId=@ContactId", new { ContactId = ContactID });
                decimal dSymbolsMoney = 0;
                decimal.TryParse(SymbolsMoney, out dSymbolsMoney);
                personalModel.SymbolsMoney = dSymbolsMoney;
            }

            // Get the privacy settings forms parent id
            const int parentId = PageTypes.PARENT_PRIVACY_SETTINGS;

            var forms = await _formRepository.GetFormsByParentId(parentId, CurrentUser.ContactID, CurrentUser.CustomerID, -2);

            var model = new ProfileViewModel
            {
                PersonalDetails = personalModel,
                PrivacySettings = forms,
                ChangePassword = default,
                FingerprintSettings = null
            };
            // Render the view
            return Ok(BaseResponse<ProfileViewModel>.Success(model));
        }

        [Authorize]
        [HttpPost("Profile")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> Profile([FromBody]ProfileViewModel model, IFormCollection FC)
        {
            string tabKey = model.tabKey;
            int id = model.id;
            bool IsNotCurrent = false;
            var contactName = "";
            var ContactID = CurrentUser.ContactID;
            if (id > 0)
            {
                IsNotCurrent = true;
            }
            // Keep track of the update result status message
            string defaultMessage = "UnexpectedError";

            // The model is valid, try to update the profile
            try
            {
                // Try to fetch the logged in contact details and related cutomer details
                var contact = DapperHelper.QueryMap<ContactTbl, CustomerFile, ContactTbl>(
                    "SELECT *" +
                    " FROM dbo.ContactTbl CT" +
                    " INNER JOIN dbo.CustomerFile CF ON CT.CustomerID = CF.RecID" +
                    " WHERE CT.ContactID = @ContactID",
                    (ct, cf) =>
                    {
                        ct.CustomerFile = cf;
                        return ct;
                    },
                    "RecID",
                    new
                    {
                        ContactID = ContactID
                    }
                ).FirstOrDefault();

                if (contact == null)
                {
                    return Ok(BaseResponse<string>.Failure());
                }


                if (IsNotCurrent)
                {
                    contactName = contact.ContactEng + " ";
                }


                // Update the profile data
                var message = defaultMessage;
                var status = NotificationType.ERROR;
                var updateSql = " UPDATE dbo.ContactTbl SET";
                switch (tabKey.ToUpper())
                {
                    case "PERSONALDETAILS":
                        {
                            // Keep track of whether the data has been updated or not
                            var updated = false;

                            // Get the personal data
                            var data = model.PersonalDetails;

                            // Keep track of the form status
                            var formStatus = -1;

                            // Update the home phone
                            if (data.HomeTel?.Trim() != contact.ContactTel)
                            {
                                // Update the number locally
                                contact.ContactTel = data.HomeTel?.Trim();

                                // Update the SQL statement
                                updateSql += " ContactTel = @ContactTel,";

                                // The profile has been updated
                                updated = true;
                            }

                            // Update the mobile phone
                            if (data.Mobile?.Trim() != contact.ContactMobile)
                            {
                                // Update the number locally
                                contact.ContactMobile = data.Mobile?.Trim();

                                // Update the SQL statement
                                updateSql += " ContactMobile = @ContactMobile,";

                                // The profile has been updated
                                updated = true;
                            }

                            // Update the email address
                            if (data.Email?.Trim() != contact.ContactEmail)
                            {
                                // Update the email locally
                                contact.ContactEmail = data.Email?.Trim();

                                // Update the SQL statement
                                updateSql += " ContactEmail = @ContactEmail,";

                                // The profile has been updated
                                updated = true;
                            }

                            // Update the birth date
                            if (data.BirthDate != contact.ContactDOB)
                            {
                                // Update the date locally
                                contact.ContactDOB = data.BirthDate;

                                // Update the SQL statement
                                updateSql += " ContactDOB = @ContactDOB,";

                                // The profile has been updated
                                updated = true;
                            }

                            // Need to update the address
                            if (IsNotCurrent)
                            {
                                if (data.Address6?.Trim() != contact.BillAddress?.Trim())
                                {
                                    // Update the address locally
                                    contact.BillAddress = data.Address6;

                                    // Fake statement to pass the execution
                                    updateSql += " BillAddress = @BillAddress,";

                                    // The profile has been updated
                                    updated = true;
                                }
                            }
                            else
                            {
                                if (data.Address6?.Trim() != contact.CustomerFile?.Address6.Trim())
                                {
                                    // The address needs to be approved manually, so no need to update the database
                                    formStatus = StatusValues.AWAITING;

                                    // The profile has been updated
                                    updated = true;
                                }
                            }


                            // If the data was updated
                            if (updated)
                            {
                                // Build the form details
                                const int formId = PageTypes.PERSONAL_INFO_PAGE;
                                const string inputPrefix = General.InputPrefix;
                                var fields = new Dictionary<string, string>
                                {
                                    {
                                        inputPrefix + ControlTypes.HOME_NUMBER,
                                        model.PersonalDetails.HomeTel
                                    },
                                    {
                                        inputPrefix + ControlTypes.MOBILE_NUMBER,
                                        model.PersonalDetails.Mobile
                                    },
                                    {
                                        inputPrefix + ControlTypes.EMAIL_ADDRESS,
                                        model.PersonalDetails.Email
                                    },
                                    {
                                        inputPrefix + ControlTypes.BIRTH_DATE,
                                        model.PersonalDetails.BirthDate+""
                                    },
                                    {
                                        inputPrefix + ControlTypes.HOME_ADDRESS,
                                        model.PersonalDetails.Address6
                                    }
                                };

                                // Insert the form into the database
                                var formTableId = -1;
                                if (!IsNotCurrent && !CurrentUser.IsChild)
                                {
                                    var formdata = await _formRepository.InsertDictionaryRequest(formId, CurrentUser, fields, false);
                                    formTableId = formdata.Item2;
                                }
                                // Overwrite the form status if needed
                                if (formStatus != -1)
                                {
                                    // Build the fields list
                                    var fieldsToUpdate = new Dictionary<int, string>
                                    {
                                        {ControlTypes.STATUS, formStatus + ""}
                                    };

                                    // Update the form
                                    await _formRepository.UpdateRequest(formTableId, fieldsToUpdate);

                                    // Send an email notification if the request wansn't approved automatically
                                    if (formStatus != StatusValues.APPROVED)
                                    {
                                        await _formRepository.ProcessEmailSms(formTableId, formId, ContactID, CurrentUser);
                                    }
                                }

                                // Commit the changes to the database
                                updateSql = updateSql.TrimEnd(',', ' ') + " WHERE ContactID = @ContactID";
                                DapperHelper.Exceute(updateSql, new
                                {
                                    contact.ContactID,
                                    contact.ContactTel,
                                    contact.ContactMobile,
                                    contact.ContactEmail,
                                    contact.ContactDOB
                                });                               

                                // Notify the user of the results
                                message = $"{contactName}Profile data has been successfully updated!";
                                status = NotificationType.SUCCESS;
                            }
                            else
                            {
                                status = NotificationType.INFO;
                                message = "profile data has not been changed!";
                            }                           

                            if (IsNotCurrent)
                            {
                                if (FC.ContainsKey(General.InputPrefix + "chk-style-star") && FC[General.InputPrefix + "chk-style-star"]== "ON")
                                {
                                    var SymbolsMoneyValue = FC[General.InputPrefix + "SymbolValue"] + "";
                                    decimal dSymbolsMoneyValue = 0;
                                    decimal.TryParse(SymbolsMoneyValue, out dSymbolsMoneyValue);
                                    if (dSymbolsMoneyValue > 0)
                                    {
                                        DapperHelper.Exceute("UPDATE ContactSpecificationsTbl SET SpecValue='true' WHERE ContactId=@ContactId AND SpecKey='SymbolsMoneyControl'; " +
                                        " UPDATE ContactSpecificationsTbl SET SpecValue=@SymbolsMoneyValue WHERE ContactId=@ContactId AND SpecKey='SymbolsMoney';", new { ContactId = ContactID, SymbolsMoneyValue = dSymbolsMoneyValue });
                                    }
                                }
                                else
                                {
                                    DapperHelper.Exceute("UPDATE ContactSpecificationsTbl SET SpecValue='false' WHERE ContactId=@ContactId AND SpecKey='SymbolsMoneyControl'; ", new { ContactId = ContactID });
                                }

                                message = $"Data has been successfully updated!";
                                status = NotificationType.SUCCESS;
                            }

                        }
                        break;
                    case "CHANGEPASSWORD":
                        {
                            var username = CurrentUser.Username;
                            if (id > 0)
                            {
                                username = contact.Cont_CPR;
                            }

                            var passwordChanged = false;
                            // Try to update the password
                            if (IsNotCurrent)
                            {
                                passwordChanged = await _accountService.ResetUserPasswordByCpr(username, model.ChangePassword.ConfirmPassword);
                            }
                            else
                            {
                                var datarr = await _accountService.ChangePassword(
                                   username,
                                   model.ChangePassword.OldPassword,
                                   model.ChangePassword.ConfirmPassword);
                                passwordChanged = datarr.Item1;
                                message = datarr.Item2;
                            }


                            // Build the form details
                            const int formId = PageTypes.CHANGE_PASS_PAGE;
                            const string inputPrefix = General.InputPrefix;
                            var fields = new Dictionary<string, string>()
                            {
                                {
                                    inputPrefix + ControlTypes.REQUEST_IP,
                                    IPAddress
                                },
                                {
                                    inputPrefix + ControlTypes.STATUS,
                                    (passwordChanged ? StatusValues.APPROVED : StatusValues.DECLINED) + ""
                                }
                            };

                            // Insert the form into the database
                            var formTableId = -1;
                            var retdata = await _formRepository.InsertDictionaryRequest(formId, CurrentUser, fields, false, false);

                            formTableId = retdata.Item2;

                            // Notify the user of the results
                            if (passwordChanged)
                            {
                                message = "Your PIN have been successfully updated!";
                                status = NotificationType.SUCCESS;
                            }
                            else
                            {
                                message = "Failed to update your PIN! Please try again later.";
                                status = NotificationType.ERROR;
                            }
                        }
                        break;
                    case "PRIVACYSETTINGS":
                        {
                            // Get the privacy settings forms parent id
                            const int parentId = PageTypes.PARENT_PRIVACY_SETTINGS;

                            // Get a list of the page headers depending on the parent
                            var forms = await _formRepository.GetFormsByParentId(parentId, CurrentUser.ContactID, CurrentUser.CustomerID);

                            // Get the submitted form data
                            var formData = FC.ToDictionary(a => a.Key,v => v.Value.FirstOrDefault());

                            // Keep track of the update status
                            var inserted = false;

                            // Process the submitted data
                            foreach (var form in forms)
                            {
                                // Initialize the form collection
                                var formCollection = new Dictionary<string,string>();

                                // Insert the form fields into the collection
                                foreach (var field in form.Fields.Where(f => f.Publish))
                                {
                                    var fieldName = General.InputPrefix + field.ID;
                                    var fieldValue = formData[fieldName]?.ToUpper() == "ON" ? PickerValues.YES : PickerValues.NO;
                                    formCollection.Add(fieldName, fieldValue + "");
                                }

                                // Insert the form into the db
                                inserted = (await _formRepository.InsertDictionaryRequest(form.ID, CurrentUser, formCollection)).Item1;
                                if (!inserted)
                                {
                                    status = NotificationType.ERROR;
                                    message = "Failed to update some of your privacy settings! Please try again later.";
                                    break;
                                }
                            }

                            if (inserted)
                            {
                                status = NotificationType.SUCCESS;
                                message = "Your privacy settings have been successfully updated!";
                            }
                        }
                        break;
                }
                var responsemes = new { message, status };
                return Ok(BaseResponse<dynamic>.Success(responsemes));
            }
            catch (Exception ex)
            {
                return Ok(BaseResponse<dynamic>.Failure(defaultMessage));
            }
        }

        [Authorize]
        [HttpPost("uploadImage")]
        public async Task<IActionResult> uploadImage(IFormFile file, int id = -1)
        {
            var ContactID = CurrentUser.ContactID;
  
            ResponseViewModel response = new ResponseViewModel() { Status = NotificationType.ERROR };

            string prefix = "ContAvatar";
            int PkId = ContactID;
            string appKeyFolder = "ContactPhotoFolder";

            //Get the photo
            try
            {

                if (file.Length <= 0)
                {
                    response.Message = "NoFileFound";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                // Make sure that the file is valid
                if (!FileUploadHelper.IsValid(file))
                {
                    //TODO:Add return message
                    response.Message = "File is invalid";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                // Get the current field's value
                var fieldValue = file.FileName;

                // Get the file extension
                var extension = Path.GetExtension(file.FileName)?.ToLower();

                // Set the file name
                var fileName = $"{prefix}{PkId}{extension}";
                response.ReferenceNumber = fileName;

                // Save the file to disk
                var appFolder = WalletApplication.MobileAppFolder;
                var contactPhotoFolder = Config.GetStringValue(appKeyFolder) + "";
                var path = Path.Combine(appFolder, contactPhotoFolder, fileName);
                using (var stream = new FileStream(path, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                //update database
                var i = DapperHelper.Exceute("UPDATE ContactTbl SET ContactPhoto=@ContactPhoto WHERE ContactID=@ContactID", new
                {
                    ContactID = ContactID,
                    ContactPhoto = fileName
                });

                response.Status = NotificationType.SUCCESS;
                response.SubmitUrl = fileName.ContactAvatarUrl();
                response.Message = "Photo has been changed successfully.";
            }
            catch (Exception ex)
            {
                response.Status = NotificationType.ERROR;
                response.Message = "ErrorOccurred";
                Logger.LogError($"Error when upload profile image for contactId ({id})", ex);
            }

            return Ok(BaseResponse<ResponseViewModel>.Success(response));
        }

        [AllowAnonymous]
        [HttpGet("ContactAvatar")]
        public async Task<IActionResult> ContactAvatar([Required] string econtactId)
        {
            // Get the customer logos folder in which the files exists
            var appFolder = WalletApplication.AppFolder;
            string contactPhotoFolder = Config.GetStringValue("ContactPhotoFolder") + "";

            // Keep track of the logo path
            var photoPath = "";
            var placeholderPath = Path.Combine(appFolder, contactPhotoFolder, "default-avatar.png");

            try
            {
                // Fetch the customer logo using the given customer id
                var contactId = -1;
                if (!int.TryParse(econtactId.Decrypt("CONTACT_ID"), out contactId))
                {
                    return NotFound("The provided avatar id is not valid.");
                }
                var contactPhoto =
                    (await DapperHelper.QueryAsync<string>(
                         "SELECT ContactPhoto FROM dbo.ContactTbl WHERE ContactId=@ContactId",
                        new
                        {
                            ContactId = contactId
                        }
                    )).FirstOrDefault() + "";

                // Construct the file path
                var path = Path.Combine(appFolder, contactPhotoFolder, contactPhoto);

                // Set the logo path
                photoPath = System.IO.File.Exists(path) ? path : placeholderPath;
            }
            catch (Exception ex)
            {
                // Something went wrong, use the placeholder
                photoPath = placeholderPath;
            }

            // Finally, return the file
            return File(photoPath, GeneralHelper.GetMimeType(photoPath));
        }

        [Authorize]
        [HttpPost("SubmitProfile")]
        public async Task<IActionResult> SubmitProfile([FromBody]ProfileViewModel model,[FromForm]IFormCollection fc)
        {
            string tabKey = model.tabKey;
            int id = model.id;

            Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Entered");
            ResponseViewModel Response = new ResponseViewModel() { };

            bool IsNotCurrent = false;
            var contactName = "";
            var ContactID = CurrentUser.ContactID;
            // Keep track of the update result status message
            string defaultMessage = "UnexpectedError";
            try
            {
                // Try to fetch the logged in contact details and related cutomer details
                var contact = DapperHelper.QueryMap<ContactTbl, CustomerFile, ContactTbl>(
                    "SELECT *" +
                    " FROM dbo.ContactTbl CT" +
                    " INNER JOIN dbo.CustomerFile CF ON CT.CustomerID = CF.RecID" +
                    " WHERE CT.ContactID = @ContactID",
                    (ct, cf) =>
                    {
                        ct.CustomerFile = cf;
                        return ct;
                    },
                    "RecID",
                    new
                    {
                        ContactID
                    }
                ).FirstOrDefault();

                if (contact == null)
                {
                    Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Contact details not found");
                    return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                }

                // Update the profile data
                var userHasGPSAccount = (await _cardRepository.GetCards<CardDetailsViewModel>(CurrentUser.ContactID, CurrentUser.CustomerID, -1, false, "", true, false, CurrentUser.Username)).Count() > 0;
                userHasGPSAccount = false;
                Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Has GPS Account: {userHasGPSAccount}");
                var message = defaultMessage;
                var status = NotificationType.ERROR;
                var UPDATE_ChooseListDetailsTbl = "";
                var updateSql = " UPDATE dbo.ContactTbl SET";
                switch (tabKey.ToUpper())
                {
                    case "PERSONALDETAILS":
                        {
                            // Keep track of whether the data has been updated or not
                            var updated = false;

                            // Get the personal data
                            var data = model.PersonalDetails;

                            // Keep track of the form status
                            var formStatus = -1;

                            // Update the mobile phone
                            if (data.Mobile?.Trim() != contact.ContactMobile)
                            {
                                Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Updating mobile number...");

                                var continueUpdateMobile = true;
                                //if user has GPS account update mobile in GPS
                                if (userHasGPSAccount)
                                {
                                    Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Updating mobile number with GPS...");
                                    var response = await _webServices.F18_UpdateMobileNumber_Raw(CurrentUser.Username, data.Mobile?.Trim(), "");
                                    Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Updated mobile number with GPS: {response}");
                                    //if response is success then continue to update the mobile in database
                                    continueUpdateMobile = response == "000";
                                }

                                if (continueUpdateMobile)
                                {
                                    // Update the number locally
                                    contact.ContactMobile = data.Mobile?.Trim();

                                    // Update the SQL statement
                                    updateSql += " ContactMobile = @ContactMobile,";
                                    UPDATE_ChooseListDetailsTbl += $" UPDATE ChooseListDetailsTbl SET CLIId = @ContactMobile WHERE TableId = @N_CSID AND CLCatId = {ControlTypes.WPS_KYC_MOBILE} ;";

                                    // The profile has been updated
                                    updated = true;
                                }
                            }

                            // Update the email address
                            if (data.Email?.Trim() != contact.ContactEmail)
                            {
                                Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Updating email address...");

                                //Email
                                var currentEmailContact =
                                  DapperHelper.Query<ContactTbl>(
                                      "SELECT * FROM dbo.ContactTbl WHERE ContactEmail = @ContactEmail  AND ContactId !=@ContactId",
                                      new
                                      {
                                          ContactEmail = data.Email?.Trim(),
                                          ContactId = ContactID
                                      }
                              ).FirstOrDefault();

                                if (currentEmailContact != null)
                                {
                                    Response.Message = "The provided email is already exist.";
                                    Response.Status = NotificationType.ERROR;
                                    Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Contact details not found");
                                    return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                                }


                                var continueUpdateEmail = true;
                                //if user has GPS account update email in GPS
                                if (userHasGPSAccount)
                                {
                                    Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Updating email address with GPS...");
                                    var response = await _webServices.F19_UpdateEmail_Raw(CurrentUser.Username, data.Email?.Trim());
                                    Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Updated email address with GPS: {response}");
                                    //if response is success then continue to update the email in database
                                    continueUpdateEmail = response == "000";
                                }

                                if (continueUpdateEmail)
                                {
                                    // Update the email locally
                                    contact.ContactEmail = data.Email?.Trim();

                                    // Update the SQL statement
                                    updateSql += " ContactEmail = @ContactEmail,";
                                    UPDATE_ChooseListDetailsTbl += $" UPDATE ChooseListDetailsTbl SET CLIId = @ContactEmail WHERE TableId = @N_CSID AND CLCatId = {ControlTypes.WPS_KYC_EMAIL} ;";

                                    // The profile has been updated
                                    updated = true;
                                }
                            }

                            // Update the address
                            if (data.Address6 != contact.BillAddress)
                            {
                                Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Updating address...");

                                // Update the date locally
                                contact.BillAddress = data.Address6;

                                // Update the SQL statement
                                updateSql += " BillAddress = @BillAddress,";
                                UPDATE_ChooseListDetailsTbl += $" UPDATE ChooseListDetailsTbl SET CLIId = @ContactAddress WHERE TableId = @N_CSID AND CLCatId = {ControlTypes.WPS_KYC_ADDRESS} ;";

                                // The profile has been updated
                                updated = true;
                            }

                            //TODO:Remove the coede if not needed
                            //if (!string.IsNullOrWhiteSpace(data.Avatar))
                            //{
                            //    contact.ContactPhoto = data.Avatar;
                            //    updateSql += " ContactPhoto = @ContactPhoto,";

                            //    // The profile has been updated
                            //    updated = true;
                            //}


                            // If the data was updated
                            if (updated)
                            {
                                // Build the form details
                                const int formId = PageTypes.PERSONAL_INFO_PAGE;
                                const string inputPrefix = General.InputPrefix;

                                var DOB = contact.ContactDOB.HasValue ? contact.ContactDOB.Value.ToString("dd/MM/yyy") : DateTime.Today.ToString("dd/MM/yyy");
                                var fields = new Dictionary<string, string>()
                            {
                                {
                                    inputPrefix + ControlTypes.HOME_NUMBER,
                                    model.PersonalDetails.HomeTel
                                },
                                {
                                    inputPrefix + ControlTypes.MOBILE_NUMBER,
                                    model.PersonalDetails.Mobile
                                },
                                {
                                    inputPrefix + ControlTypes.EMAIL_ADDRESS,
                                    model.PersonalDetails.Email
                                },

                                {
                                    inputPrefix + ControlTypes.BIRTH_DATE,
                                    DOB
                                },
                                {
                                    inputPrefix + ControlTypes.HOME_ADDRESS,
                                    model.PersonalDetails.Address6
                                }
                            };

                                // Insert the form into the database
                                var formTableId = -1;
                                if (!IsNotCurrent && !CurrentUser.IsChild)
                                {
                                    var formdata = await _formRepository.InsertDictionaryRequest(formId, CurrentUser, fields, false);
                                    formTableId = formdata.Item2;
                                }

                                // Overwrite the form status if needed
                                if (formStatus != -1)
                                {
                                    // Build the fields list
                                    var fieldsToUpdate = new Dictionary<int, string>
                                {
                                    {ControlTypes.STATUS, formStatus + ""}
                                };

                                    // Update the form
                                    await _formRepository.UpdateRequest(formTableId, fieldsToUpdate);

                                    // Send an email notification if the request wansn't approved automatically
                                    if (formStatus != StatusValues.APPROVED)
                                    {
                                        await _formRepository.ProcessEmailSms(formTableId, formId, CurrentUser.ContactID, CurrentUser);
                                    }
                                }


                                #region Insert the employee KYC form into the database
                                //BEG: Insert the employee KYC form into the database
                                //Get active KYC form related to current contact
                                Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Getting the employee KYC CSID from the database...");
                                var O_CSID = DapperHelper.ExecuteScalar<int>(
                                    $" SELECT TOP 1 ID FROM dbo.CustomerServicesTbl " +
                                    $"WHERE ProductID = {PageTypes.EMPLOYEE_KYC} AND Active = 1 AND Posted = 1 AND ContactID = @ContactID ORDER BY ID DESC; "
                                    , new { ContactID = contact.ContactID });
                                Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Got the employee KYC CSID from the database: {O_CSID}");

                                //build sql for insert new form with changes, it will be inactive and not posted till update the contactTbl
                                var insertSql = " SET DATEFORMAT DMY; "
                                  + " DECLARE @N_CSID INT; "
                                  + " INSERT INTO dbo.CustomerServicesTbl(ProductID, UserModified, DateModified, Active, RecID, TypeID, ContactID, CRID, Posted, SeqNo, CAID, FBatchID, AccountPosted, SalaryPosted, RejectedBy, RejectedOn, Parent, ConSubProfitSeg) "
                                  + " SELECT ProductID, @Username, GETDATE(), 0, RecID, TypeID, ContactID, CRID, 0, SeqNo, CAID, FBatchID, AccountPosted, SalaryPosted, RejectedBy, RejectedOn, Parent, ConSubProfitSeg FROM dbo.CustomerServicesTbl WHERE ID = @O_CSID "
                                  + " SELECT @N_CSID = CONVERT(INT, SCOPE_IDENTITY()); "

                                  + " INSERT INTO dbo.ChooseListDetailsTbl(pageId, TableId, CLCatId, CLIId, SortCode, Active, DataID, TicktTranID, CustTicktID) "
                                  + " SELECT pageId, @N_CSID, CLCatId, CLIId, SortCode, Active, DataID, TicktTranID, CustTicktID FROM dbo.ChooseListDetailsTbl WHERE TableId = @O_CSID "

                                  + UPDATE_ChooseListDetailsTbl //=> UPDATE_ChooseListDetailsTbl is variable bulid it in the top. Any feild change will update in the new form
                                + " SELECT @N_CSID;";

                                Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Inserting the employee KYC form into the database...");
                                var N_CSID = DapperHelper.ExecuteScalar<int>(insertSql, new
                                {
                                    O_CSID = O_CSID,
                                    Username = CurrentUser.Username,
                                    ContactID = contact.ContactID,
                                    ContactName = contact.ContactEng,
                                    ContactMobile = contact.ContactMobile,
                                    ContactEmail = contact.ContactEmail,
                                    ContactDOB = contact.ContactDOB,
                                    ContactAddress = contact.BillAddress
                                });
                                Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Inserted the employee KYC form into the database: {N_CSID}");
                                //END: Insert the employee KYC form into the database
                                #endregion
                                //TODO:REMOVE THE TRUE & COMMENT
                                if (true/*N_CSID > 0*/)
                                {
                                    Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Updating contact details...");

                                    // Commit the changes to the database
                                    updateSql = updateSql.TrimEnd(',', ' ') + " WHERE ContactID = @ContactID";
                                    var i = DapperHelper.Exceute(updateSql, new
                                    {
                                        ContactID = contact.ContactID,
                                        ContactEng = contact.ContactEng,
                                        ContactMobile = contact.ContactMobile,
                                        ContactEmail = contact.ContactEmail,
                                        ContactDOB = contact.ContactDOB,
                                        BillAddress = contact.BillAddress,
                                        ContactPhoto = contact.ContactPhoto
                                    });
                                    Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Updated contact details: {i}");

                                    //If Commit the changes to the database successfully activate new form and inactivate the orginal form
                                    if (i > 0)
                                    {
                                        Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Activating new KYC form...");
                                        DapperHelper.Exceute(
                                            " UPDATE CustomerServicesTbl SET Active=0,Posted=0 WHERE ID=@O_CSID ;" +
                                            "UPDATE CustomerServicesTbl SET Active=1,Posted=1 WHERE ID=@N_CSID ;", new
                                            {
                                                O_CSID = O_CSID,
                                                N_CSID = N_CSID
                                            });
                                        Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} Activated new KYC form");
                                    }
                                }

                                // Notify the user of the results
                                message = "ProfileChangeSuccess";
                                status = NotificationType.SUCCESS;
                            }
                            else
                            {
                                Logger.LogDebug($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username} No data to change");
                                status = NotificationType.INFO;
                                message = "ProfileNoChangeInfo";
                            }

                            // Ignore all validation rules on the other tabs
                            foreach (var key in ModelState.Keys.Where(key => key.StartsWith("ChangePassword.")).ToList())
                            {
                                ModelState.Remove(key);
                            }
                        }
                        break;
                    case "CHANGEPASSWORD":
                        {
                            // Try to update the password
                            var passwordChangedMessage = "";

                            var username = CurrentUser.Username;
                            if (id > 0)
                            {
                                username = contact.Cont_CPR;
                            }

                            var passwordChanged = false;
                            if (IsNotCurrent)
                            {
                                passwordChanged = await _accountService.ResetUserPasswordByCpr(username, model.ChangePassword.ConfirmPassword);
                            }
                            else
                            {
                                passwordChanged = (await _accountService.ChangePassword(
                                        CurrentUser.Username,
                                        model.ChangePassword.OldPassword,
                                        model.ChangePassword.ConfirmPassword)).Item1;
                            }


                            // Build the form details
                            const int formId = PageTypes.CHANGE_PASS_PAGE;
                            const string inputPrefix = General.InputPrefix;
                            var fields = new Dictionary<string, string>()
                            {
                                {
                                    inputPrefix + ControlTypes.REQUEST_IP,
                                    IPAddress
                                },
                                {
                                    inputPrefix + ControlTypes.STATUS,
                                    (passwordChanged ? StatusValues.APPROVED : StatusValues.DECLINED) + ""
                                }
                            };

                            // Insert the form into the database
                            var formTableId = -1;
                            formTableId = (await _formRepository.InsertDictionaryRequest(formId, CurrentUser, fields, false, false)).Item2;

                            // Notify the user of the results
                            if (passwordChanged)
                            {
                                message = passwordChangedMessage;
                                status = NotificationType.SUCCESS;
                            }
                            else
                            {
                                message = passwordChangedMessage;
                                status = NotificationType.ERROR;
                            }
                        }
                        break;
                    case "PRIVACYSETTINGS":
                        {
                            // Get the privacy settings forms parent id
                            const int parentId = PageTypes.PARENT_PRIVACY_SETTINGS;

                            // Get a list of the page headers depending on the parent
                            var forms = await _formRepository.GetFormsByParentId(parentId, CurrentUser.ContactID, CurrentUser.CustomerID);

                            // Get the submitted form data
                            var formData = fc;

                            // Keep track of the update status
                            var inserted = false;

                            // Process the submitted data
                            foreach (var form in forms)
                            {
                                // Initialize the form collection
                                var formCollection = new Dictionary<string,string>();

                                // Insert the form fields into the collection
                                foreach (var field in form.Fields.Where(f => f.Publish))
                                {
                                    var fieldName = General.InputPrefix + field.ID;
                                    var fieldValue = formData[fieldName] == "ON" ? PickerValues.YES : PickerValues.NO;
                                    formCollection.Add(fieldName, fieldValue + "");
                                }

                                // Insert the form into the db
                                inserted = (await _formRepository.InsertDictionaryRequest(form.ID, CurrentUser, formCollection)).Item1;
                                if (!inserted)
                                {
                                    status = NotificationType.ERROR;
                                    message = "ErrorPrivacySettings";
                                    break;
                                }
                            }

                            if (inserted)
                            {
                                status = NotificationType.SUCCESS;
                                message = "SuccessPrivacySettings";
                            }
                        }
                        break;
                }

                // Update the status message
                Response.Message = message;
                Response.Status = status;
            }
            catch (Exception ex)
            {
                Response.Message = NotificationType.ERROR;
                Response.Status = "An unexpected error has occurred.Please try again later";
                Logger.LogError($"[Account/SubmitProfile/{tabKey}/{CurrentUser.Username}", ex);
            }

            return Ok(BaseResponse<ResponseViewModel>.Success(Response));
        }

        [AllowAnonymous]
        [HttpPost("RefreshToken")]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest refreshTokenRequest)
        {
            ///TO DO
            var refreshToken = refreshTokenRequest.RefreshToken;
            var token = refreshTokenRequest.JWTToken;
            if (refreshToken == "" || token == "")
            {
                return Unauthorized(BaseResponse<string>.Failure(new string[] {"invalid tokens"}));
            }
            else
            {
                var cp = _accountService.ValidateJwtToken(token);
                if (cp == null)
                {
                    return Unauthorized(BaseResponse<string>.Failure(new string[] { "invalid jwt token" }));
                }

                var username = cp.Claims.FirstOrDefault(a => a.Type == ClaimTypes.NameIdentifier).Value;
                int contactid = 0;
                int.TryParse(cp.Claims.Where(a => a.Type == "ContactId")?.FirstOrDefault().Value,out contactid);

                var tokenid = await DapperHelper.ExecuteScalarAsync
                    <int>(" SELECT ID FROM TokenRequests Where UName = @UName and RefreshToken = @RefreshToken And Token = @Token And Revoked = 0 And RefreshTokenExpiryTime > GetDate() ",
                    new
                    {
                        UName = username,
                        RefreshToken = refreshToken,
                        Token = token
                    });

                if(tokenid > 0)
                {
                    await DapperHelper.ExceuteAsync(" UPDATE TokenRequests SET Revoked = 1 WHERE ID = @ID ", new { ID = tokenid });
                    LoginResponse loginResponse = new LoginResponse();
                    var newtoken = await _accountService.GenerateJWTTokenSignIn(username, isMobile, HttpContext);
                    if (newtoken != "")
                    {
                        loginResponse.Username = username;
                        loginResponse.JWTToken = token;
                        loginResponse.TokenExpiry = Config.GetIntValue("JWTSettings:ExpirationTimeMinutes");
                        loginResponse.RefreshToken = await _accountService.GetRefreshToken(username, token, contactid);
                        return Ok(BaseResponse<LoginResponse>.Success(loginResponse));
                    }
                    else
                    {
                        return Ok(BaseResponse<string>.Failure("Token generation error."));
                    }
                }
                else
                {
                    return Ok(BaseResponse<string>.Failure("Refresh token expired or revoked, please sign in again"));
                }
            }
        }

        [Authorize]
        [HttpGet("Logout")]
        public async Task<IActionResult> Logout([Required] string refreshToken, bool inactiveAccount = false)
        {
            ///TO DO
            ///Revoke refresh token
            ///
            var username = CurrentUser.Username;

            if (inactiveAccount)
            {
                await DapperHelper.ExceuteAsync("UPDATE contacttbl SET active=0 WHERE contactemail = @Email",
                    new
                    {
                        CurrentUser.Email,
                    }, "Utilities");
            }

            await DapperHelper.ExceuteAsync("UPDATE TokenRequests SET revoked=1 WHERE RefreshToken = @RefreshToken And UName = @username ",
                    new
                    {
                        RefreshToken = refreshToken,
                        username
                    }, "Utilities");

            return Ok();
        }

        [Authorize]
        [HttpGet("GetUserMenus")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<UserMenus>))]
        public async Task<IActionResult> GetUserMenus()
        {
            var key = string.Format(CacheKeys.USER_MENU, CurrentUser.Username);
            var menus = await _cacheHelper.GetCacheAsync<List<MenuViewModel>>(key);
            if (menus == null)
            {
                menus =
                   (await DapperHelper.QueryAsync<MenuViewModel>(
                       $" SELECT O.OID AS 'Id',{LanguageHelper.GetSelectStmt("O.EngName", "Title")},O.ObjectName AS 'Url', O.[Version] AS 'ParentId', O.IconClass AS 'Icon' ,PS.ProductId " +
                       " FROM dbo.ObjectsTbl O" +
                       " INNER JOIN dbo.PermissionTbl P ON P.OID = O.OID" +
                       " LEFT JOIN dbo.ProductServiceTbl PS ON PS.OID = O.OID" +
                       LanguageHelper.GetLeftJoin("ObjectsTbl", "EngName", "O.OID") +
                       " WHERE O.SysID = 50 AND O.[ObjID] = 9 AND O.Show = 1 AND P.[UID] = @UID" +
                       " ORDER BY O.Menu, O.SubMenu1, O.SubMenu2",
                       new
                       {
                           UID = CurrentUser.ContactID + ""
                       }
                   )).ToList();
                await _cacheHelper.SetCacheAsync(key, menus);
            }

            menus.ForEach(a => a.PID = a.ProductId.ToString().Encrypt("PID"));

            var merchant = menus.Where(a => a.ParentId == 500048).ToList();
            if(CurrentUser.IsMerchant)
            {
                merchant = menus.Where(a => a.Id == 500050).ToList();
                merchant.Add(new MenuViewModel
                {
                    Id = -999,
                    Icon = "ci ci-wallet",
                    Url = "#statementWallet",
                    Title = "Statement"
                });
                merchant.Add(new MenuViewModel
                {
                    Id = -999,
                    Icon = "ci ci-wallet",
                    Url = "#statementHold",
                    Title = "Hold Statement"
                });
            }
            var usermenu = new UserMenus()
            {
                AllMenus = menus.Where(a => a.ParentId != 0).ToList(),
                MainMenu = menus.Where(a => a.ParentId == 500001).ToList(),
                UserMenu = menus.Where(a => a.ParentId == 500039).ToList(),
                CardMenu = menus.Where(a => a.ParentId == 500042).ToList(),
                WalletMenu = menus.Where(a => a.ParentId == 500048).ToList(),
                MerchantMenu = merchant
            };

            return Ok(BaseResponse<UserMenus>.Success(usermenu));
        }

        [Authorize]
        [HttpGet("Cards")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<List<WalletAccount>>))]
        public async Task<IActionResult> Cards()
        {
            var walletAccounts = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, PaySubType: 1);
            if(walletAccounts == null)
            {
                return Ok(BaseResponse<List<WalletAccount>>.Failure());
            }
            return Ok(BaseResponse<List<WalletAccount>>.Success(walletAccounts));
        }

        [Authorize]
        [HttpGet("GetNotifications")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<List<Notification>>))]
        public async Task<IActionResult> GetNotifications(int? id, int page = 1, bool isFirst = false)
        {
            // Keep track of the notifications list
            var notifications = new List<Notification>();

            try
            {
                // Define the number of items in each page
                const int pageLimit = 10;

                // Calculate the page offset
                var offset = pageLimit * (page - 1);

                var type = DapperHelper.Query<string>(
                    $" SELECT CLI.CLNameE AS Title" +
                    " FROM dbo.ChooseListCategoryItemTbl CLCI" +
                    " INNER JOIN dbo.ChooseListItemTbl CLI ON CLI.CLIId = CLCI.CLIId" +
                    " WHERE CLCI.CLCatId = @CLCatId AND CLCI.CatMapID = @CatMapID AND CLI.CLIId = @CLIId AND CLI.Active=1 ",
                    new
                    {
                        CLCatId = ControlTypes.TYPE,
                        CatMapID = PageTypes.NOTIFICATIONS_PAGE,
                        CLIId = id
                    }
                ).FirstOrDefault();


                var typeA = DapperHelper.Query<string>(
                    $" SELECT  {LanguageHelper.GetSelectStmt("cli.CLNameE", "Title", 1)}" +
                    " FROM dbo.ChooseListCategoryItemTbl CLCI" +
                    " INNER JOIN dbo.ChooseListItemTbl CLI ON CLI.CLIId = CLCI.CLIId" +
                    LanguageHelper.GetLeftJoin("ChooseListItemTbl", "CLNameE", "cli.CLIId", 1) +
                    " WHERE CLCI.CLCatId = @CLCatId AND CLCI.CatMapID = @CatMapID AND CLI.CLIId = @CLIId AND CLI.Active=1 ",
                    new
                    {
                        CLCatId = ControlTypes.TYPE,
                        CatMapID = PageTypes.NOTIFICATIONS_PAGE,
                        CLIId = id
                    }
                ).FirstOrDefault();

                // Prepare the procedure parameters
                var parameters = new
                {
                    // English language
                    Lang = 1,
                    // Notifications forms
                    ProductId = PageTypes.NOTIFICATIONS_PAGE,
                    // Only show the current user's data
                    InternalWhereStmt = $" AND cst.ContactID = {CurrentUser.ContactID}",
                    // Only show the given type
                    WhereStmt = $" AND [Type] IN ({id}{(id == PickerValues.REQUEST ? $",{PickerValues.REQUEST_SENT},{PickerValues.SPLIT_PAYMENT_REQUEST}" : "")})",
                    // Include only active items
                    OnlyActive = '1',
                    // Order the notifications by time in descending order
                    OrderStmt =
                        " ORDER BY CASE" +
                            " WHEN ISDATE([Application Date]) = 1" +
                            " THEN CONVERT(DATETIME, [Application Date])" +
                            " ELSE NULL" +
                        " END DESC",
                    // Only show X items
                    Limit = pageLimit,
                    // Skip X items
                    Offset = offset,
                    // Only return the given columns
                    OutputColumns =
                        " ID, [Application Date] AS Date, [Content]," +
                        " [Type], CONVERT(BIT, CASE WHEN [Read] = 'No' THEN 0 ELSE 1 END) AS IsRead"
                };


                // Exceute the procedure using the given parameters                
                notifications = (await DapperHelper.QueryAsync<Notification>(
                    "dbo.Usp_CVRMRPT_ServiceRequests",
                    parameters,
                    commandType: CommandType.StoredProcedure
                )).ToList();

                return Ok(BaseResponse<List<Notification>>.Success(notifications));
            }
            catch (Exception ex)
            {
                Logger.LogError($"[GetNotifications-GET][{CurrentUser.Username}] Failed to retrieve user's notifications.", ex);
            }

            return Ok(BaseResponse<List<Notification>>.Failure());
        }

        [Authorize]
        [HttpPost("UpdateNotification")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<ResponseViewModel>))]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<dynamic>))]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<string>))]
        public async Task<IActionResult> UpdateNotification([FromBody] NotificationRequest notificationRequest)
        {
            var ids = notificationRequest.ids;
            var type = notificationRequest.type;

            try
            {
                // Make sure that we have data
                if (ids == null || !ids.Any())
                {
                    return Ok(BaseResponse<string>.Failure());
                }

                // Get a list of the decrypted ids
                var decIds = new List<int>();
                var notificationId = -1;

                foreach (var id in ids)
                {
                    // Decrypt the notification id
                    var decId = Misc.Decrypt(id, "NOTIFICATION_ID");

                    // The notification id isn't valid, skip it
                    if (string.IsNullOrWhiteSpace(decId) || !int.TryParse(decId, out notificationId))
                    {
                        continue;
                    }

                    // Add the decrypted id to the list
                    decIds.Add(notificationId);
                }

                // Make sure that we have data
                if (!decIds.Any())
                {
                    return Ok(BaseResponse<string>.Failure());
                }

                // Initialize the form repository
                //var formRepository = new FormRepository(this, ServicesLocalizer, GlobalLocalizer);

                // Keep track of the results
                var updated = false;


                // If this is a delete action
                // If this is a accept request (WALLTE TRANSFER) action => Transfer money to user
                // If this is a reject request (WALLTE TRANSFER) action => In active to hide the notification
                var response = new ResponseViewModel();
                type = (type + "").ToUpper();
                if (type == "DELETE" || type == "ACCEPT" || type == "REJECT")
                {
                    var sql =
                        " UPDATE dbo.CustomerServicesTbl" +
                        " SET active = 0 WHERE ID IN (" + string.Join(",", decIds) + ") OR (Par1 <> '' AND Par1 IN (SELECT Par1 FROM dbo.CustomerServicesTbl WHERE ID IN (" + string.Join(",", decIds) + ")))" +
                        " /*AND RecID = @CustomerID*/" +
                        " AND ProductID = @ProductID";

                    updated = DapperHelper.Exceute(sql, new
                    {
                        CustomerID = CurrentUser.CustomerID,
                        ProductID = PageTypes.NOTIFICATIONS_PAGE
                    }) > 0;

                    // If this is a accept request (WALLTE TRANSFER) action
                    var formId = -1;
                    if (type == "ACCEPT" || type == "REJECT")
                    {
                        //GET AMOUNT
                        var formData = (await DapperHelper.QueryAsync<Tuple<int, int, string, int>>($@"
                        SELECT cst.ID AS Item1, cf.CAID AS Item2, cf.SwiftCode + ' ' + dbo.UFN_Rounder(cld.CLIId, cf.[Decimal]) AS Item3, cst.ContactID AS Item4
                        FROM dbo.CustomerServicesTbl ntf
                        INNER JOIN dbo.CustomerServicesTbl cst ON cst.ID = ntf.Par1
                        INNER JOIN dbo.ChooseListDetailsTbl cld ON cld.TableId = cst.ID
                        INNER JOIN dbo.vw_CustomerFile cf ON cf.CAID = cst.CAID
                        WHERE ntf.ID = @TableId AND cld.CLCatId = @CLCatId",
                            new
                            {
                                TableId = decIds[0],
                                CLCatId = ControlTypes.AMOUNT
                            })).FirstOrDefault();

                        //GET CSID
                        formId = formData.Item1;

                        //GET CAID
                        var toCAID = formData.Item2;

                        //GET AMOUNT
                        var sAmount = formData.Item3;

                        //GET CONTACTID
                        var contactId = formData.Item4;

                        if (type == "REJECT")
                        {
                            // Send a rejection notification to the requestor only
                            if (CurrentUser.ContactID != contactId)
                            {
                                var i = await DapperHelper.ExceuteAsync(
                                    "dbo.usp_SendReceiptGeneralProcessRequestMoneyNotificationWallet @ToCAID = @ToCAID, @ToAmount = @ToAmount, @FormID = @FormID, @ReferenceNumber = @ReferenceNumber, @RequesteeContactId = @RequesteeContactId, @IsRejected = 1",
                                    new
                                    {
                                        ToCAID = toCAID,
                                        ToAmount = sAmount,
                                        FormID = PageTypes.WALLET_REQUEST_MONEY,
                                        ReferenceNumber = formId,
                                        RequesteeContactId = CurrentUser.ContactID
                                    }
                                );
                            }

                            //response for Inactivate the selected notifications
                            response = new ResponseViewModel()
                            {
                                Status = NotificationType.SUCCESS,
                                Message = "TransactionRejected"
                            };
                        }
                        return Ok(BaseResponse<ResponseViewModel>.Success(response));
                    }
                }
                else
                {
                    // Otherwise, treat the action as read
                    var sql =
                        " UPDATE cld" +
                       $" SET cld.CLIId = {PickerValues.YES}" +
                        " FROM dbo.CustomerServicesTbl cst" +
                        " INNER JOIN  dbo.ChooseListDetailsTbl cld ON cst.ID = cld.TableId" +
                       $" WHERE cst.ID IN ({string.Join(",", decIds)})" +
                        " AND cld.CLCatId = @CLCatId" +
                        " AND cst.ContactId = @ContactId";

                    // Update the read status for the selected notifications
                    updated = await DapperHelper.ExceuteAsync(sql, new
                    {
                        CLCatId = ControlTypes.READ,
                        ContactId = CurrentUser.ContactID
                    }) > 0;
                }

                // Get the updated notifications count
                var counts = await _formRepository.GetNotificationsCount(CurrentUser.CustomerID + "", CurrentUser.ContactID + "");
                var unreadCount = counts["UNREAD"];

                // Return the results back
                var res = new
                {
                    success = updated,
                    unreadCount
                };

                return Ok(BaseResponse<dynamic>.Success(res));
            }
            catch (Exception ex)
            {
                // If this is a accept request (WALLTE TRANSFER) action
                if (type == "ACCEPT" || type == "REJECT")
                {
                    return Ok(new ResponseViewModel()
                    {
                        Status = NotificationType.ERROR,
                        Message = "ErrorOccurred"
                    });
                }
            }

            // Something went wrong
            return Ok(BaseResponse<string>.Failure());
        }

        [Authorize]
        [HttpPost("SingleNotification")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<Notification>))]
        public async Task<IActionResult> SingleNotification([Required]int? id)
        {
            if (id != null)
            {
                #region Get the notification
                // Prepare the procedure parameters
                var parameters = new
                {
                    // English language
                    Lang = 1,
                    // Notifications forms
                    ProductId = PageTypes.NOTIFICATIONS_PAGE,
                    // Only show the current user's data
                    InternalWhereStmt = $" AND cst.ContactID = {CurrentUser.ContactID} AND cst.ID={id}",
                    // Include only active items
                    OnlyActive = '1',
                    // Order the notifications by time in descending order
                    OrderStmt =
                        " ORDER BY CASE" +
                            " WHEN ISDATE([Application Date]) = 1" +
                            " THEN CONVERT(DATETIME, [Application Date])" +
                            " ELSE NULL" +
                        " END DESC",
                    // Only return the given columns
                    OutputColumns =
                        " ID, [Application Date] AS Date, [Content]," +
                        " [Type], CONVERT(BIT, CASE WHEN [Read] = 'No' THEN 0 ELSE 1 END) AS IsRead"
                };


                // Exceute the procedure using the given parameters                
                var notificationsQuery = await DapperHelper.QueryAsync<Notification>(
                    "dbo.Usp_CVRMRPT_ServiceRequests",
                    parameters,
                    commandType: CommandType.StoredProcedure
                );
                return Ok(BaseResponse<List<Notification>>.Success(notificationsQuery.ToList()));
                #endregion
            }
            return Ok(BaseResponse<List<Notification>>.Failure());
        }

        [Authorize]
        [HttpPost("Notifications")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<NotificationGroup>))]
        public async Task<IActionResult> Notifications(int? id)
        {
            // Get the notification groups
            var groupsQuery =
                await DapperHelper.QueryAsync<NotificationGroup>(
                    $" SELECT CLI.CLIId AS Id, {LanguageHelper.GetSelectStmt("cli.CLNameE", "Title", 1)}" +
                    " FROM dbo.ChooseListCategoryItemTbl CLCI" +
                    " INNER JOIN dbo.ChooseListItemTbl CLI ON CLI.CLIId = CLCI.CLIId" +
                    LanguageHelper.GetLeftJoin("ChooseListItemTbl", "CLNameE", "cli.CLIId", 1) +
                    " WHERE CLCI.CLCatId = @CLCatId AND CLCI.CatMapID = @CatMapID AND CLI.Active=1",
                    new
                    {
                        CLCatId = ControlTypes.TYPE,
                        CatMapID = PageTypes.NOTIFICATIONS_PAGE
                    }
            );
            return Ok(BaseResponse<List<NotificationGroup>>.Success(groupsQuery.ToList()));
        }

        [HttpGet("SendShuftiproRequest")]
        public async Task<IActionResult> SendShuftiproRequest()
        {
            return Ok();
            /*Log.Information($"[SendShuftiproRequest Entered]");

            var reference = TempData["ShuftiproRef"] + "";
            var verificationUrl = TempData["ShuftiproUrl"] + "";
            if (!string.IsNullOrWhiteSpace(reference))
            {
                Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Got ShuftiPro resume redirect endpoint: {verificationUrl}");

                try
                {
                    // Try to use the previously generated reference to allow the user to resume their application
                    Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Fetching the request status...");
                    var status = await GetShuftiproStatusRequest(reference);
                    Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Fetched the request status: {status.Event}");
                    if (status.Event == ShuftiProEvent.RequestPending && !string.IsNullOrWhiteSpace(verificationUrl))
                    {
                        Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Request is valid, trying to resume");
                        ViewBag.Step = 000;
                        TempData["verification_url"] = verificationUrl;
                        TempData["CreateAccountStep"] = 000;
                        return Ok("successPending " + verificationUrl);
                    }
                    else if (status.Event == ShuftiProEvent.VerificationAccepted || status.Event == ShuftiProEvent.VerificationDeclined)
                    {
                        Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Request has already been processed, complete the process on the app");
                        ViewBag.Step = 2;
                        TempData["CreateAccountStep"] = 2;
                        TempData["ShuftiproRef"] = reference;
                        return Ok("redirectToAction 2");
                    }
                }
                catch (Exception ex)
                {
                    Log.Error($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Failed to resume the ShuftiPro request", ex);
                }
            }
            Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Generating a new reference number...");
            reference = Guid.NewGuid().ToString("N");
            Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Generated a new reference number: {reference}");

            //const JsonRequestBehavior behaviuor = JsonRequestBehavior.AllowGet;

            // Initialize the output data
            var responseContent = "";
            var client_id = ShuftiProClientID;
            var RestApiKey = ShuftiProSecretKey;

            // Check if the process is for retail customer or not
            var isRetailCustomer = false;
            bool.TryParse(HttpContext.Session.GetString("IsRetailCustomer"), out isRetailCustomer);

            try
            {
                // Create a request to the API provider
                var request = WebRequest.Create("https://api.shuftipro.com/") as HttpWebRequest;
                if (request == null)
                {
                    return Json("false");
                }

                // Set up the request parameters
                request.KeepAlive = true;
                request.Method = "POST";
                request.ContentType = "application/json; charset=utf-8";

                var plainTextBytes = System.Text.Encoding.UTF8.GetBytes((client_id + ":" + RestApiKey));
                var api = System.Convert.ToBase64String(plainTextBytes);

                // Add the authorization key
                request.Headers.Add("authorization", $"Basic {api}");

                // Create the request data object
                // var serializer = new JavaScriptSerializer();
                var callback_url = Config.GetStringValue("ShuftiProCallBackURL").Replace("{lang}", LanguageHelper.GetCurrentLangCode() + "") ?? "";
                var redirect_url = Config.GetStringValue("ShuftiProRedirectURL").Replace("{lang}", LanguageHelper.GetCurrentLangCode() + "").Replace("{reference}", TempData["ShuftiproRef"] + "") ?? "";

                var updated = DapperHelper.Exceute("UPDATE dbo.ContactTbl SET EntertainerCode = @ShuftiRef WHERE Cont_CPR = @ContactCPR", new { ContactCPR = HttpContext.Session.GetString("ApplyNowCPRNumber"), ShuftiRef = reference }) > 0;
                if (!updated)
                {
                    Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Failed to save the shuftipro reference in contact file");
                    // this.AddNotification(Resources.Global.UnexpectedError, NotificationType.ERROR);
                    // return RedirectToAction("CreateAccount", new { id = isRetailCustomer ? 1 : 4 });
                    return Ok(NotificationType.ERROR);
                }

                // Fetch the form details
                var formData = isRetailCustomer ? null : DapperHelper.Query<CustomerServicesTbl>($@"
                        SELECT cst.* 
                        FROM dbo.CustomerServicesTbl cst
                        INNER	JOIN dbo.ContactTbl ct ON ct.ContactID = cst.ContactID
                        WHERE ct.Cont_CPR = @ContactCPR AND cst.Active = 1 AND cst.ProductId = {PageTypes.EMPLOYEE_KYC}
                        ORDER BY DateModified DESC", new
                {
                    ContactCPR = HttpContext.Session.GetString("ApplyNowCPRNumber")
                }).FirstOrDefault();
                var contId = 0;
                var recId = 0;
                var formId = 0;

                if (formData != null)
                {
                    contId = formData.ContactID ?? 0;
                    recId = formData.RecID ?? 0;
                    formId = formData.ID;
                }

                var formRepo = await _formRepository.GetFormById(PageTypes.EMPLOYEE_KYC, contId, recId, formId, true, null, false, true);
                var form = isRetailCustomer ? null : formRepo;

                // prepare params
                var name = isRetailCustomer ? "" : form.FirstOrDefault(r => r.ID == ControlTypes.WPS_KYC_FULL_NAME).Value;
                DateTime dob = DateTime.MinValue;
                if (!isRetailCustomer) DateTime.TryParseExact(form.FirstOrDefault(r => r.ID == ControlTypes.WPS_KYC_DOB).Value, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out dob);
                var address = isRetailCustomer ? "" : form.FirstOrDefault(r => r.ID == ControlTypes.WPS_KYC_ADDRESS).Value;
                DateTime cprExpiry = DateTime.MinValue;
                if (!isRetailCustomer) DateTime.TryParseExact(form.FirstOrDefault(r => r.ID == ControlTypes.WPS_KYC_CPR_EXPIRY).Value, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out cprExpiry);

                TempData["ShuftiproRef"] = reference;
                dynamic requestData = new ExpandoObject();
                requestData.reference = reference;
                requestData.callback_url = "";
                requestData.redirect_url = redirect_url;
                requestData.ttl = Config.GetStringValue("ShuftiProRequestTimeout") ?? "60";
                requestData.country = "";
                try
                {
                    requestData.language = LanguageHelper.GetCurrentLangCode().ToUpper();
                }
                catch (Exception ex)
                {
                    Log.Error($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Language Error", ex);
                    requestData.language = "EN";
                }
                requestData.verification_mode = "image_only";
                requestData.face = new
                {
                    proof = "",
                    allow_offline = "0"
                };
                requestData.document = new
                {
                    supported_types = new string[] { "id_card" },
                    name = new
                    {
                        full_name = name,
                        fuzzy_match = "1"
                    },
                    proof = "",
                    additional_proof = "",
                    dob = isRetailCustomer ? "" : dob.ToString("yyyy-MM-dd"),
                    expiry_date = isRetailCustomer ? "" : cprExpiry.ToString("yyyy-MM-dd"),
                    document_number = HttpContext.Session.GetString("ApplyNowCPRNumber"),
                    allow_offline = "1",
                    allow_online = "1",
                    fetch_enhanced_data = "1",
                    backside_proof_required = "1"

                };

                if (isRetailCustomer)
                {
                    requestData.document_two = new
                    {
                        supported_types = new string[] { "passport" },
                        name = new
                        {
                            full_name = name,
                            fuzzy_match = "1"
                        },
                        proof = "",
                        additional_proof = "",
                        dob = "",
                        issue_date = "",
                        expiry_date = "",
                        document_number = "",
                        allow_offline = "1",
                        allow_online = "0",
                        fetch_enhanced_data = "1"
                    };
                }

                requestData.show_results = "0";
                requestData.decline_on_single_step = "0";
                requestData.allow_retry = "1";

                if ((HttpContext.Session.GetString("KYCNeedAddressProof") + "").ToUpper() != "FALSE")
                {
                    requestData.address = new
                    {
                        supported_types = new string[] {
                                "cpr_smart_card_reader_copy",
                                "utility_bill",
                                "driving_license"
                        },
                        full_address = address,
                        address_fuzzy_match = "1"
                    };
                }
                HttpContext.Session.Remove("KYCNeedAddressProof");

                // Serialize the object and post it to the API
                var param = JsonConvert.SerializeObject(requestData);
                Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] requestContent:{param}");
                var byteArray = Encoding.UTF8.GetBytes(param);
                using (var writer = request.GetRequestStream())
                {
                    writer.Write(byteArray, 0, byteArray.Length);
                }

                // Get the provider's response
                using (var response = request.GetResponse() as HttpWebResponse)
                {
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        responseContent = reader.ReaViewModelEnd();
                    }
                }

                Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] responseContent:{responseContent}");

                // Ensure that we have a response
                if (string.IsNullOrWhiteSpace(responseContent))
                {
                    return Ok("Failed, no response from our verification vendor.");
                }

                // All good! The response contains a "verification_url" tag.
                if (responseContent.Contains("verification_url"))
                {
                    var x = JsonConvert.DeserializeObject(responseContent);
                    //  js = new JavaScriptSerializer();
                    ShuftiVerficationResponse resObject = JsonConvert.DeserializeObject<ShuftiVerficationResponse>(responseContent);

                    if (!string.IsNullOrWhiteSpace(resObject.verification_url))
                    {
                        Log.Information($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Got redirect endpoint: {resObject.verification_url}");
                        ViewBag.Step = 000;
                        TempData["verification_url"] = resObject.verification_url;

                        // Store the verification url for resume ability
                        DapperHelper.Exceute("UPDATE dbo.ContactTbl SET EntertainerAccess = @ShuftiUrl WHERE Cont_CPR = @ContactCPR", new { ContactCPR = HttpContext.Session.GetString("ApplyNowCPRNumber"), ShuftiUrl = resObject.verification_url });

                        //return Redirect(resObject["verification_url"]);
                        return Ok("success " + resObject.verification_url);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[Account/SendShuftiproRequest/{HttpContext.Session.GetString("ApplyNowCPRNumber")}/{reference}] Error", ex);
                ViewBag.ErrorTitle = GlobalLocalizer["Error"];
                ViewBag.ErrorDesc = GlobalLocalizer["ErrorOccurred"];
                return View("Error");
            }

            TempData["CreateAccountStep"] = 000;

            // ?
            return RedirectToAction("CreateAccount", new { id = 000 });*/
        }

        [HttpPost("IsFingerprintEnabled")]
        public IActionResult IsFingerprintEnabled(string fingerprintHash)
        {
            try
            {
                // Make sure that we have data
                if (string.IsNullOrWhiteSpace(fingerprintHash))
                {
                    return Ok(BaseResponse<dynamic>.Failure(new
                    {
                        success = false
                    }));
                }

                // Decrypt the fingerprint hash
                var decHash = GeneralHelper.Decrypt(fingerprintHash, "FINGERPRINT");

                // Compare it to the username of the currently logged in user
                var result = decHash == CurrentUser.Username;

                // Return the results back
                return Ok(BaseResponse<dynamic>.Success(new
                {
                    success = false
                }));
            }
            catch (Exception ex)
            {
                Logger.LogError($"{{IP}}[Account][IsFingerprintEnabled]/Error"+ex.Message);
            }

            // Something went wrong
            return Ok(BaseResponse<dynamic>.Failure(new
            {
                success = false
            }));
        }

        [HttpPost("CheckUpdate")]
        [AllowAnonymous]
        public IActionResult CheckUpdate(string version, string deviceType)
        {
            Logger.LogInformation($"IP{{IP}}/User{{User}}/[Account]/CheckUpdate/{version},{deviceType}");
            // Initialize the results object
            var result = new Result
            {
                status = "UNSPECIFIED_ERROR",
                version = ""
            };

            try
            {
                // Make sure that we have proper data
                if (string.IsNullOrWhiteSpace(version) || string.IsNullOrWhiteSpace(deviceType))
                {
                    result.status = "DATA_ERROR";
                    return Ok(BaseResponse<Result>.Failure(result));
                }

                // Fetch the latest version
                var latestVersion = Config.GetStringValue("Latest" + deviceType.ToUpper() + "Version");
                if (string.IsNullOrWhiteSpace(latestVersion))
                {
                    latestVersion = "-1";
                }
                if (latestVersion == "-1")
                {
                    result.status = "VERSION_ERROR";
                    return Ok(BaseResponse<Result>.Failure(result));
                }

                // Set the version result
                result.version = latestVersion;

                // Parse the version string into decimals 
                var latestVersionDecimal = 0m;
                var currentVersionDecimal = 0m;
                decimal.TryParse(latestVersion.Replace(".", ""), out latestVersionDecimal);
                decimal.TryParse(version.Replace(".", ""), out currentVersionDecimal);

                // If the current version is older than the latest, indicate that an update is required
                if (currentVersionDecimal < latestVersionDecimal)
                {
                    result.status = "UPDATE_REQUIRED";
                    return Ok(BaseResponse<Result>.Success(result));
                }
                result.status = "NO_UPDATE_REQUIRED";
            }
            catch (Exception ex)
            {

            }
            return Ok(BaseResponse<Result>.Success(result));
        }

        [HttpPost("RegisterDevice")]
        [AllowAnonymous]
        public async Task<IActionResult> RegisterDevice([FromBody] RegisterDeviceRequest registerDeviceRequest)
        {
            Logger.LogInformation($"IP{{IP}}/User{{User}}/[Account]/RegisterDevice/{registerDeviceRequest}");
            try
            {
                var username = registerDeviceRequest.username;
                var userId = registerDeviceRequest.userId;
                var regId = registerDeviceRequest.regId;
                var deviceType = registerDeviceRequest.deviceType;

                // Decrypt the submitted username
                username = username.Decrypt("USERNAME");

                // Make sure that we have proper data
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(userId) || string.IsNullOrWhiteSpace(regId))
                {
                    return Ok(BaseResponse<bool>.Failure());
                }

                // Determine the device id
                var deviceId = -1;
                switch (deviceType?.ToUpper())
                {
                    case "ANDROID":
                        deviceId = 1;
                        break;
                    case "IOS":
                        deviceId = 2;
                        break;
                    case "WINDOWS":
                        deviceId = 3;
                        break;
                    default:
                        deviceId = 0;
                        break;
                }

                // Check if we have any mobile users with the given data
                var data = await DapperHelper.QueryAsync<MobileUsersTbl>(
                    " SELECT *" +
                    " FROM dbo.MobileUsersTbl" +
                    " WHERE userId = @UserId OR signal_token = @SignalToken OR mobile_token = @MobileToken",
                    new
                    {
                        UserId = username,
                        SignalToken = userId,
                        MobileToken = regId
                    }
                );
                var mobileUsers = data.ToList();
                if (mobileUsers.Any())
                {
                    // Fetch the first record
                    var user = mobileUsers.First();

                    // If there are multiple records with same data, keep only 1 record
                    var count = mobileUsers.Count;
                    if (count > 1)
                    {
                        // Remove the other records
                        var idsToDelete = mobileUsers.Where(r => r.id != user.id).Select(r => r.id).ToList();
                        await DapperHelper.ExceuteAsync(
                            $"DELETE FROM dbo.MobileUsersTbl WHERE id IN ({string.Join(",", idsToDelete)})"
                        );
                    }

                    // Update the record with the new data
                    await DapperHelper.ExceuteAsync(
                        " UPDATE dbo.MobileUsersTbl" +
                        " SET userId = @UserId, signal_token = @SignalToken, mobile_token = @MobileToken," +
                        "     dev_type = @DevType, updated_at = @UpdatedAt" +
                        " WHERE id = @Id",
                        new
                        {
                            UserId = username,
                            SignalToken = userId,
                            MobileToken = regId,
                            DevType = deviceId,
                            UpdatedAt = DateTime.Now,
                            Id = user.id
                        }
                    );
                }
                else
                {
                    // Add them to the list
                    await DapperHelper.ExceuteAsync(
                        " INSERT INTO dbo.MobileUsersTbl (userId, mobile_token, created_at, dev_type, signal_token, updated_at)" +
                        " VALUES (@UserId, @MobileToken, @CreatedAt, @DevType, @SignalToken, NULL)",
                        new
                        {
                            UserId = username,
                            SignalToken = userId,
                            MobileToken = regId,
                            DevType = deviceId,
                            CreatedAt = DateTime.Now
                        }
                    );
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"{{IP}}[Account][RegisterDevice]/Error{ex.Message}");
                return Ok(BaseResponse<bool>.Failure());                
            }

            // Return the results
            return Ok(BaseResponse<bool>.Success());
        }

        [HttpGet("GetCultureStrings")]
        [AllowAnonymous]
        public async Task<IActionResult> GetCultureStrings()
        {
            try
            {
                var data = new
                {
                    accountLocalizer = await Task.Run(() => _accountLocalizer.GetAllStrings().Select(a => new { a.Name, a.Value }).ToList()),
                    servicesLocalizer = await Task.Run(() => _servicesLocalizer.GetAllStrings().Select(a => new { a.Name, a.Value }).ToList()),
                    globalLocalizer = await Task.Run(() =>  _globalLocalizer.GetAllStrings().Select(a => new { a.Name, a.Value }).ToList())
                };
                return Ok(BaseResponse<dynamic>.Success(data));
            }
            catch(Exception ex)
            {
                Logger.LogError($"[Account]/GetCultureStrings/{ex.Message}");
            }
            return NotFound();
        }

        [HttpPost("GetEncryptedValue")]
        [Authorize]
        public async Task<IActionResult> GetEncryptedValue([Required]string data, [Required] string purpose)
        {
            Logger.LogInformation($"IP{{IP}}/User{{User}}/[Account]/GetEncryptedValue/{data},{purpose}");
            if (data.Length > 0 && purpose.Length > 0)
            {
                return Ok(BaseResponse<string>.Success(await Task.Run(() => data.Encrypt(purpose))));
            }
            else
            { 
                return BadRequest(BaseResponse<string>.Failure());
            }
        }
    }
}
