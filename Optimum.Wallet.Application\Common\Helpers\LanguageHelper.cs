using System.Globalization;

namespace Optimum.Wallet.Application.Common.Helpers
{
    public static class LanguageHelper
    {
        /*public static MvcHtmlString LangSwitcher(this UrlHelper url, string Name, RouteData routeData, string lang)
        {
            var liTagBuilder = new TagBuilder("li");
            var aTagBuilder = new TagBuilder("a");
            var routeValueDictionary = new RouteValueDictionary(routeData.Values);
            if (routeValueDictionary.ContainsKey("lang"))
            {
                if (routeData.Values["lang"] as string == lang)
                {
                    liTagBuilder.AddCssClass("active");
                }
                else
                {
                    routeValueDictionary["lang"] = lang;
                }
            }
            aTagBuilder.MergeAttribute("href", url.RouteUrl(routeValueDictionary));
            aTagBuilder.MergeAttribute("lang", lang);
            aTagBuilder.SetInnerText(Name);
            liTagBuilder.InnerHtml = aTagBuilder.ToString();
            return new MvcHtmlString(liTagBuilder.ToString());
        }
        */
        public static string GetCurrentLangCode()
        {
            return Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName.ToLower();
        }

        public static CultureInfo GetCurrentCulture
        {
            get { return Thread.CurrentThread.CurrentCulture; }
        }

        public static bool GetCurrentCultureRTL
        {
            get { return Thread.CurrentThread.CurrentCulture.TextInfo.IsRightToLeft; }
        }

        public static string GetLeftJoin(string tableName, string columnName, string joinIdColumn, int index = 0)
        {
            var leftJoin = "";
            leftJoin = " LEFT JOIN "
                        + " ( "
                        + " SELECT D.RefTableId, D.TextField FROM dbo.Web_MultiLanguageHeaderTbl H "
                        + " INNER JOIN dbo.Web_MultiLanguageDetailsTbl D ON D.MLHID = H.MLHID "
                        + $" INNER JOIN dbo.web_Langtbl L ON L.ID = D.LangId AND L.LangCode ='{GetCurrentLangCode()}' "
                        + $" WHERE D.Active = 1 AND H.Active = 1 AND H.TableName = '{tableName}' AND H.ColumnName = '{columnName}' "
                        + $" ) ML{index} ON ML{index}.RefTableId = {joinIdColumn}";

            return leftJoin;
        }

        public static string GetSelectStmt(string mainColumnName, string mainColumnElias, int index = 0)
        {
            var stmt = $"CASE WHEN ISNULL(ML{index}.TextField,'') !='' THEN ML{index}.TextField ELSE {mainColumnName} END ";

            if (!string.IsNullOrWhiteSpace(mainColumnElias))
            {
                stmt += $" AS '{mainColumnElias}'";
            }

            return stmt;
        }


    }
}