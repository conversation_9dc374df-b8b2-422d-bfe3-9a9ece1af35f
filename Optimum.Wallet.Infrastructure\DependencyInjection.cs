using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Application.Interfaces;
using Optimum.Wallet.Application.Interfaces.Repositories;
using Optimum.Wallet.Core.Interfaces;
using Optimum.Wallet.Infrastructure.Data.Context;
using Optimum.Wallet.Infrastructure.Repository;
using Optimum.Wallet.Infrastructure.Services;

namespace Optimum.Wallet.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Register DbContext
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseSqlServer(Config.GetConnectionString("Utilities"));
            });

            // Register Repositories
            services.AddScoped<ICardRepository, CardRepository>();
            services.AddScoped<IWalletRepository, WalletRepository>();
            services.AddScoped<IFormRepository, FormRepository>();
            services.AddScoped<ITokenRequestAsync, TokenRequestAsync>();
            services.AddScoped(typeof(IGenericRepositoryAsync<>), typeof(GenericRepositoryAsync<>));

            // Register Infrastructure Services
            // TODO: Fix CrediAppService interface implementation
            // services.AddScoped<ICrediAppService, CrediAppService>();
            services.AddScoped<IWebServices, WebServices>();
            
            return services;
        }
    }
}
