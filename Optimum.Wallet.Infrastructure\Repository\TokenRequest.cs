﻿using Microsoft.EntityFrameworkCore;
using Optimum.Wallet.Infrastructure.Data.Context;
using Optimum.Wallet.Core.Interfaces;
using Optimum.Wallet.Domain.Entities;

namespace Optimum.Wallet.Infrastructure.Repository
{
    public class TokenRequestAsync : GenericRepositoryAsync<TokenRequests>,ITokenRequestAsync
    {
        private readonly DbSet<TokenRequests> _tokenrequest;

        public TokenRequestAsync(ApplicationDbContext dbContext) : base(dbContext)
        {
            _tokenrequest = dbContext.Set<TokenRequests>();
        }
    }
}
