namespace Optimum.Wallet.Application.Common.Models
{
    public class ResponseViewModel
    {
        public string Message { get; set; }
        public string Status { get; set; }
        public string TransactionNumber { get; set; }
        public string TransactionMessage { get; set; }
        public string AuthorizationNumber { get; set; }
        public string ReferenceNumber { get; set; }
        public List<RequestFormField> Fields { get; set; }
        public string SubmitUrl { get; set; }
        public PaymentSubmitModel PaymentData { get; set; }
        public bool approved { get; set; }
    }

    public class PaymentSubmitModel
    {
        public string PaymentID { get; set; }
        public string PaymentLink { get; set; }
        public decimal TranAmount { get; set; }
        public decimal TranTargetAmount { get; set; }
        public int TranFormId { get; set; }
        public int TranFormTableId { get; set; }
        public int TranCAID { get; set; }
        public string PayTranType { get; set; }
    }

    public class WalletQuoteRequest
    {
        public string sCAID{ get; set; }
        public string dCAID { get; set; }
        public decimal amount { get; set; }
        public bool isSourceAmount { get; set; }
        public bool confirmTransfer { get; set; }
    }

    public class WalletQuoteResponse
    {
        public bool success { get; set; }
        public string sourceAmount { get; set; }
        public string targetAmount { get; set; }
        public string exchangeRate { get; set; }
        public string exchangeRateReverse { get; set; }
        public string fees { get; set; }
        public string vat { get; set; }
        public string feesWithVat { get; set; }
        public string totalToPay { get; set; }
    }

    public class CardBalanceResponse
    {
        public string HashedCAID { get; set; }
        public string AvailableBalance { get; set; }
    }
}
