using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Application.Interfaces;
using Optimum.Wallet.Infrastructure.CrediAppWS;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Optimum.Wallet.Infrastructure.Services
{
    /// <summary>
    /// Implementation of the ICrediAppService interface using the generated WCF client
    /// </summary>
    public class CrediAppService : ICrediAppService
    {
        private readonly ILogger<CrediAppService> _logger;
        private readonly CrediAppServiceSoapClient _client;

        public CrediAppService(ILogger<CrediAppService> logger)
        {
            _logger = logger;
            _client = new CrediAppServiceSoapClient(CrediAppServiceSoapClient.EndpointConfiguration.CrediAppServiceSoap);
        }

        public async Task<List<CardDetailsViewModel>> F4_GetCardListAsync(string pCpr)
        {
            _logger.LogDebug($"[CrediAppService/F4_GetCardList/{pCpr}] Entered.");
            var result = new List<CardDetailsViewModel>();

            try
            {
                var response = await _client.F4_GetCardListAsync(pCpr);
                result = JsonConvert.DeserializeObject<List<CardDetailsViewModel>>(response.Body.F4_GetCardListResult);
            }
            catch (System.Exception ex)
            {
                _logger.LogError($"[CrediAppService/F4_GetCardList/{pCpr}] Failed to get the card List.", ex);
            }
            return result;
        }

        public async Task<CardBalance> F5_GetBalanceAsync(string pCpr, string pCardNumber, string mainCardBalanceJson)
        {
            _logger.LogDebug($"[CrediAppService/F5_GetBalance/{pCpr}/[{pCardNumber}]] Entered.");
            var balance = new CardBalance();

            try
            {
                var response = await _client.F5_GetBalanceAsync(pCpr, pCardNumber, mainCardBalanceJson);
                balance = JsonConvert.DeserializeObject<CardBalance>(response.Body.F5_GetBalanceResult);
            }
            catch (System.Exception ex)
            {
                _logger.LogError($"[CrediAppService/F5_GetBalance/{pCpr}/[{pCardNumber}]] Failed to get the balance.", ex);
            }
            return balance;
        }

        public async Task<Dictionary<string, string>> F6_GetStatementListAsync(string pCpr, string pCardNumber)
        {
            _logger.LogDebug($"[CrediAppService/F6_GetStatementList/{pCpr}/{pCardNumber}] Enter");
            var result = new Dictionary<string, string>();

            try
            {
                var response = await _client.F6_GetStatementListAsync(pCpr, pCardNumber);
                result = JsonConvert.DeserializeObject<Dictionary<string, string>>(response.Body.F6_GetStatementListResult);
            }
            catch (System.Exception ex)
            {
                _logger.LogError($"[CrediAppService/F6_GetStatementList/{pCpr}/{pCardNumber}] Failed to get statement list.", ex);
            }
            return result;
        }

        public async Task<List<CardStatementModelView>> F8_GetCurrentStatementTransactionAsync(string pCpr, string pCardNumber)
        {
            _logger.LogDebug($"[CrediAppService/F8_GetCurrentStatementTransaction/{pCpr}/{pCardNumber}] Enter");
            var result = new List<CardStatementModelView>();

            try
            {
                var response = await _client.F8_GetCurrentStatementTransactionAsync(pCpr, pCardNumber);
                result = JsonConvert.DeserializeObject<List<CardStatementModelView>>(response.Body.F8_GetCurrentStatementTransactionResult);
            }
            catch (System.Exception ex)
            {
                _logger.LogError($"[CrediAppService/F8_GetCurrentStatementTransaction/{pCpr}/{pCardNumber}] Failed to get current statement transaction.", ex);
            }
            return result;
        }

        public async Task<List<CardStatementModelView>> GetStatementTransactionAsync(string pCpr, string pCardNumber, string type, string statementDate, string suppCardsJson, string onlySupp)
        {
            _logger.LogDebug($"[CrediAppService/GetStatementTransaction/{pCpr}/{pCardNumber}/{statementDate}/{type}/{suppCardsJson}/{onlySupp}] Enter");
            var result = new List<CardStatementModelView>();

            try
            {
                var response = await _client.GetStatementTransactionAsync(pCpr, pCardNumber, type, statementDate, suppCardsJson, onlySupp);
                result = JsonConvert.DeserializeObject<List<CardStatementModelView>>(response.Body.GetStatementTransactionResult);
            }
            catch (System.Exception ex)
            {
                _logger.LogError($"[CrediAppService/GetStatementTransaction/{pCpr}/{pCardNumber}/{statementDate}/{type}/{suppCardsJson}/{onlySupp}] Failed to get statement transaction.", ex);
            }
            return result;
        }

        // Implement other methods similarly...

        public async Task<Dictionary<string, string>> F10_StopCard_RawAsync(string pCpr, string pCardNumber)
        {
            _logger.LogDebug($"[CrediAppService/F10_StopCard_Raw/{pCpr}/{pCardNumber}] Enter");
            var result = new Dictionary<string, string>();

            try
            {
                var response = await _client.F10_StopCard_RawAsync(pCpr, pCardNumber);
                result = JsonConvert.DeserializeObject<Dictionary<string, string>>(response.Body.F10_StopCard_RawResult);
            }
            catch (System.Exception ex)
            {
                _logger.LogError($"[CrediAppService/F10_StopCard_Raw/{pCpr}/{pCardNumber}] Failed to stop card.", ex);
            }
            return result;
        }

        // Implement remaining methods as needed for your application

        public async Task<string> F29_GetWalletsBalancesAsync(string pCpr, string pCardNumber, string mainCardBalanceJson)
        {
            _logger.LogDebug($"[CrediAppService/F29_GetWalletsBalances/{pCpr}/{pCardNumber}] Enter");
            string result = "[]";

            try
            {
                var response = await _client.F29_GetWalletsBalancesAsync(pCpr, pCardNumber, mainCardBalanceJson);
                result = response.Body.F29_GetWalletsBalancesResult;
            }
            catch (System.Exception ex)
            {
                _logger.LogError($"[CrediAppService/F29_GetWalletsBalances/{pCpr}/{pCardNumber}] Failed to get wallets balances.", ex);
            }
            return result;
        }

        // Implement other methods as needed...

        public Task<Dictionary<string, string>> F13_PayMyCardPayment_RawAsync(string pCpr, string pCardNumber, string pAmount, string pCurrency)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<Dictionary<string, string>> External_Transfer_Funds_RawAsync(string pCpr, string pCardNumber, string pAmount, string pCurrency, string pBeneficiary)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<Dictionary<string, string>> F16A_ActivatEstatement_RawAsync(string pCpr, string pCardNumber, string pEmail)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<Dictionary<string, string>> F18_UpdateMobileNumber_RawAsync(string pCpr, string pCardNumber, string pMobile)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<Dictionary<string, string>> F19_UpdateEmail_RawAsync(string pCpr, string pCardNumber, string pEmail)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<PersonalData> F25_GetPersonalData_RawAsync(string pCpr)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<VerifyUser> F27_CardHolderVerification_RawAsync(string _sCardEmbossedName, string _sCardLast4Digit, string pCpr, string pMobile, string valdationFlag)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<string> F30_VerifyTransferAmountAsync(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<string> F31_ConfirmTransferAmountAsync(string pCpr, string pCardNumber, string sourceWallet, string destinationWallet, decimal transferAmount, string transferCurrency)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<string> GetWalletStatementTransactionAsync(string pCpr, string pCardNumber, string type, string statementDate, string Currency)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<string> F23_LoyFppProgAsync(string pCpr)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<string> F24_LoyThameenAsync(string pCpr)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }

        public Task<string> RunWebServiceAsync(string functionName, string parameters)
        {
            throw new System.NotImplementedException("Method not yet implemented");
        }
    }
}
