using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.StaticFiles;
// using Optimum.Wallet.Api.Extensions; // TODO: Move required extension methods to Application if needed.
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Extensions;

namespace Optimum.Wallet.Application.Common.Helpers
{
    // Based on http://stackoverflow.com/a/18251883
    public class FileUploadHelper
    {
        public static bool IsValid(IFormFile file, int maxFileSize = 2)
        {
            var isValid = false;

            if (file == null || file.Length > maxFileSize * 1024 * 1024)
            {
                return isValid;
            }

            if (IsOneOfValidFormats(GeneralHelper.GetMimeType(file.FileName))
                && IsOneOfValidExtensions(Path.GetExtension(file.FileName)))
            {
                isValid = true;
            }

            return isValid;
        }

        private static bool IsOneOfValidFormats(string mimeType)
        {
            var formats = GetValidFormats();
            return formats.Contains(mimeType.ToLower());
        }

        private static bool IsOneOfValidExtensions(string extension)
        {
            var extensions = GetValidExtensions();
            return extensions.Contains(extension.ToLower());
        }

        private static IEnumerable<string> GetValidFormats()
        {
            var formats = new List<string>
            {
                "image/bmp",
                "image/gif",
                "image/jpeg",
                "image/png",
                "application/pdf",
                "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            };

            return formats;
        }

        private static IEnumerable<string> GetValidExtensions()
        {
            var extensions = new List<string>
            {
                ".bmp",
                ".gif",
                ".jpg",
                ".jpeg",
                ".jpe",
                ".png",
                ".pdf",
                ".doc",
                ".docx"
            };

            return extensions;
        }

        public static ResponseViewModel uploadFile(string prefix, int PkId, string appKeyFolder, string inputName, IFormFile formFile)
        {
            ResponseViewModel response = new ResponseViewModel() { Status = NotificationType.ERROR };

            //Get the photo
            try
            {
                if (formFile != null)
                {
                    // Get the uploaded files
                    var file = formFile;

                    if (file.Length <= 0)
                    {
                        response.Message = "NoFileFound";
                        return response;
                    }

                    // Make sure that the file is valid
                    if (!FileUploadHelper.IsValid(file))
                    {
                        //TODO:Add return message
                        response.Message = "File is invalid";
                        return response;
                    }

                    // Get the current field's value
                    var fieldValue = file.FileName;

                    // Get the file extension
                    var extension = Path.GetExtension(file.FileName)?.ToLower();

                    // Set the file name
                    var fileName = $"{prefix}{PkId}{extension}";
                    response.ReferenceNumber = fileName;

                    // Save the file to disk
                    var appFolder = WalletApplication.AppFolder;
                    if (new List<string> { "ContactPhotoFolder", "AccountPhotoFolder", "CommentPhotoFolder" }.Contains(appKeyFolder))
                    {
                        appFolder = WalletApplication.MobileAppFolder;
                    }

                    var accountFolder = Config.GetStringValue(appKeyFolder);
                    var path = Path.Combine(appFolder, accountFolder, fileName);
                    using (Stream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write))
                    {
                        file.CopyTo(fileStream);
                    }
                    response.Status = NotificationType.SUCCESS;
                }
            }
            catch (Exception ex)
            {
                response.Status = NotificationType.ERROR;
                //TODO:Add return message
            }

            return response;
        }

    }
}