using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Optimum.Wallet.Infrastructure.Repository;
using Optimum.Wallet.Application.Common;
using Optimum.Wallet.Application.Common.Helpers;
using Optimum.Wallet.Core.Constants;
using Optimum.Wallet.Application.Common.Models;
using Optimum.Wallet.Core.Extensions;
using Optimum.Wallet.Application.Interfaces.Repositories;
using Optimum.Wallet.Application.Interfaces;
using Optimum.Wallet.Domain.Entities;
using StandardizedQR;
using System.ComponentModel.DataAnnotations;
using System.Drawing;
using System.Dynamic;

namespace Optimum.Wallet.Api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    public class WalletActionsController : BaseController<WalletActionsController>
    {
        private readonly IStringLocalizer<WalletActionsController> _loc;
        private readonly ICardRepository _cardRepository;
        private readonly IWalletRepository _walletRepository;
        private readonly IFormRepository _formRepository;
        private readonly CacheHelper _cacheHelper;

        public WalletActionsController(IStringLocalizer<WalletActionsController> stringLocalizer,
            ICardRepository cardRepository,
            CacheHelper cacheHelper, IWalletRepository walletRepository, IFormRepository formRepository)
        {
            _loc = stringLocalizer;
            _cardRepository = cardRepository;
            Misc._rootProvider = DataProtectionProvider.Create(new DirectoryInfo(@"C:\\WalletKeysExample"));
            _cacheHelper = cacheHelper;
            _walletRepository = walletRepository;
            _formRepository = formRepository;
        }

        [HttpPost("FormData")]
        public async Task<IActionResult> FormData([FromBody] WalletServiceRequest walletRequestMoney)
        {
            var sId = walletRequestMoney.sId;
            var sCAID = walletRequestMoney.sCAID;
            var sRequestId = walletRequestMoney.sRequestId;
            var isMerchantWallet = walletRequestMoney.isMerchantWallet;
            var sCard = walletRequestMoney.sCard;
            var IsLogRequest = walletRequestMoney.IsLogRequest;

            int id = 0;
            int.TryParse(sId.Decrypt("PID"), out id);

            int CAID = CurrentUser.CAID;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (!int.TryParse(sCAID.Decrypt("CAID"), out CAID))
                {
                    CAID = CurrentUser.CAID;
                }
            }

            var data = new WalletServiceResponse();
            var requestMoneyCAID = -1;
            var requestMoneyContactId = -1;
            var requestMoneyContactCPR = "";
            var requestMoneyAmount = "";
            var requestMoneyContactMobile = "";
            if (id == PageTypes.WALLET_SEND_MONEY && !string.IsNullOrWhiteSpace(sRequestId))
            {
                int notificationId = -1;
                if (!int.TryParse(sRequestId.Decrypt("NOTIFICATION_ID"), out notificationId))
                {
                    notificationId = -1;
                }

                var isSplitPaymentRequest = await DapperHelper.ExecuteScalarAsync<string>($"SELECT Par2 FROM dbo.CustomerServicesTbl WHERE ID = @NotificationId", new
                {
                    NotificationId = notificationId
                }) == "1";

                if (isSplitPaymentRequest)
                {
                    var requestDataQuery = await DapperHelper.QueryAsync<Tuple<int, int, string, decimal, string>>(@"
                        SELECT SPH.CAID AS Item1, SPH.ContactId AS Item2, CT.Cont_CPR AS Item3, SPD.Amount AS Item4, CT.ContactMobile AS Item5
                        FROM dbo.CustomerServicesTbl cst
                        INNER JOIN dbo.Wallet_SplitPaymentDetails SPD ON SPD.Id = cst.Par1
                        INNER JOIN dbo.Wallet_SplitPaymentHeader SPH ON SPH.Id = SPD.HeaderId
                        INNER JOIN ContactTbl CT ON CT.ContactID = SPH.ContactId
                        WHERE cst.ID = @NotificationId
                    ",
                    new
                    {
                        NotificationId = notificationId
                    });
                    var requestData = requestDataQuery.FirstOrDefault();

                    requestMoneyCAID = requestData.Item1;
                    requestMoneyContactId = requestData.Item2;
                    requestMoneyContactCPR = requestData.Item3;
                    requestMoneyAmount = requestData.Item4 + "";
                    requestMoneyContactMobile = requestData.Item5;
                }
                else
                {
                    var requestDataQuery = await DapperHelper.QueryAsync<ChooseListDetailsTbl>(@"
                        SELECT cld.*, frm.CAID AS CustTicktID, frm.ContactID AS TicktTranID, cnt.ContactMobile AS DataID
                        FROM dbo.CustomerServicesTbl ntf
                        INNER JOIN dbo.CustomerServicesTbl frm ON frm.ID = ntf.Par1
                        INNER JOIN dbo.ChooseListDetailsTbl cld ON frm.ID = cld.TableId
                        INNER JOIN dbo.ContactTbl cnt on cnt.ContactID = frm.ContactID
                        WHERE ntf.ID = @NotificationId
                    ",
                    new
                    {
                        NotificationId = notificationId
                    });
                    var requestData = requestDataQuery.ToList();

                    requestMoneyCAID = requestData.FirstOrDefault()?.CustTicktID ?? requestMoneyCAID;
                    requestMoneyContactId = requestData.FirstOrDefault()?.TicktTranID ?? requestMoneyContactId;
                    requestMoneyContactCPR = requestData.FirstOrDefault(r => r.CLCatId == 492)?.CLIId;
                    requestMoneyAmount = requestData.FirstOrDefault(r => r.CLCatId == ControlTypes.AMOUNT)?.CLIId;
                    requestMoneyContactMobile = requestData.FirstOrDefault()?.DataID;
                }

                data.FormId = id;
                data.FormValues = new Dictionary<int, string>
                {
                    { 499, PickerValues.PAYMENT_OPTION_OTHERS + "" },
                    { 1540, requestMoneyCAID + "" },
                    { 492,  requestMoneyContactCPR},
                    { 409, CAID + "" },
                    { ControlTypes.AMOUNT,  requestMoneyAmount},
                    { 418,  requestMoneyContactMobile}
                };
            }

            var viewOnly = false;
            var model = await _formRepository.CreateRequestFormModel(CurrentUser, id, data.FormValues, -1, viewOnly, -1, -1, IsLogRequest);

            if (id == PageTypes.WALLET_SEND_MONEY && !string.IsNullOrWhiteSpace(sRequestId))
            {
                model.Fields.FirstOrDefault(r => r.ID == ControlTypes.TARGET_WALLET).Accounts = await _cardRepository.GetWalletAccounts(requestMoneyContactId, requestMoneyCAID);
            }
            else if (id == PageTypes.WALLET_SEND_MONEY && isMerchantWallet)
            {
                model.Fields.FirstOrDefault(r => r.ID == ControlTypes.SOURCE_WALLET).Accounts = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, -1, 2);
            }

            if (id == PageTypes.WALLET_SEND_MONEY)
            {
                model.Fields.FirstOrDefault(r => r.ID == ControlTypes.CARD_NUMBER).Cards.RemoveAll(r => r.CardAccountTypeId == CustAcctSetup.DEBIT_CARD + "");
            }

            // Build a list of the card field ids
            var cardFields = new List<int>
            {
                ControlTypes.CARD_NUMBER,
                ControlTypes.SUPP_CARD_NUMBER,
                ControlTypes.MAIN_CARD_NUMBER
            };

            // Check that the request is eligible (has a valid card)
            if (!viewOnly)
            {
                var cardField = model.Fields?.FirstOrDefault(r => cardFields.Contains(r?.ID ?? -1));
                var isEligible = model.Fields != null && model.Fields.Count > 0 && (cardField == null || cardField.Cards?.Count > 0);
                if (!isEligible)
                {
                    var ErrorTitle = "Not Eligible";
                    var ErrorDesc = "None of your cards are eligible for this service.";
                    return Ok(BaseResponse<string>.Failure(new string[] { ErrorTitle, ErrorDesc }));
                }
            }

            
            data.Title = model.Title;
            data.formFields = model.Fields;
            data.SelectedCardId = sCard;
            data.FormId = id;
            data.CAID = sCAID;
            data.RequestID = sRequestId;
            data.CAIDHash = (CAID + "").HashMD5();

            return Ok(BaseResponse<WalletServiceResponse>.Success(data));
        }

        [HttpPost("SubmitForm")]
        public async Task<IActionResult> SubmitForm([FromForm]IFormCollection fc, int id, string sCAID = "", string sRequestId = "")
        {
            Logger.LogInformation($"[Request/SubmitForm/{id}][{CurrentUser.Username}] Entered {(fc == null ? "Form is NULL" : "Form has data")}");

            ResponseViewModel Response = new ResponseViewModel() { };

            string type = "SERVICES";

            // Validate the submitted data
            if (id <= 0 || fc == null || fc.Count <= 0)
            {
                Response.Message = "ErrorDataSubmitted";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            int CAID = CurrentUser.CAID;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (!int.TryParse(sCAID.Decrypt(General.CAIDEncryptPurpose), out CAID))
                {
                    CAID = CurrentUser.CAID;
                }
            }

            #region Validate Amount if the form is WALLET_SEND_MONEY OR WALLET_REQUEST_MONEY
            if (new List<int> { PageTypes.WALLET_PAY_MERCHANT, PageTypes.WALLET_SEND_MONEY, PageTypes.WALLET_REQUEST_MONEY }.Contains(id))
            {


            }
            #endregion

            #region REQUEST MONEY
            //=================================[REQUEST MONEY]=================================================
            var sAmount = fc[General.InputPrefix + ControlTypes.AMOUNT];
            var sTargetAmount = sAmount;
            var amount = -1m;
            decimal.TryParse(sAmount, out amount);
            var targetAmount = -1m;
            decimal.TryParse(sTargetAmount, out targetAmount);

            var hasRequestFromMobile = false;
            ContactTbl RequestFromUser = null;

            var checkExchangeRate = WalletApplication.MultiCurrencyForms.Contains(id);
            var exchangeRateResult = new WalletExchangeQuote();
            if (checkExchangeRate)
            {
                var rateSourceCAID = 0;
                var rateTargetCAID = 0;
                var rateIsSourceAmount = true;
                int.TryParse(fc[General.InputPrefix + ControlTypes.SOURCE_CAID].ToString().Decrypt(General.CAIDEncryptPurpose), out rateSourceCAID);
                int.TryParse(fc[General.InputPrefix + ControlTypes.TARGET_CAID].ToString().Decrypt(General.CAIDEncryptPurpose), out rateTargetCAID);
                bool.TryParse(fc[General.InputPrefix + ControlTypes.IS_SOURCE_AMOUNT], out rateIsSourceAmount);

                var exchangeRateResultQuery = await DapperHelper.QueryAsync<WalletExchangeQuote>(
                    "usp_CalculateWalletExchangeRate",
                    new
                    {
                        DbName = DapperHelper.DbName,
                        Date = DateTime.Now.ToString("dd/MM/yyyy"),
                        FromCAID = rateSourceCAID,
                        ToCAID = rateTargetCAID,
                        Amount = amount,
                        IsSourceAmount = rateIsSourceAmount
                    },
                    "Ledger",
                    System.Data.CommandType.StoredProcedure
                );
                exchangeRateResult = exchangeRateResultQuery.FirstOrDefault();

                sAmount = rateIsSourceAmount ? sAmount : exchangeRateResult.CalculatedAmount + "";
                sTargetAmount = rateIsSourceAmount ? exchangeRateResult.CalculatedAmount + "" : amount + "";
                decimal.TryParse(sTargetAmount, out targetAmount);

                if (fc[General.InputPrefix + ControlTypes.PAYMENT_OPTION] != PickerValues.PAYMENT_OPTION_DEBIT_CARD + "")
                {
                    CAID = rateSourceCAID;
                }
            }

            // Check if this CAID is owned by the user on SEND MONEY from wallet
            // TODO: Send within Bahrain / Send international
            var hasCaid = await _cardRepository.doesUserOwnCaid(CurrentUser, CAID);

            if (!hasCaid)
            {
                Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] User tried to use another user's CAID {CAID}!");

                Response.Message = "ErrorOccurred";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            if (id == PageTypes.WALLET_REQUEST_MONEY)
            {
                Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] amount validation for WALLET_REQUEST_MONEY.");

                #region Validate amount
                if (!decimal.TryParse(sAmount, out amount) || amount <= 0)
                {
                    Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] generate QR for amount is invalid.");

                    Response.Message = "InvalidAmountError";
                    Response.Status = NotificationType.ERROR;
                    return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                }

                //Validate Maxuimum Amount 
                var validateMaxAmountResponse = _formRepository.ValidateWalletMaxAmount(amount, id);
                if (validateMaxAmountResponse.Status.ToLower() == NotificationType.ERROR.ToLower())
                {
                    return Ok(BaseResponse<ResponseViewModel>.Failure(validateMaxAmountResponse));
                }

                #endregion
                #region Validate request from mobile no
                if (!string.IsNullOrWhiteSpace(fc[General.InputPrefix + ControlTypes.REQUEST_FROM]) || !string.IsNullOrWhiteSpace(fc[General.InputPrefix + ControlTypes.PERSONAL_ID]))
                {
                    var requestFromMobile = fc[General.InputPrefix + ControlTypes.REQUEST_FROM] + "";
                    var requestFromCPR = fc[General.InputPrefix + ControlTypes.PERSONAL_ID] + "";

                    if (requestFromMobile == CurrentUser.Mobile)
                    {
                        Logger.LogInformation($"[Request/SubmitForm/{id}/{type}/{requestFromMobile}][{CurrentUser.Username}] mobile no. given same as user mobile number.");
                        Response.Message = "RequestFromYourSelfError";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                    }

                    if (requestFromCPR == CurrentUser.Username)
                    {
                        Logger.LogInformation($"[Request/SubmitForm/{id}/{type}/{requestFromMobile}][{CurrentUser.Username}] CPR no. given same as user CPR number.");
                        Response.Message = "RequestFromYourSelfError";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                    }

                    var column = "";
                    var input = "";
                    if (!string.IsNullOrWhiteSpace(requestFromCPR))
                    {
                        column = "Cont_CPR";
                        input = requestFromCPR;
                    }
                    else if (!string.IsNullOrWhiteSpace(requestFromMobile))
                    {
                        column = "ContactMobile";
                        input = requestFromMobile;
                    }
                    else
                    {
                        Logger.LogInformation($"[Request/SubmitForm/{id}/{type}/{requestFromMobile}][{CurrentUser.Username}] CPR no. or Mobile No. is invalid");
                        Response.Message = "CPR no. or Mobile No. is invalid.";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                    }

                    //Check if is registred or not
                    RequestFromUser =
                    DapperHelper.Query<ContactTbl>(
                    $"SELECT * FROM dbo.ContactTbl WHERE {column} = @input AND Active = 1  AND ISNULL(MobileLogin,0)=1 ",
                    new
                    {
                        input = input
                    }
                    ).FirstOrDefault();
                    var isRegistered = RequestFromUser != null;
                    if (!isRegistered)
                    {
                        Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] mobile no. given does not match any account.");

                        Response.Message = "InvalidRecipientError";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                    }
                    hasRequestFromMobile = true;
                }
                #endregion
            }
            //===================================END [REQUEST MONEY]===============================================
            #endregion

            #region ADD MONEY-CARD || WALLET_ADD_MONEY VALIDATION
            if (id == PageTypes.CARD_ADD_MONEY || id == PageTypes.WALLET_ADD_MONEY)
            {
                Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] amount Validation for CARD_ADD_MONEY || WALLET_ADD_MONEY.");

                #region Validate amount                
                if (!decimal.TryParse(sAmount, out amount) || amount <= 0)
                {
                    Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] amount is invalid.");

                    Response.Message = "InvalidAmountError";
                    Response.Status = NotificationType.ERROR;
                    return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                }
                #endregion

                //Payment option - Wallet selected
                if (fc[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_WALLET + ""
                    || fc[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_WALLET_ACCOUNT + "")
                {
                    var customerBalance = await _walletRepository.GetCustomerWalletBalance(CAID, CurrentUser.ContactID);
                    if (amount > customerBalance)
                    {
                        Response.Message = "InsufficientWalletBalance";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));

                    }

                    //Validate Maxuimum Amount 
                    var validateMaxAmountResponse = _formRepository.ValidateWalletMaxAmount(amount, id);
                    if (validateMaxAmountResponse.Status.ToLower() == NotificationType.ERROR.ToLower())
                    {
                        return Ok(BaseResponse<ResponseViewModel>.Failure(validateMaxAmountResponse));
                    }

                    //Validate total amount transfer it as today
                    var validateMaxAmountPerDayResponse = await _formRepository.ValidateWalletMaxTotalAmountTransfer(amount, CAID, CurrentUser.ContactID);
                    if (validateMaxAmountPerDayResponse.Status.ToLower() == NotificationType.ERROR.ToLower())
                    {
                        return Ok(BaseResponse<ResponseViewModel>.Failure(validateMaxAmountPerDayResponse));
                    }
                }
                //Payment option - Wallet card selected
                else if (fc[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_CARD_ALT + "")
                {
                    var customerBalance = await _walletRepository.GetCustomerWalletBalance(CAID, CurrentUser.ContactID);
                    if (amount > customerBalance)
                    {
                        Response.Message = "InsufficientCardBalance";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));

                    }
                }
                else if (fc[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_CARD_ALT + "")
                {

                }
                else if (fc[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_DEBIT_CARD + "" && id == PageTypes.WALLET_ADD_MONEY)
                {
                    //if payment option is debit card and the form is add money for wallet make the card field empty
                    /*because the field in the form is hidden and at last step which is sucess payment the code show the form details with the card number
                    fc[General.InputPrefix + ControlTypes.MAIN_CARD_NUMBER] = "";
                    fc[General.InputPrefix + ControlTypes.SUPP_CARD_NUMBER] = "";
                    fc[General.InputPrefix + ControlTypes.CARD_NUMBER] = ""; */
                }
            }
            #endregion

            if (new List<int> { PageTypes.WALLET_PAY_MERCHANT, PageTypes.WALLET_SEND_MONEY, PageTypes.CARD_SEND_MONEY, PageTypes.WALLET_SCAN_AND_PAY }.Contains(id))
            {
                Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] Determined that the request is for Wallet. Getting amount...");

                // Send Money from wallet to own card
                if ((id == PageTypes.WALLET_SEND_MONEY || id == PageTypes.CARD_SEND_MONEY) && new List<string> { PickerValues.PAYMENT_OPTION_CARD + "", PickerValues.PAYMENT_OPTION_OTHERS + "", PickerValues.PAYMENT_OPTION_MY_ACCOUNTS + "", PickerValues.PAYMENT_OPTION_WITHIN_BH + "" }.Contains(fc[General.InputPrefix + ControlTypes.WALLET_SEND_TO_TYPE] + ""))
                {

                }
                // Others
                else
                {
                    var toMobileNo = fc[General.InputPrefix + 417];//=> WALLET_PAY_MERCHANT

                    if (id == PageTypes.WALLET_SEND_MONEY || id == PageTypes.CARD_SEND_MONEY || id == PageTypes.WALLET_SCAN_AND_PAY)
                    {
                        toMobileNo = fc[General.InputPrefix + 418];
                    }

                    if (toMobileNo == CurrentUser.Mobile)
                    {
                        Logger.LogInformation($"[Request/SubmitForm/{id}/{type}/{toMobileNo}][{CurrentUser.Username}] mobile no. given same as user mobile number.");
                        Response.Message = "SenViewModelYourSelfError";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                    }

                    var toCPR = fc[General.InputPrefix + ControlTypes.PERSONAL_ID] + "";

                    if (toCPR == CurrentUser.Username)
                    {
                        Logger.LogInformation($"[Request/SubmitForm/{id}/{type}/{toCPR}][{CurrentUser.Username}] CPR no. given same as user CPR number.");
                        Response.Message = "SenViewModelYourSelfError";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                    }

                    var column = "Cont_CPR";
                    var input = "";
                    if (!string.IsNullOrWhiteSpace(toCPR))
                    {
                        column = "Cont_CPR";
                        input = toCPR;
                    }
                    else if (!string.IsNullOrWhiteSpace(toMobileNo))
                    {
                        column = "ContactMobile";
                        input = toMobileNo;
                    }
                    else
                    {
                        Logger.LogInformation($"[Request/SubmitForm/{id}/{type}/{toMobileNo}][{CurrentUser.Username}] CPR no. or Mobile No. is invalid");
                        Response.Message = "CPR no. or Mobile No. is invalid.";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                    }


                    #region Check if is registred or not
                    RequestFromUser =
                        DapperHelper.Query<ContactTbl>(
                            $"SELECT * FROM dbo.ContactTbl WHERE {column} = @input AND Active = 1  AND ISNULL(MobileLogin,0)=1 ",
                            new
                            {
                                input = input
                            }
                    ).FirstOrDefault();
                    var isRegistered = RequestFromUser != null;
                    if (!isRegistered)
                    {
                        Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] mobile no. given does not match any account.");

                        Response.Message = "InvalidRecipientError";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                    }
                    #endregion
                }

                // TODO: Look into ID 44 used !
                if (!decimal.TryParse(fc[General.InputPrefix + 44], out amount) || amount <= 0)
                {
                    Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] wallet transfer amount is invalid.");

                    Response.Message = "InvalidAmountError";
                    Response.Status = NotificationType.ERROR;
                    return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                }

                if (id == PageTypes.CARD_SEND_MONEY)
                {
                    var customerBalance = await _walletRepository.GetCustomerWalletBalance(CAID, CurrentUser.ContactID);
                    if (amount > customerBalance)
                    {
                        Response.Message = "InsufficientCardBalance";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));

                    }
                }
                else
                {
                    var customerBalance = await _walletRepository.GetCustomerWalletBalance(CAID, CurrentUser.ContactID);

                    if (amount > customerBalance)
                    {
                        Response.Message = "InsufficientWalletBalance";
                        Response.Status = NotificationType.ERROR;
                        return Ok(BaseResponse<ResponseViewModel>.Failure(Response));

                    }

                    #region Validate max amount per single & whole transactions
                    //Validate Maxuimum Amount 
                    var validateMaxAmountResponse = _formRepository.ValidateWalletMaxAmount(amount, id);
                    if (validateMaxAmountResponse.Status.ToLower() == NotificationType.ERROR.ToLower())
                    {
                        return Ok(BaseResponse<ResponseViewModel>.Failure(validateMaxAmountResponse));
                    }

                    //Validate total amount transfer it as today
                    var validateMaxAmountPerDayResponse = await _formRepository.ValidateWalletMaxTotalAmountTransfer(amount, CAID, CurrentUser.ContactID);
                    if (validateMaxAmountPerDayResponse.Status.ToLower() == NotificationType.ERROR.ToLower())
                    {
                        return Ok(BaseResponse<ResponseViewModel>.Failure(validateMaxAmountPerDayResponse));
                    }
                    #endregion
                }
            }


            /* #region ATM Withdrawal Money 
            if (id == PageTypes.ATM_WITHDRAWAL_MONEY)
            {
                Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] Withdraw Money...");

                //START: SumHash
                string encryptkey = System.Configuration.ConfigurationManager.AppSettings["KioskWebServicesKey"] + "";
                string inputData = fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_Date] +
                                        fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_Time] +
                                        fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_TransactionAmount] +
                                        fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_BillNumber] +
                                        fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_StoreLabel] +
                                        fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_CustomerLabel] +
                                        fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_TerminalLabel] +
                                        fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_TransactionType];

                System.Text.ASCIIEncoding encoding = new System.Text.ASCIIEncoding();
                byte[] keyByte = encoding.GetBytes(encryptkey);
                System.Security.Cryptography.HMACSHA256 hmacmd5 = new System.Security.Cryptography.HMACSHA256(keyByte);
                byte[] messageBytes = encoding.GetBytes(inputData);
                byte[] hashmessage = hmacmd5.ComputeHash(messageBytes);
                string finalResultSumHash = "";
                for (int i = 0; i < hashmessage.Length; i++)
                {
                    finalResultSumHash += hashmessage[i].ToString("X2"); // hex format
                }
                //END: SumHash

                Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] Call BFCKioskAPI/PostTransactionResult");

                BFCKioskAPI.BFCKioskSoap client = new BFCKioskAPI.BFCKioskSoapClient("BFCKioskSoap");

                var result = client.PostTransactionResult(
                    fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_Date],
                    fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_Time],
                    fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_TransactionAmount],
                    fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_BillNumber],
                    fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_StoreLabel],
                    fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_CustomerLabel],
                    fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_TerminalLabel],
                    fc[General.InputPrefix + ControlTypes.ATM_Withdrawal_Money_TransactionType],
                    finalResultSumHash + ""
                    );

                string responseStatus = Global.Error;
                string responseMsg = Kiosk.ResponseCode99;
                switch (result.TransactionResponse.Code)
                {
                    case "00":
                        responseStatus = Global.Success;
                        responseMsg = Kiosk.ResponseCode00;
                        break;
                    case "05":
                        responseStatus = Global.Error;
                        responseMsg = Kiosk.ResponseCode05;
                        break;
                    case "12":
                        responseStatus = Global.Error;
                        responseMsg = Kiosk.ResponseCode12;
                        break;
                    case "13":
                        responseStatus = Global.Error;
                        responseMsg = Kiosk.ResponseCode13;
                        break;
                    case "16":
                        responseStatus = Global.Error;
                        responseMsg = Kiosk.ResponseCode16;
                        break;
                    case "61":
                        responseStatus = Global.Error;
                        responseMsg = Kiosk.ResponseCode61;
                        break;
                    case "65":
                        responseStatus = Global.Error;
                        responseMsg = Kiosk.ResponseCode65;
                        break;
                    case "96":
                        responseStatus = Global.Error;
                        responseMsg = Kiosk.ResponseCode96;
                        break;
                    case "99":
                    default:
                        responseStatus = Global.Error;
                        responseMsg = Kiosk.ResponseCode99;
                        break;
                }

                Response.Status = responseStatus;
                Response.Message = responseMsg;

                if (Application.IsDummyData)
                {
                    Response.Status = Global.Success;
                    Response.Message = Kiosk.ResponseCode00;
                }

                Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] BFCKioskAPI/PostTransactionResult Return Message:{Response.Message} | Status:{Response.Status} ");

                return Json(Response);
            }

            #endregion */


            // Insert the request to the database and submit an email report
            var formTableId = -1;
            var inserted = await _formRepository.InsertRequest(id, CurrentUser.CustomerID, CurrentUser.ContactID, CurrentUser.Username, fc, formTableId, CAID, CurrentUser.CRID, CurrentUser, exchangeRateResult);
            formTableId = inserted.Item2;

            // Set the card number (Either main, supp or general)
            var cardNumber =
                   !string.IsNullOrWhiteSpace(fc[General.InputPrefix + ControlTypes.MAIN_CARD_NUMBER]) ? fc[General.InputPrefix + ControlTypes.MAIN_CARD_NUMBER] :
                   !string.IsNullOrWhiteSpace(fc[General.InputPrefix + ControlTypes.SUPP_CARD_NUMBER]) ? fc[General.InputPrefix + ControlTypes.SUPP_CARD_NUMBER] :
                   !string.IsNullOrWhiteSpace(fc[General.InputPrefix + ControlTypes.CARD_NUMBER]) ? fc[General.InputPrefix + ControlTypes.CARD_NUMBER] :
                   "";

            if (inserted.Item1)
            {
                if ((id == PageTypes.WALLET_SEND_MONEY || id == PageTypes.CARD_SEND_MONEY) && !string.IsNullOrWhiteSpace(sRequestId))
                {
                    int notificationId = -1;
                    if (!int.TryParse(sRequestId.Decrypt("NOTIFICATION_ID"), out notificationId))
                    {
                        notificationId = -1;
                    }

                    // Inactivate the selected notifications
                    var sql =
                        " UPDATE dbo.CustomerServicesTbl" +
                        " SET active = 0 WHERE ID IN (" + notificationId + ") OR (Par1 <> '' AND Par1 IN (SELECT Par1 FROM dbo.CustomerServicesTbl WHERE ID IN (" + notificationId + ")))" +
                        " /*AND RecID = @CustomerID*/" +
                        " AND ProductID = @ProductID";

                    DapperHelper.Exceute(sql, new
                    {
                        CustomerID = CurrentUser.CustomerID,
                        ProductID = PageTypes.NOTIFICATIONS_PAGE
                    });


                    Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}][{notificationId}] Checking for split payment request...");
                    var isSplitPaymentRequest = DapperHelper.ExecuteScalar<string>($"SELECT Par2 FROM dbo.CustomerServicesTbl WHERE ID = @NotificationId", new
                    {
                        NotificationId = notificationId
                    }) == "1";
                    Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}][{notificationId}] Checked for split payment request: {isSplitPaymentRequest}");

                    if (isSplitPaymentRequest)
                    {
                        Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}][{notificationId}] Updating paid status for split payment request...");
                        DapperHelper.Exceute($@"
                            UPDATE SPD
                            SET SPD.Paid = 1, SPD.DatePaid = GETDATE()
                            FROM dbo.Wallet_SplitPaymentDetails SPD
                            INNER JOIN dbo.Wallet_SplitPaymentHeader SPH ON SPH.Id = SPD.HeaderId
                            INNER JOIN dbo.CustomerServicesTbl CST ON CST.Par1 = SPD.Id
                            WHERE CST.ID =  @NotificationId
                        ", new
                        {
                            NotificationId = notificationId
                        });
                        Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}][{notificationId}] Updated paid status for split payment request...");
                    }
                }

                #region REQUEST MONEY
                //=================================[REQUEST MONEY]=================================================
                if (id == PageTypes.WALLET_REQUEST_MONEY)
                {
                    if (hasRequestFromMobile)
                    {
                        // Add the message to the MobileNotificationProcessingQueue
                        /*var messageParameters = new ExpandoObject() as IDictionary<string, object>;
                        var sql =
                              "SET DATEFORMAT DMY;INSERT INTO dbo.MobileNotificationProcessingQueue (CustomerId, MessageContent, MessageType, DateCreated, DateModified, Processed, NoOfTrails, Cont_CPR)" +
                             $" VALUES (@CustomerId, @MessageContent, @MessageType, GETDATE(),GETDATE(),0,0, @Cont_CPR)";*/

                        var amountFormatted = "";
                        if (checkExchangeRate)
                        {
                            amountFormatted = amount.FormatAmount(exchangeRateResult.TargetDecimal, exchangeRateResult.TargetSwiftCode);
                        }
                        else
                        {
                            var accountDetailsAsync = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, CAID);
                            var accountDetails = accountDetailsAsync.FirstOrDefault();
                            amountFormatted = amount.FormatAmount(accountDetails?.NoOfDecimals ?? 0, accountDetails?.SwiftCode ?? "");
                        }

                        /*messageParameters.Add($"CustomerId", RequestFromUser.CustomerID);
                        messageParameters.Add($"MessageContent", $"{CurrentUser.Name} has requested {amountFormatted} from you. Would you like to transfer this amount?");
                        messageParameters.Add($"MessageType", $"R{formTableId}");//=>Request
                        messageParameters.Add($"Cont_CPR", RequestFromUser.Cont_CPR);
                        var i = DapperHelper.Exceute(sql, messageParameters);*/

                        var i = await DapperHelper.ExceuteAsync(
                            "dbo.usp_SendReceiptGeneralProcessRequestMoneyNotificationWallet @ToCAID = @ToCAID, @ToAmount = @ToAmount, @FormID = @FormID, @ReferenceNumber = @ReferenceNumber, @RequesteeContactId = @RequesteeContactId",
                            new
                            {
                                ToCAID = CAID,
                                ToAmount = amountFormatted,
                                FormID = id,
                                ReferenceNumber = formTableId,
                                RequesteeContactId = RequestFromUser.ContactID
                            }
                        );

                        //Executed successfully
                        if (i > 0)
                        {
                            await DapperHelper.ExceuteAsync($"UPDATE ChooseListDetailsLogTbl SET CLIId='{StatusValues.APPROVED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS};");
                        }
                        else
                        {
                            await DapperHelper.ExceuteAsync($"UPDATE ChooseListDetailsLogTbl SET CLIId='{StatusValues.DECLINED}' WHERE TableId={formTableId} AND CLCatId={ControlTypes.STATUS}");
                        }
                    }
                    else
                    {
                        var walletAccountAsync = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, CAID, -1);
                        var walletAccount = walletAccountAsync.FirstOrDefault();
                        if (walletAccount == null)
                        {
                            Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] Could not find the wallet account.");

                            Response.Message = "ErrorOccurred";
                            Response.Status = NotificationType.ERROR;
                            return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                        }

                        #region Generate QR Code
                        //Retreive Customer File Data
                        var items = DapperHelper.Query<MerchantData>(
                      " SELECT CF.BankName,C.CityE City,ACT.ISO18245 " +
                      " FROM dbo.CustomerFile CF " +
                      " INNER JOIN dbo.CityTbl C ON C.CityID = CF.CityID " +
                      " INNER JOIN dbo.ARCustomerTypeTbl ACT ON ACT.CustomerTypeId= CF.CustomerTypeID " +
                      " WHERE CF.RecId=@RecId ",
                      new
                      {
                          RecId = CurrentUser.CustomerID
                      }).FirstOrDefault();

                        if (items == null)
                        {
                            Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] Data Retrieved From CustomerFile IS NULL.");
                            Response.Message = "ErrorOccurred";
                            Response.Status = NotificationType.ERROR;
                            return Ok(BaseResponse<ResponseViewModel>.Failure(Response)); ;
                        }

                        if (items.ISO18245 <= 0)
                        {
                            Logger.LogInformation($"[Request/SubmitForm/{id}/{type}][{CurrentUser.Username}] ISO 18245 IS Invalid.");

                            Response.Message = "ErrorOccurred";
                            Response.Status = NotificationType.ERROR;
                            return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                        }

                        string code = GeneralHelper.GenerateQR(amount, items.BankName, items.City, items.ISO18245, CAID,CurrentUser);
                        Logger.LogInformation($"[Wallet/GenerateStaticQR][{CurrentUser.Username}] payload Code:{code}");

                        var imgBarPath = "";
                        using (Bitmap bitMap = GeneralHelper.GetBitmapQR(code))
                        {
                            using (MemoryStream ms = new MemoryStream())
                            {
                                bitMap.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
                                byte[] byteImage = ms.ToArray();
                                imgBarPath = "data:image/png;base64," + Convert.ToBase64String(byteImage);
                            }
                        }

                        var data = new
                        {
                            QRTitle = walletAccount.Name,
                            QRDescription = $"A/C Number: {walletAccount.AccountNumber}<br/>{amount.FormatAmount(walletAccount.NoOfDecimals, walletAccount.SwiftCode)}",
                            QR = imgBarPath
                        };

                        return Ok(BaseResponse<dynamic>.Success(data));
                        #endregion
                    }
                }
                //=================================End [REQUEST MONEY]=================================================
                #endregion

                #region REDIRECT TO BENEFIT [WALLET_ADD_MONEY || CARD_ADD_MONEY]
                if (new List<int> { PageTypes.WALLET_ADD_MONEY, PageTypes.CARD_ADD_MONEY }.Contains(id) && fc[General.InputPrefix + ControlTypes.PAYMENT_OPTION] == PickerValues.PAYMENT_OPTION_DEBIT_CARD + "")/*Benefit*/
                {
                    Response.PaymentData = new PaymentSubmitModel() { PaymentLink = "Payment/Submit" };
                    Response.PaymentData.TranAmount = amount;
                    Response.PaymentData.TranTargetAmount = targetAmount;
                    Response.PaymentData.TranFormId = id;
                    Response.PaymentData.TranFormTableId = formTableId;
                    Response.PaymentData.TranCAID = CAID;
                    Response.PaymentData.PayTranType = "WALLET-ADD-MONEY";

                    if (id == PageTypes.CARD_ADD_MONEY)
                    {
                        Response.PaymentData.PayTranType = "CARD-ADD-MONEY";
                        //TempData["PayCardNumber"] = cardNumber;
                    }
                }
                #endregion


                var message = GeneralHelper.GetStatusMessage(id, "request", true);
                if (type.ToUpper().Equals("SERVICES"))
                {
                    message = GeneralHelper.GetStatusMessage(id, "service", true);
                }

                Response.Message = message;
                Response.Status = NotificationType.SUCCESS;
            }
            else
            {
                var message = GeneralHelper.GetStatusMessage(id, "request", false);
                if (type.ToUpper().Equals("SERVICES"))
                {
                    message = GeneralHelper.GetStatusMessage(id, "service", false);
                }

                Response.Message = message;
                Response.Status = NotificationType.ERROR;
            }
            return Ok(BaseResponse<ResponseViewModel>.Success(Response));
        }

        [HttpPost("Quote")]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<ResponseViewModel>))]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BaseResponse<WalletQuoteResponse>))]
        public async Task<IActionResult> Quote([FromBody] WalletQuoteRequest walletQuoteRequest)
        {
            var sCAID = walletQuoteRequest.sCAID;
            var dCAID = walletQuoteRequest.dCAID;
            var amount = walletQuoteRequest.amount;
            var isSourceAmount = walletQuoteRequest.isSourceAmount;
            var confirmTransfer = walletQuoteRequest.confirmTransfer;

            // Keep track of the response
            var Response = new ResponseViewModel();
            if (string.IsNullOrWhiteSpace(sCAID) || string.IsNullOrWhiteSpace(dCAID) || amount <= 0)
            {
                Response.Message = "ErrorDataSubmitted";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Success(Response));
            }

            int fromCAID = CurrentUser.CAID;
            if (!int.TryParse(sCAID.Decrypt(General.CAIDEncryptPurpose), out fromCAID))
            {
                fromCAID = CurrentUser.CAID;
            }

            int toCAID = CurrentUser.CAID;
            if (!int.TryParse(dCAID.Decrypt(General.CAIDEncryptPurpose), out toCAID))
            {
                toCAID = CurrentUser.CAID;
            }

            if (fromCAID == toCAID)
            {
                Response.Status = NotificationType.ERROR;
                Response.Message = "You cannot transfer money to the same wallet.";
                return Ok(BaseResponse<ResponseViewModel>.Success(Response));
            }

            var resultQuery = await DapperHelper.QueryAsync<WalletExchangeQuote>(
                "usp_CalculateWalletExchangeRate",
                new
                {
                    DbName = DapperHelper.DbName,
                    Date = DateTime.Now.ToString("dd/MM/yyyy"),
                    FromCAID = fromCAID,
                    ToCAID = toCAID,
                    Amount = amount,
                    IsSourceAmount = isSourceAmount
                },
                "Ledger",
                System.Data.CommandType.StoredProcedure
            );
            var result = resultQuery.FirstOrDefault();

            var sourceAmount = (isSourceAmount ? amount : result.CalculatedAmount);
            var targetAmount = (isSourceAmount ? result.CalculatedAmount : amount);
            if (confirmTransfer)
            {
                var customerBalance = await _walletRepository.GetCustomerWalletBalance(fromCAID, CurrentUser.ContactID);
                if (amount > customerBalance)
                {
                    Response.Message = "InsufficientWalletBalance";
                    Response.Status = NotificationType.ERROR;
                    return Ok(BaseResponse<ResponseViewModel>.Success(Response));
                }

                var receiptId = await DapperHelper.ExecuteScalarAsync<int>(
                    $" EXEC dbo.usp_CreateGeneralReceiptWalletTransfer @UserName = N'{CurrentUser.Username}', @FromCAID = {fromCAID}, @FromAmount = {sourceAmount}, @ToCAID = {toCAID}, @BatchNo = 0, @FormID = 0, @CSID = 0, @Type = 2, @ToAmount = {targetAmount}, @ExchangeRate = {result.ExchangeRate};"
                );

                Response = new ResponseViewModel
                {
                    Status = receiptId > 0 ? NotificationType.SUCCESS : NotificationType.ERROR,
                    Message = receiptId > 0 ? "Your wallet transfer has been processed successfully." : "Failed to process your wallet transfer. Please try again later."
                };

                // this.AddNotification(Response.Message, Response.Status);
                return Ok(BaseResponse<ResponseViewModel>.Success(Response));
            }
            var data = new WalletQuoteResponse()
            {
                success = true,
                sourceAmount = sourceAmount.FormatAmount(result.SourceDecimal, ""),
                targetAmount = targetAmount.FormatAmount(result.TargetDecimal, ""),
                exchangeRate = result.ExchangeRate.FormatAmount(5, ""),
                exchangeRateReverse = (1 / result.ExchangeRate).FormatAmount(5, ""),
                fees = result.Fees.FormatAmount(result.SourceDecimal, result.SourceSwiftCode),
                vat = result.Vat.FormatAmount(result.SourceDecimal, result.SourceSwiftCode),
                feesWithVat = (result.Fees + result.Vat).FormatAmount(result.SourceDecimal, result.SourceSwiftCode),
                totalToPay = (sourceAmount + result.Fees + result.Vat).FormatAmount(result.SourceDecimal, result.SourceSwiftCode)
            };
            return Ok(BaseResponse<WalletQuoteResponse>.Success(data));
        }


        [HttpPost("CalculateRate")]
        public async Task<IActionResult> CalculateRate(int id, decimal amount, string sourceCAID, string targetCAID)
        {
            var model = new List<ExchangeRate>();
            var response = new ResponseViewModel
            {
                Status = NotificationType.ERROR,
                Message = "ErrorOccurred"
            };
            try
            {
                int srcCAID = -1;
                if (!string.IsNullOrWhiteSpace(sourceCAID))
                {
                    int.TryParse(sourceCAID.Decrypt("CAID"), out srcCAID);
                }
                else
                {
                    srcCAID = CurrentUser.CAID;
                }

                int tgtCAID = -1;
                if (!string.IsNullOrWhiteSpace(targetCAID))
                {
                    int.TryParse(targetCAID.Decrypt("CAID"), out tgtCAID);
                }
                else
                {
                    tgtCAID = CurrentUser.CAID;
                }

                model = (await DapperHelper.QueryAsync<ExchangeRate>($@"
                    SET DATEFORMAT DMY;

                    DECLARE @Rate decimal(30,19);
                    DECLARE @Decimal int;

                    CREATE TABLE #tmpCHist (CcyCode varchar(10) COLLATE SQL_Latin1_General_CP1256_CI_AS NULL, Rate decimal(30,19),RateM decimal(30,19), BuyRate decimal(30,19), BuyRateM decimal(30,19), SellRate decimal(30,19), SellRateM decimal(30,19)) 
                    Insert Into #tmpCHist (CcyCode,Rate,RateM,BuyRate, BuyRateM, SellRate, SellRateM) 
                    exec {DapperHelper.LedgerDbName}usp_GetRateByDate @Date, 1;

                    SELECT 
                        dbo.UFN_Rounder(CONVERT(MONEY, (@SourceAmount) / T.SellRate) * TGT.SellRate, TGT.[Decimal]),
                        CuF.CcyCode AS [CcyCode], CR.SwiftCode AS [SwiftCode], CR.Decimal AS [NoOfDecimal],
                        CuF.Rate AS [Rate], CuF.RateM AS [RateM], 
                        CuF.BuyRate AS [BuyRate], CuF.BuyRateM AS [BuyRateM], 
                        CuF.SellRate AS [SellRate],  CuF.SellRateM AS [SellRateM], @SourceAmount AS [SourceAmount]
                    FROM {DapperHelper.UtilitiesDbName}CustomerAccountTbl CA
                    INNER JOIN {DapperHelper.LedgerDbName}[Account file] AF ON AF.AccountNo = CA.CAAccountNo
                    INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CF ON CF.CcyCode = AF.CCy
                    INNER JOIN #tmpCHist T ON T.CcyCode = CF.CcyCode
                    INNER JOIN (
                        SELECT CuCF.SwiftCode, CuCF.[Decimal], CuF.[Rate], CuCF.BranchID, CuF.BuyRate, CuF.BuyRateM, CuF.SellRate, CuF.SellRateM
                        FROM #tmpCHist CuF 
                        INNER JOIN {DapperHelper.LedgerDbName}[Currency File] CuCF ON CuCF.CcyCode = CuF.CcyCode
                    ) TGT ON TGT.SwiftCode = 'USD' AND TGT.BranchID = @BranchID
                    WHERE CA.CAID = @tgtCAID

                    /*SELECT  CuF.CcyCode AS [CcyCode], CR.SwiftCode AS [SwiftCode], CR.Decimal AS [NoOfDecimal],
                            CuF.Rate AS [Rate], CuF.RateM AS [RateM], 
                            CuF.BuyRate AS [BuyRate], CuF.BuyRateM AS [BuyRateM], 
                            CuF.SellRate AS [SellRate],  CuF.SellRateM AS [SellRateM], @SourceAmount AS [SourceAmount]
                    FROM {DapperHelper.LedgerDbName}[Currency file] CR
                    INNER JOIN #tmpCHist CuF ON CuF.CcyCode = CR.CcyCode 
                    INNER JOIN {DapperHelper.UtilitiesDbName}CustomerAccountTbl CASRC ON CASRC.CAID = @srcCAID 
                    INNER JOIN {DapperHelper.LedgerDbName}[Account file] AFSRC ON AFSRC.AccountNo = CASRC.CAAccountNo AND CuF.CcyCode = AFSRC.CCy

                    UNION ALL

                    SELECT  CuF.CcyCode AS [CcyCode], CR.SwiftCode AS [SwiftCode], CR.Decimal AS [NoOfDecimal],
                            CuF.Rate AS [Rate], CuF.RateM AS [RateM], 
                            CuF.BuyRate AS [BuyRate], CuF.BuyRateM AS [BuyRateM], 
                            CuF.SellRate AS [SellRate],  CuF.SellRateM AS [SellRateM], @SourceAmount AS [SourceAmount]
                    FROM {DapperHelper.LedgerDbName}[Currency file] CR
                    INNER JOIN #tmpCHist CuF ON CuF.CcyCode = CR.CcyCode 
                    INNER JOIN {DapperHelper.UtilitiesDbName}CustomerAccountTbl CATGT ON CATGT.CAID = @tgtCAID 
                    INNER JOIN {DapperHelper.LedgerDbName}[Account file] AFTGT ON AFTGT.AccountNo = CATGT.CAAccountNo AND CuF.CcyCode = AFTGT.CCy*/

                    DROP TABLE #tmpCHist 
                ", new
                {
                    tgtCAID = tgtCAID,
                    srcCAID = srcCAID,
                    SourceAmount = amount,
                    Date = DateTime.Now.ToString("dd/MM/yyyy"),
                    BranchID = WalletApplication.BranchId
                })).ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError($"[Request/CalculareRate-POST][{CurrentUser.Username}] Error:", ex);
                response.Status = NotificationType.ERROR;
                response.Message = "InternalError";
                return Ok(BaseResponse<ResponseViewModel>.Failure(response));
            }
            return Ok(BaseResponse<List<ExchangeRate>>.Success(model));
        }

        [HttpGet("GetUserWalletAccounts")]
        public async Task<IActionResult> GetUserWalletAccounts(string toMobileNo, string toCPR, string type = "W", string sCAID = "")
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogInformation($"[Request/GetUserWalletAccounts/{runId}/{toCPR}/{toMobileNo}][{CurrentUser.Username}] Entered");
            var Response = new ResponseViewModel();

            int CAID = -1;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (!int.TryParse(sCAID, out CAID))
                {
                    CAID = -1;
                }
            }

            if (toMobileNo == CurrentUser.Mobile)
            {
                Logger.LogInformation($"[Request/GetUserWalletAccounts/{runId}/{toMobileNo}][{CurrentUser.Username}] mobile no. given same as user mobile number.");
                Response.Message = "SenViewModelYourSelfError";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            if (toCPR == CurrentUser.Username)
            {
                Logger.LogInformation($"[Request/GetUserWalletAccounts/{runId}/{toCPR}][{CurrentUser.Username}] CPR no. given same as user CPR number.");
                Response.Message = "SenViewModelYourSelfError";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            var column = "Cont_CPR";
            var input = "";
            if (!string.IsNullOrWhiteSpace(toCPR))
            {
                column = "Cont_CPR";
                input = toCPR;
            }
            else if (!string.IsNullOrWhiteSpace(toMobileNo))
            {
                column = "ContactMobile";
                input = toMobileNo;
            }
            else
            {
                Logger.LogInformation($"[Request/GetUserWalletAccounts/{runId}][{CurrentUser.Username}] CPR no. or Mobile No. is invalid");
                Response.Message = "CPR no. or Mobile No. is invalid.";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }


            #region Check if is registred or not
            var RequestFromUser =
                DapperHelper.Query<ContactTbl>(
                    $"SELECT * FROM dbo.ContactTbl WHERE {column} = @input AND Active = 1  AND ISNULL(MobileLogin,0)=1 ",
                    new
                    {
                        input = input
                    }
            ).FirstOrDefault();

            var isRegistered = RequestFromUser != null;
            if (!isRegistered)
            {
                Logger.LogInformation($"[Request/GetUserWalletAccounts/{runId}][{CurrentUser.Username}] mobile no. given does not match any account.");
                Response.Message = "InvalidRecipientError";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }
            #endregion

            if (type.ToUpper() == "C")
            {
                var cards = await _cardRepository.GetCards<CardDetailsViewModel>(RequestFromUser.ContactID, RequestFromUser.CustomerID, -1, false, "", false, false, RequestFromUser.Cont_CPR);
                if (!cards.Any())
                {
                    Response.Message = "The recipient doesn't have any cards!";
                    Response.Status = NotificationType.ERROR;
                    return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
                }
                if (CAID != -1)
                {
                    cards = cards.Where(r => r.CardId == CAID);
                }
                return Ok(BaseResponse<List<CardDetailsViewModel>>.Success(cards.ToList()));
            }

            var accounts = await _cardRepository.GetWalletAccounts(RequestFromUser.ContactID);
            if (!accounts.Any())
            {
                Response.Message = "The recipient doesn't have any accounts!";
                Response.Status = NotificationType.ERROR;
                return Ok(BaseResponse<ResponseViewModel>.Failure(Response));
            }

            if (CAID != -1)
            {
                accounts = accounts.Where(r => r.CAID == CAID).ToList();
            }

            return Ok(BaseResponse<List<WalletAccount>>.Success(accounts.ToList()));
        }

        [HttpGet("GetSplitPayments")]
        public async Task<IActionResult> SplitPayments(string sCAID)
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/SplitPaymentList/{runId}][{CurrentUser.Username}] Entered");

            var bills = new List<SplitPaymentBill>();

            try
            {
                var billsQuery = await DapperHelper.QueryAsync<SplitPaymentBill>($@"
                    SELECT SPH.Id, SPH.Title, SPH.Amount, SPH.DateCreated, SPH.CAID, 
                    COUNT(SPD.ContactId) AS TotalRecipients, COUNT(CASE WHEN SPD.Paid = 1 THEN 1 ELSE NULL END) AS TotalPaid,
                    SUM(CASE WHEN SPD.Paid = 1 THEN SPD.Amount ELSE NULL END) TotalAmountPaid,
                    CF.SwiftCode AS SwiftCode, CF.[Decimal] AS NoOfDecimals
                    FROM dbo.Wallet_SplitPaymentHeader SPH
                    INNER JOIN dbo.Wallet_SplitPaymentDetails SPD ON SPD.HeaderId = SPH.Id
                    INNER JOIN dbo.vw_CustomerFile CF ON CF.CAID = SPH.CAID
                    WHERE SPH.Active = 1 AND SPH.ContactId = @ContactId AND SPH.CAID = @CAID
                    GROUP BY SPH.Id, SPH.Title, SPH.Amount, SPH.DateCreated, SPH.CAID, CF.SwiftCode, CF.[Decimal]
                    ORDER BY SPH.DateCreated DESC
                ", new
                {
                    CAID = sCAID.Decrypt("CAID"),
                    ContactId = CurrentUser.ContactID
                });
                bills = billsQuery.ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError($"[Request/SplitPaymentList/{runId}][{CurrentUser.Username}] Error:", ex);
            }

            return Ok(BaseResponse<List<SplitPaymentBill>>.Success(bills));
        }

        [HttpGet("GetSplitPaymentDetails")]
        public async Task<IActionResult> SplitPaymentDetails([Required] string id)
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/SplitPaymentDetails/{runId}][{CurrentUser.Username}] Entered");

            int decryptedID = Int32.Parse(id.DecryptUrl());
            var bill = new SplitPaymentDetails();

            try
            {
                var billQuery = await DapperHelper.QueryAsync<SplitPaymentDetails>($@"
                    SELECT SPH.Id, SPH.Title AS BillName, SPH.Amount AS BillAmount, SPH.Notes AS BillNotes, SPH.DateCreated, 
                    CF.CAID, CF.CustAcNameE AS AccountName, CF.SwiftCode AS SwiftCode, CF.[Decimal] AS NoOfDecimals
                    FROM dbo.Wallet_SplitPaymentHeader SPH
                    INNER JOIN dbo.vw_CustomerFile CF ON CF.CAID = SPH.CAID
                    WHERE SPH.Id = @Id AND SPH.Active = 1 AND SPH.ContactId = @ContactId
                ", new
                {
                    Id = decryptedID,
                    ContactId = CurrentUser.ContactID
                });
                bill = billQuery.FirstOrDefault();

                var billRecipientQuery = await DapperHelper.QueryAsync<SplitPaymentRecipient>($@"
                    SELECT CT.ContactID AS Id, CT.ContactEng AS Name, CT.ContactMobile AS Mobile, SPD.Amount AS Amount, SPD.Paid AS Paid, SPD.DatePaid AS DatePaid
                    FROM dbo.Wallet_SplitPaymentHeader SPH
                    INNER JOIN dbo.Wallet_SplitPaymentDetails SPD ON SPD.HeaderId = SPH.Id
                    INNER JOIN dbo.ContactTbl CT ON CT.ContactID = SPD.ContactId
                    WHERE SPH.Id = @Id AND SPH.Active = 1 AND SPH.ContactId = @ContactId
                ", new
                {
                    Id = decryptedID,
                    ContactId = CurrentUser.ContactID
                });

                bill.BillRecipient = billRecipientQuery.ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError($"[Request/SplitPaymentDetails/{runId}][{CurrentUser.Username}] Error:", ex);
            }

            return Ok(BaseResponse<SplitPaymentDetails>.Success(bill));
        }

        [HttpPost("SplitPaymentDelete")]
        public async Task<IActionResult> SplitPaymentDelete([Required] string id)
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/SplitPaymentDelete/{runId}][{CurrentUser.Username}] Entered");
            int decryptedID = Int32.Parse(id.DecryptUrl());
            try
            {
                var isSuccess = await DapperHelper.ExceuteAsync($@"
                    UPDATE dbo.Wallet_SplitPaymentHeader 
                    SET Active = 0
                    WHERE Id = @Id AND ContactId = @ContactId
                ", new
                {
                    Id = decryptedID,
                    ContactId = CurrentUser.ContactID
                }) > 0;

                return Ok(BaseResponse<bool>.Success(true));
            }
            catch (Exception ex)
            {
                Logger.LogError($"[Request/SplitPaymentDelete/{runId}][{CurrentUser.Username}] Error:", ex);
            }

            return Ok(BaseResponse<bool>.Failure());
        }

        [HttpGet("GetCreateSplitPaymentBillForm")]
        public async Task<IActionResult> CreateSplitPaymentBillForm(string sCAID = "")
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/SplitPayment/{runId}/{sCAID}][{CurrentUser.Username}] Entered");
            Logger.LogDebug($"[Request/SplitPayment/{runId}/{sCAID}][{CurrentUser.Username}] Decrypting CAID...");

            int CAID = CurrentUser.CAID;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (!int.TryParse(sCAID.Decrypt(General.CAIDEncryptPurpose), out CAID))
                {
                    Logger.LogDebug($"[Request/SplitPayment/{runId}/{sCAID}][{CurrentUser.Username}] CAID is invalid, using default CAID");
                    CAID = CurrentUser.CAID;
                }
            }
            Logger.LogDebug($"[Request/SplitPayment/{runId}/{CAID}][{CurrentUser.Username}] Decrypted CAID: {CAID}");

            var walletAccountTask = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, CAID);
            var walletAccount = walletAccountTask.FirstOrDefault();
            if (walletAccount == null)
            {
                Logger.LogDebug($"[Request/SplitPayment/{runId}/{CAID}][{CurrentUser.Username}] Couldn't find the wallet account");
                return Ok(BaseResponse<bool>.Failure(new string[] { "Account Not Found", "Failed to find your wallet account. Please try again later." })); ;
            }
            return Ok(BaseResponse<WalletAccount>.Success(walletAccount));
        }

        [HttpPost("SplitPaymentCheckAddUser")]
        public async Task<IActionResult> SplitPaymentCheckAddUser([FromBody] SplitPaymentCheckUserRequest splitPaymentCheckUserRequest)
        {
            var toMobileNo = splitPaymentCheckUserRequest.toMobileNo;
            var toCPR = splitPaymentCheckUserRequest.toCPR;
            var type = splitPaymentCheckUserRequest.type;
            var sCAID = splitPaymentCheckUserRequest.sCAID;

            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/SplitPaymentAddUser/{runId}/{toCPR}/{toMobileNo}][{CurrentUser.Username}] Entered");
            var Response = new ResponseViewModel();

            int CAID = -1;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (!int.TryParse(sCAID.Decrypt(General.CAIDEncryptPurpose), out CAID))
                {
                    CAID = -1;
                }
            }

            if (toMobileNo == CurrentUser.Mobile || toCPR == CurrentUser.Username)
            {
                Logger.LogDebug($"[Request/SplitPaymentAddUser/{runId}/{toMobileNo}][{CurrentUser.Username}] mobile no. given same as user mobile number.");
                return Ok(BaseResponse<SplitPaymentCheckUserResponse>.Success(
                    new SplitPaymentCheckUserResponse()
                    {
                        Status = NotificationType.ERROR,
                        Message = "",
                        User = new
                        {
                            Id = Misc.Encrypt(CurrentUser.ContactID + "", "CONTACT_ID"),
                            Name = CurrentUser.Name,
                            Mobile = CurrentUser.Mobile,
                        }
                    }));
            }

            var column = "Cont_CPR";
            var input = "";
            if (!string.IsNullOrWhiteSpace(toCPR))
            {
                column = "Cont_CPR";
                input = toCPR;
            }
            else if (!string.IsNullOrWhiteSpace(toMobileNo))
            {
                column = "ContactMobile";
                input = toMobileNo;
            }
            else
            {
                Logger.LogDebug($"[Request/SplitPaymentAddUser/{runId}][{CurrentUser.Username}] CPR no. or Mobile No. is invalid");
                return Ok(BaseResponse<SplitPaymentCheckUserResponse>.Success(
                    new SplitPaymentCheckUserResponse()
                    {
                        Status = NotificationType.ERROR,
                        Message = "The user doesn't exists.",
                        User = null
                    }));
            }

            #region Check if is registred or not
            var RequestFromUser =
                (await DapperHelper.QueryAsync<ContactTbl>(
                    $"SELECT * FROM dbo.ContactTbl WHERE {column} = @input AND Active = 1  AND ISNULL(MobileLogin,0)=1 ",
                    new
                    {
                        input = input
                    }
            )).FirstOrDefault();

            var isRegistered = RequestFromUser != null;
            if (!isRegistered)
            {
                Logger.LogDebug($"[Request/SplitPaymentAddUser/{runId}][{CurrentUser.Username}] mobile no. given does not match any account.");
                return Ok(BaseResponse<SplitPaymentCheckUserResponse>.Success(
                    new SplitPaymentCheckUserResponse()
                    {
                        Status = NotificationType.ERROR,
                        Message = "The user doesn't exists.",
                        User = null
                    }));
            }
            #endregion

            return Ok(BaseResponse<SplitPaymentCheckUserResponse>.Success(
                    new SplitPaymentCheckUserResponse()
                    {
                        Status = NotificationType.SUCCESS,
                        Message = "",
                        User = new
                        {
                            Id = Misc.Encrypt(CurrentUser.ContactID + "", "CONTACT_ID"),
                            Name = RequestFromUser.ContactEng,
                            Mobile = RequestFromUser.ContactMobile,
                        }
                    }));
        }

        [HttpPost("SplitPayment")]
        public async Task<IActionResult> SplitPayment(string sCAID, SplitPaymentRequest request)
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{sCAID}][{CurrentUser.Username}] Entered: {JsonConvert.SerializeObject(request)}");

            var response = new ResponseViewModel
            {
                Status = NotificationType.ERROR,
                Message = "ErrorOccurred"
            };

            try
            {
                Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{sCAID}][{CurrentUser.Username}] Decrypting CAID...");
                int CAID = -1;
                if (!string.IsNullOrWhiteSpace(sCAID))
                {
                    if (!int.TryParse(sCAID.Decrypt(General.CAIDEncryptPurpose), out CAID))
                    {
                        Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{sCAID}][{CurrentUser.Username}] CAID is invalid");
                        response.Message = "ErrorDataSubmitted";
                        return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                    }
                }
                Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}] Decrypted CAID: {CAID}");

                var walletAccountTask = await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, CAID);
                var walletAccount = walletAccountTask.FirstOrDefault();
                if (walletAccount == null)
                {
                    Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}] Couldn't find the wallet account");
                    response.Message = "ErrorDataSubmitted";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                if (!ModelState.IsValid || request.BillRecipient.Count() <= 1 || request.BillAmount != request.BillRecipient.Sum(r => r.Amount))
                {
                    Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}] Submitted data is not valid");
                    response.Message = "ErrorDataSubmitted";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}] Building the header query");
                var query = $@"
                    DECLARE @HeaderId INT;
                    DECLARE @DetailId INT;
                    INSERT INTO dbo.Wallet_SplitPaymentHeader (ContactId, Title, Amount, Notes, Scan, Active, DateCreated, CAID)
                    VALUES (@ContactId, @Title, @Amount, @Notes, @Scan, 1, GETDATE(), {CAID})

                    SET @HeaderId = SCOPE_IDENTITY();
                ";
                Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}] Built the header query, adding the header parameters");
                var queryParams = new ExpandoObject() as IDictionary<string, object>;
                queryParams.Add("@ContactId", CurrentUser.ContactID);
                queryParams.Add("@Title", request.BillName);
                queryParams.Add("@Amount", request.BillAmount);
                queryParams.Add("@Notes", request.BillNotes);
                queryParams.Add("@Scan", null);
                Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}] Added the header parameters");

                Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}] Processing the recipients details");
                var count = 0;
                foreach (var recipient in request.BillRecipient)
                {
                    Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}][{recipient.Mobile}] Decrypting the recipient id");
                    var contactId = recipient.Id.Decrypt("CONTACT_ID");
                    var isBillOwner = contactId == CurrentUser.ContactID + "";
                    Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}][{recipient.Mobile}] Decrypted the recipient id: {contactId}");

                    Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}][{recipient.Mobile}] Building the details query");
                    query += $@"
                        INSERT INTO dbo.Wallet_SplitPaymentDetails (HeaderId, ContactId, Amount, Paid, Active, DatePaid)
                        VALUES (@HeaderId, @RecipientContactId{count}, @RecipientAmount{count}, {(isBillOwner ? 1 : 0)}, 1, {(isBillOwner ? "GETDATE()" : "NULL")})
                        
                        SET @DetailId = SCOPE_IDENTITY();
                        {(isBillOwner ? "" : $"EXEC dbo.usp_SendReceiptGeneralProcessRequestMoneyNotificationWallet @ToCAID = {CAID}, @ToAmount = @RecipientAmountFormatted{count}, @FormID = 500072, @ReferenceNumber = @DetailId, @RequesteeContactId = @RecipientContactId{count}")}                        
                    ";
                    Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}][{recipient.Mobile}] Built the details query, adding the details parameters");
                    queryParams.Add($"@RecipientContactId{count}", contactId);
                    queryParams.Add($"@RecipientAmount{count}", recipient.Amount);
                    queryParams.Add($"@RecipientAmountFormatted{count++}", recipient.Amount.FormatAmount(walletAccount.NoOfDecimals, walletAccount.SwiftCode));
                    Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}][{recipient.Mobile}] Added the details parameters");
                }

                // Save the changes and commit to the db
                Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}] Saving the data to the db");
                var isSuccess = DapperHelper.Exceute(query, queryParams) > 0;
                Logger.LogDebug($"[Request/SplitPayment-POST/{runId}/{CAID}][{CurrentUser.Username}] Saved the data to the db: {isSuccess}");

                if (isSuccess)
                {
                    response.Status = NotificationType.SUCCESS;
                    response.Message = "TransactionProcessedSuccess";
                    // this.AddNotification(response.Message, response.Status);
                }
                else
                {
                    response.Status = NotificationType.ERROR;
                    response.Message = "Failed to process your transaction. Please try again later";
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"[Request/SplitPayment-POST/{runId}/{sCAID}][{CurrentUser.Username}] Error:", ex);
                response.Status = NotificationType.ERROR;
                response.Message = "InternalError";
                return Ok(BaseResponse<ResponseViewModel>.Failure(response));
            }

            return Ok(BaseResponse<ResponseViewModel>.Success(response));
        }

        [HttpGet("BeneficiaryList")]
        public async Task<IActionResult> BeneficiaryList()
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/BeneficiaryList/{runId}][{CurrentUser.Username}] Entered");

            var beneficaries = await DapperHelper.QueryAsync<MoneyTransferBeneficiary>($@"
                SELECT Id AS Id, BN.BeneficiaryName AS Name, BK.BankName AS BankName, NULL AS BankAccount, BN.BeneficiarySWIFTCode AS BankSwiftCode, BN.BeneficiaryIBAN AS BankIBAN
	            FROM dbo.Wallet_BeneficiaryTbl BN
	            INNER JOIN dbo.Banks BK ON BK.BankID = BN.BeneficiaryBankId
                WHERE BN.ContactId = @ContactId
            ", new
            {
                ContactId = CurrentUser.ContactID
            });

            return Ok(BaseResponse<List<MoneyTransferBeneficiary>>.Success(beneficaries.ToList()));
        }

        [HttpPost("BeneficiaryAdd")]
        public async Task<IActionResult> BeneficiaryAdd([FromBody] MoneyTransferBeneficiary request)
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/BeneficiaryAdd-POST/{runId}][{CurrentUser.Username}] Entered: {JsonConvert.SerializeObject(request)}");

            var response = new ResponseViewModel
            {
                Status = NotificationType.ERROR,
                Message = "ErrorOccurred"
            };

            try
            {
                if (!ModelState.IsValid)
                {
                    Logger.LogDebug($"[Request/BeneficiaryAdd-POST/{runId}][{CurrentUser.Username}] Submitted data is not valid");
                    response.Message = "ErrorDataSubmitted";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                // Save the changes and commit to the db
                Logger.LogDebug($"[Request/BeneficiaryAdd-POST/{runId}][{CurrentUser.Username}] Inserting the data to the db");
                var isSuccess = await DapperHelper.ExceuteAsync($@"
                    INSERT INTO dbo.Wallet_BeneficiaryTbl (ContactId, BeneficiaryIBAN, BeneficiaryName, BeneficiaryBankId, BeneficiarySWIFTCode, Active, DateCreated, UserCreated)
                    SELECT @ContactId, @IBAN, @Name, @BankId, @BankSWIFT, 1, GETDATE(), @UserCreated
                ", new
                {
                    ContactId = CurrentUser.ContactID,
                    IBAN = request.BankIBAN.ToUpper(),
                    Name = request.Name,
                    BankId = 5,
                    BankSWIFT = "AUBBBHBM",
                    UserCreated = CurrentUser.Username
                }) > 0;
                Logger.LogDebug($"[Request/BeneficiaryAdd-POST/{runId}][{CurrentUser.Username}] Inserted the data to the db: {isSuccess}");

                if (isSuccess)
                {
                    response.Status = NotificationType.SUCCESS;
                    response.Message = "TransactionProcessedSuccess";
                }
                else
                {
                    response.Status = NotificationType.ERROR;
                    response.Message = "Failed to process your transaction. Please try again later";
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"[Request/BeneficiaryAdd-POST/{runId}][{CurrentUser.Username}] Error:", ex);
                response.Status = NotificationType.ERROR;
                response.Message = "InternalError";
            }

            return Ok(BaseResponse<ResponseViewModel>.Success(response));
        }

        [HttpPost("BeneficiaryEdit")]
        public async Task<IActionResult> BeneficiaryEdit([FromBody] MoneyTransferBeneficiary request, string requestType)
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/BeneficiaryEdit-POST/{runId}/{requestType}][{CurrentUser.Username}] Entered: {JsonConvert.SerializeObject(request)}");

            var response = new ResponseViewModel
            {
                Status = NotificationType.ERROR,
                Message = "ErrorOccurred"
            };

            try
            {
                Logger.LogDebug($"[Request/BeneficiaryEdit-POST/{runId}/{requestType}][{CurrentUser.Username}] Decrypting beneficiary Id...");
                var sId = request.Id.ToString();
                var beneficiaryId = -1;
                if (!string.IsNullOrWhiteSpace(sId))
                {
                    if (!int.TryParse(sId.Decrypt(General.BeneficiaryEncryptPurpose), out beneficiaryId))
                    {
                        Logger.LogDebug($"[Request/BeneficiaryEdit-POST/{runId}/{requestType}][{CurrentUser.Username}] Beneficiary Id is invalid");
                    }
                }
                request.Id = beneficiaryId;
                Logger.LogDebug($"[Request/BeneficiaryEdit-POST/{runId}/{requestType}][{CurrentUser.Username}] Decrypted beneficiary Id: {beneficiaryId}");

                if (!ModelState.IsValid || request.Id <= 0)
                {
                    Logger.LogDebug($"[Request/BeneficiaryEdit-POST/{runId}/{requestType}][{CurrentUser.Username}] Submitted data is not valid");
                    response.Message = "ErrorDataSubmitted";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                // Save the changes and commit to the db
                Logger.LogDebug($"[Request/BeneficiaryEdit-POST/{runId}/{requestType}][{CurrentUser.Username}] Saving the data to the db");
                var isSuccess = await DapperHelper.ExceuteAsync(requestType.ToUpper() == "DELETE" ? $@"
                    DELETE FROM dbo.Wallet_BeneficiaryTbl
                    WHERE Id = @Id AND ContactId = @ContactId
                " : $@"
                    UPDATE dbo.Wallet_BeneficiaryTbl
                    SET BeneficiaryIBAN = @IBAN, BeneficiaryName = @Name, UserModified = @UserModified, DateModified = GETDATE()
                    WHERE Id = @Id AND ContactId = @ContactId                    
                ", new
                {
                    Id = request.Id,
                    ContactId = CurrentUser.ContactID,
                    IBAN = request.BankIBAN.ToUpper(),
                    Name = request.Name,
                    UserModified = CurrentUser.Username
                }) > 0;
                Logger.LogDebug($"[Request/BeneficiaryEdit-POST/{runId}/{requestType}][{CurrentUser.Username}] Saved the data to the db: {isSuccess}");

                if (isSuccess)
                {
                    response.Status = NotificationType.SUCCESS;
                    response.Message = "TransactionProcessedSuccess";
                }
                else
                {
                    response.Status = NotificationType.ERROR;
                    response.Message = "Failed to process your transaction. Please try again later";
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"[Request/BeneficiaryEdit-POST/{runId}/{requestType}][{CurrentUser.Username}] Error:", ex);
                response.Status = NotificationType.ERROR;
                response.Message = "InternalError";
            }

            return Ok(BaseResponse<ResponseViewModel>.Success(response));
        }

        [HttpPost("SetScannerResult")]
        public IActionResult SetScannerResult(string content, string format)
        {
            Logger.LogDebug($"[SetScannerResult-POST/{content}/{format}][{CurrentUser.Username}] Entered");

            try
            {
                // Make sure that we have proper data
                if (string.IsNullOrWhiteSpace(content) || string.IsNullOrWhiteSpace(format))
                {
                    Logger.LogError($"[SetScannerResult-POST/{content}/{format}][{CurrentUser.Username}] content or format are invalid");
                    return Ok(BaseResponse<bool>.Failure());
                }
                else
                {
                    var merchantPayload = MerchantPayload.FromQR(content);
                    Logger.LogDebug($"[SetScannerResult-POST/{content}/{format}][{CurrentUser.Username}] MobileNumber:{merchantPayload.AdditionalData.MobileNumber}|Transaction Amount:{merchantPayload.TransactionAmount}|BankName  ");
                    if (!string.IsNullOrWhiteSpace(merchantPayload.AdditionalData.MobileNumber) || !string.IsNullOrWhiteSpace(merchantPayload.AdditionalData.AdditionalConsumerDataRequest))
                    {
                        return Ok(new MerchantData()
                        {
                            MobileNumber = merchantPayload.AdditionalData.MobileNumber,
                            CPR = merchantPayload.AdditionalData.AdditionalConsumerDataRequest,
                            Amount = merchantPayload.TransactionAmount,
                            BankName = merchantPayload.MerchantName,
                            //For ATM Withdrawal
                            Date = DateTime.Now.ToString("ddMMyy"),
                            Time = DateTime.Now.ToString("HHmmss"),
                            TransactionAmount = merchantPayload.TransactionAmount + "",
                            BillNumber = merchantPayload.AdditionalData.BillNumber + "",
                            StoreLabel = merchantPayload.AdditionalData.StoreLabel + "",
                            CustomerLabel = merchantPayload.AdditionalData.CustomerLabel + "",
                            TerminalLabel = merchantPayload.AdditionalData.TerminalLabel + "",
                            TransactionType = (merchantPayload.AdditionalData.PurposeOfTransaction + "").ToLower() == "purchase" ? "00" : "01",
                            Currency = Wallets.GetWalletCurrency(merchantPayload.TransactionCurrency + "")
                        });
                    }

                }
            }
            catch (Exception ex)
            {
                // Log the error message
                Logger.LogError($"[SetScannerResult-POST][{CurrentUser.Username}] An internal error has occurred. Please try again later.", ex);
                return Ok(BaseResponse<bool>.Failure());
            }
            return Ok(BaseResponse<bool>.Failure());
        }

        [HttpGet("MerchantWithdraw")]
        public async Task<IActionResult> MerchantWithdraw(string sCAID = "")
        {
            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/MerchantWithdraw/{runId}/{sCAID}][{CurrentUser.Username}] Entered");

            Logger.LogDebug($"[Request/MerchantWithdraw/{runId}/{sCAID}][{CurrentUser.Username}] Decrypting CAID...");
            int CAID = CurrentUser.CAID;
            if (!string.IsNullOrWhiteSpace(sCAID))
            {
                if (!int.TryParse(sCAID.Decrypt(General.CAIDEncryptPurpose), out CAID))
                {
                    Logger.LogDebug($"[Request/MerchantWithdraw/{runId}/{sCAID}][{CurrentUser.Username}] CAID is invalid, using default CAID");
                    CAID = CurrentUser.CAID;
                }
            }
            Logger.LogDebug($"[Request/SplitPayment/{runId}/{CAID}][{CurrentUser.Username}] Decrypted CAID: {CAID}");
            var response = new ResponseViewModel
            {
                Status = NotificationType.ERROR,
                Message = "ErrorOccurred"
            };
            var walletAccount = (await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, CAID, 2)).FirstOrDefault();
            if (walletAccount == null)
            {
                Logger.LogDebug($"[Request/MerchantWithdraw/{runId}/{CAID}][{CurrentUser.Username}] Couldn't find the wallet account");
                response.Status = "Account Not Found";
                response.Message = "Failed to find your merchant account. Please try again later.";
                return Ok(BaseResponse<ResponseViewModel>.Failure(response));
            }

            var data = await DapperHelper.QueryAsync<MerchantPaymentRequest>($@"
                SELECT CF.CAID AS MerchantCAID, CF.CustomerRecID AS MerchantId, CF.BankName AS MerchantName, 
                CFPD.BankAccount AS BankAccount, CFPD.SwiftCode AS BankSwiftCode, CFPD.IBAN AS BankIBAN, BNK.BankName AS BankName
                FROM dbo.CustomerFilePaymentDetailsTbl CFPD
                INNER JOIN dbo.vw_CustomerFile CF ON CF.CAID = CFPD.CAID
                INNER JOIN dbo.Banks BNK ON BNK.BankID = CFPD.BankID
                WHERE CF.CAID = @CAID
            ", new
            {
                CAID = CAID
            }, "Payable");
            var paymentData = data.FirstOrDefault();
            if (paymentData == null)
            {
                Logger.LogDebug($"[Request/MerchantWithdraw/{runId}/{CAID}][{CurrentUser.Username}] Couldn't find merchant payment details");
                response.Status = "Payment Details Not Available";
                response.Message = "Failed to find your merchant payment details. Please try again later or contact us for assistance.";
                return Ok(BaseResponse<ResponseViewModel>.Failure(response));
            }

            paymentData.MerchantAccount = walletAccount;

            return Ok(BaseResponse<MerchantPaymentRequest>.Success(paymentData));
        }

        [HttpPost("MerchantWithdraw")]
        public async Task<IActionResult> MerchantWithdraw([FromBody] MerchantWithdrawRequest merchantWithdrawRequest)
        {
            var sCAID = merchantWithdrawRequest.sCAID;
            var amount = merchantWithdrawRequest.amount;

            var runId = GeneralHelper.GetRandomString(8);
            Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{sCAID}][{CurrentUser.Username}] Entered: {amount}");

            var response = new ResponseViewModel
            {
                Status = NotificationType.ERROR,
                Message = "ErrorOccurred"
            };

            try
            {
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{sCAID}][{CurrentUser.Username}] Decrypting CAID...");
                int CAID = -1;
                if (!string.IsNullOrWhiteSpace(sCAID))
                {
                    if (!int.TryParse(sCAID.Decrypt(General.CAIDEncryptPurpose), out CAID))
                    {
                        Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{sCAID}][{CurrentUser.Username}] CAID is invalid");
                        response.Message = "ErrorDataSubmitted";
                        return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                    }
                }
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Decrypted CAID: {CAID}");

                if (CAID <= 0 || amount <= 0)
                {
                    Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Submitted data is not valid");
                    response.Message = "ErrorDataSubmitted";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                var walletAccount = (await _cardRepository.GetWalletAccounts(CurrentUser.ContactID, CAID, 2)).FirstOrDefault();
                if (walletAccount == null)
                {
                    Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Couldn't find the wallet account");
                    response.Message = "ErrorDataSubmitted";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                var paymentData = DapperHelper.Query<MerchantPaymentRequest>($@"
                    SELECT CF.CAID AS MerchantCAID, CF.CustomerRecID AS MerchantId, CF.BankName AS MerchantName, 
                    CFPD.BankAccount AS BankAccount, CFPD.SwiftCode AS BankSwiftCode, CFPD.IBAN AS BankIBAN, BNK.BankName AS BankName
                    FROM dbo.CustomerFilePaymentDetailsTbl CFPD
                    INNER JOIN dbo.vw_CustomerFile CF ON CF.CAID = CFPD.CAID
                    INNER JOIN dbo.Banks BNK ON BNK.BankID = CFPD.BankID
                    WHERE CF.CAID = @CAID
                ", new
                {
                    CAID = CAID
                }, "Payable").FirstOrDefault();

                if (paymentData == null)
                {
                    Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Couldn't find merchant payment details");
                    response.Message = "ErrorDataSubmitted";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                var customerBalance = await _walletRepository.GetCustomerWalletBalance(CAID, CurrentUser.ContactID);
                if (amount > customerBalance)
                {
                    Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] insufficient balance!");
                    response.Message = "InsufficientWalletBalance";
                    response.Message = "ErrorDataSubmitted";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Getting main account CAID...");
                var toCAID = -1;
                int.TryParse(WalletApplication.GetEnvVariable("MAIN-CAID"), out toCAID);
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Got main account CAID: {toCAID}");

                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Creating a receipt...");
                var result = await DapperHelper.ExecuteScalarAsync<string>(
                    $" EXEC dbo.usp_CreateGeneralReceiptWalletTransfer @UserName = N'{CurrentUser.Username}', @FromCAID = {CAID}, @FromAmount = {amount}, @ToCAID = {toCAID}, @BatchNo = 0, @FormID = {PageTypes.WALLET_MERCHANT_WITHDRAW_MONEY}, @CSID = -1, @Type = 1, @ToAmount = {amount}, @ExchangeRate = {1};"
                );
                int ReceiptId = 0;
                int.TryParse(result, out ReceiptId);
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Created a receipt: {result}");

                if (ReceiptId <= 0)
                {
                    Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Failed to create a receipt");
                    response.Message = "InsufficientWalletBalance";
                    response.Message = "ErrorDataSubmitted";
                    return Ok(BaseResponse<ResponseViewModel>.Failure(response));
                }

                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Creating a payment file...");
                var fileName = (Config.GetStringValue("AUBBAHPaymentFileName") ?? "MALLAT_PAY_@DATE@_@TIME@").Replace("@DATE@", DateTime.Now.ToString("ddMMyyyy")).Replace("@TIME@", DateTime.Now.ToString("HHmmss"));
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Creating a payment file, fileName: {fileName}");
                var filePath = Config.GetStringValue("AUBBAHPaymentFolder") + WalletApplication.BranchId + "\\Payments\\";
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Creating a payment file, filePath: {filePath}");
                if (!Directory.Exists(filePath))
                {
                    Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Directory doesn't exist, creating a new one...");
                    Directory.CreateDirectory(filePath);
                    Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Directory doesn't exist, created a new one");
                }

                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Creating the payment text file...");
                FileInfo t = new FileInfo($"{filePath}{fileName}.txt");
                StreamWriter Tex = t.CreateText();

                // Header Line
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Creating the payment text file header...");
                var receiptRef = ReceiptId + "";
                var crNumber = Config.GetStringValue("AUBBAHPaymentCompanyCR") ?? "44562-3";
                var debitAccountNumber = Config.GetStringValue("AUBBAHPaymentDebitAccountNumber") ?? "*************";
                var debitDescription = $"MERCHANT PAYMENT RECEIPT #{receiptRef}";
                var headerLine = $"S1,{crNumber},{debitAccountNumber},MXD,1,{debitDescription},{DateTime.Now.ToString("dd/MM/yyyy")},{(Config.GetStringValue("AUBBAHPaymentBatchReference") ?? "MALLAT_PAY_@REF@").Replace("@REF@", receiptRef)}";
                Tex.WriteLine(headerLine);
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Created the payment text file header: {headerLine}");

                // Details Line
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Creating the payment text file details...");
                var transactionType = paymentData.BankSwiftCode.Equals("AUBBBHBM", StringComparison.InvariantCultureIgnoreCase) ? "TRF" : "LCL";
                var creditAmount = amount.FormatAmount(walletAccount.NoOfDecimals, "", false, "").Replace(",", "");
                var exchangeRate = "";
                var creditDescription = $"WALLET TRANSFER RECEIPT #{receiptRef}";
                var detailsLine = $"S2,{transactionType},{creditAmount},{walletAccount.SwiftCode},{exchangeRate},,,,{paymentData.BankIBAN},{receiptRef},,,{creditDescription},{creditDescription},,,,{paymentData.MerchantName},,,{paymentData.BankName},,,,{paymentData.BankSwiftCode},,,,,,,,,,";
                Tex.WriteLine(detailsLine);
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Created the payment text file details: {detailsLine}");

                // Trailer Line
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Creating the payment text file trailer...");
                var trailerLine = $"S3,1";
                Tex.WriteLine(trailerLine);
                Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Created the payment text file trailer: {trailerLine}");

                Tex.Flush();
                Tex.Close();

                if (Config.GetStringValue("AUBBAHPaymentFolderBatch") != null)
                {
                    try
                    {
                        var f = Config.GetStringValue("AUBBAHPaymentFolderBatch").ToString().Replace("@branch@", WalletApplication.BranchId);
                        Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Invoking the batch file: {f}");
                        if (System.IO.File.Exists(f))
                        {
                            Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Starting the process...");
                            //Process a = new Process();
                            //a.StartInfo.FileName = f;
                            //a.StartInfo.CreateNoWindow = true;
                            //a.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                            //var isStarted = a.Start();
                            //a.Dispose();
                            //Logger.LogDebug($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Started the process: {isStarted}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError($"[Request/MerchantWithdraw-POST/{runId}/{CAID}][{CurrentUser.Username}] Failed to run the batch file.", ex);
                    }
                }
                response.Status = NotificationType.SUCCESS;
                response.Message = "TransactionProcessedSuccess";
            }
            catch (Exception ex)
            {
                Logger.LogError($"[Request/MerchantWithdraw-POST/{runId}/{sCAID}][{CurrentUser.Username}] Error:", ex);
                response.Status = NotificationType.ERROR;
                response.Message ="InternalError";
            }

            return Ok(BaseResponse<ResponseViewModel>.Success(response));
        }
    }
}
